export interface BulkPdfRedactionDataModel {
  keywords: string[]

  documentTempTable?: string
}

export interface BulkRedactionGridItem {
  term: string
  hits: string
  numOfDoc: number
  action: boolean
}

export interface BulkRedactionJobDetailRequestModel {
  userIds: number[]
  startDate?: Date
  endDate?: Date
  pageNumber: number
  pageSize: number
  statuses: string[]
}

export interface BulkRedactionJobDetailModel {
  jobId: number
  userId: number
  redactedBy: string
  redactedOn: Date
  term: string
  hits: string
  documentCount: number
  failedRedactionCount: number
  statusName: string
}

export interface BulkRedactionJobDetailResponseModel {
  bulkRedactionJobDetailModel: BulkRedactionJobDetailModel[]
  totalHitCount: number
}

export interface BulkRedactionDetailModel {
  fileId: number
  terms: string
  status: string
  details: string
}
