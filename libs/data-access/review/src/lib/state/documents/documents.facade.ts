import { Injectable } from '@angular/core'
import { select, Store } from '@ngrx/store'
import * as DocumentsActions from './documents.actions'
import * as DocumentSelectors from './documents.selectors'
import { DocumentsService } from '../../services'
import { Observable } from 'rxjs'
import { DocumentsState } from './documents.reducer'
import {
  DocumentMenuType,
  PageControlActionType,
} from '@venio/shared/models/constants'
import {
  DocumentViewerLogModel,
  ImageTypeExportDetail,
  PDFLoadingArgs,
  Viewer,
} from '../../models/interfaces'
import { TiffViewerPayload } from '../../models/interfaces/tiff-viewer/tiff-viewer.model'

@Injectable()
export class DocumentsFacade {
  constructor(
    private readonly store: Store,
    private documentService: DocumentsService
  ) {}

  public set loadViewerContainer(fileId: number) {
    this.documentService.loadViewerContainer$.next(fileId)
  }

  public get onLoadViewerContainer(): Observable<number> {
    return this.documentService.loadViewerContainer$
  }

  public set viewerContainerLoaded(isLoaded: boolean) {
    this.documentService.viewerContainerLoaded$.next(isLoaded)
  }

  public get onViewerContainerLoaded(): Observable<boolean> {
    return this.documentService.viewerContainerLoaded$
  }

  public set viewerComponentReady(isViewerLoaded: Viewer) {
    this.documentService.viewerComponentReady$.next(isViewerLoaded)
  }

  public get onViewerComponentReady(): Observable<Viewer> {
    return this.documentService.viewerComponentReady$
  }

  public set loadPDF(args: PDFLoadingArgs) {
    this.documentService.loadPDF$.next(args)
  }

  public get onLoadPdf(): Observable<PDFLoadingArgs> {
    return this.documentService.loadPDF$
  }

  public set loadProducedPDF(args: PDFLoadingArgs) {
    this.documentService.loadProducedPDF$.next(args)
  }

  public get onLoadProducedPdf(): Observable<PDFLoadingArgs> {
    return this.documentService.loadProducedPDF$
  }

  public set loadExportDetails(imageTypeExportDetail: ImageTypeExportDetail) {
    this.documentService.loadExportDetails$.next(imageTypeExportDetail)
  }

  public get onLoadExportDetails(): Observable<ImageTypeExportDetail> {
    return this.documentService.loadExportDetails$
  }

  public set loadNearNative(fileId: number) {
    this.documentService.loadNearNative$.next(fileId)
  }

  public get onLoadNearNative(): Observable<number> {
    return this.documentService.loadNearNative$
  }

  public set loadFulltextViewer(fileId: number) {
    this.documentService.loadFulltextViewer$.next(fileId)
  }

  public get onLoadFulltextViewer(): Observable<number> {
    return this.documentService.loadFulltextViewer$
  }

  public set loadTiffViewer(payload: TiffViewerPayload) {
    this.documentService.loadTiffViewer$.next(payload)
  }

  public get onLoadTiffViewer(): Observable<TiffViewerPayload> {
    return this.documentService.loadTiffViewer$
  }

  public set setDocumentNavigation(action: PageControlActionType) {
    this.documentService.documentNavigation$.next(action)
  }

  public get getDocumentNavigation(): Observable<PageControlActionType> {
    return this.documentService.documentNavigation$
  }

  public getCurrentDocument$ = this.store.pipe(
    select(DocumentSelectors.getStateOfDocumentsState('currentDocument'))
  )

  public getSelectedDocuments$ = this.store.pipe(
    select(DocumentSelectors.getStateOfDocumentsState('selectedDocuments'))
  )

  public getIsBatchSelected$ = this.store.pipe(
    select(DocumentSelectors.getStateOfDocumentsState('isBatchSelected'))
  )

  public getUnselectedDocuments$ = this.store.pipe(
    select(DocumentSelectors.getStateOfDocumentsState('unselectedDocuments'))
  )

  public getCurrentDocumentTablePage$ = this.store.pipe(
    select(
      DocumentSelectors.getStateOfDocumentsState('currentDocumentTablePage')
    )
  )

  public isBulkDocument$ = this.store.pipe(
    select(DocumentSelectors.getStateOfDocumentsState('isBulkDocument'))
  )

  public getCurrentFileName$ = this.store.pipe(
    select(DocumentSelectors.getStateOfDocumentsState('currentFileName'))
  )

  public getTagSummary$ = this.store.pipe(
    select(DocumentSelectors.getStateOfDocumentsState('tagSummary'))
  )

  public getTagHistory$ = this.store.pipe(
    select(DocumentSelectors.getStateOfDocumentsState('tagHistory'))
  )

  public getDocumentTableUpdatingData$ = this.store.pipe(
    select(
      DocumentSelectors.getStateOfDocumentsState('documentTableUpdatingData')
    )
  )

  /**
   * Notifies a menu event has been triggered.
   * @see DocumentMenuType
   * @see DocumentMenuComponent
   */
  public selectDocumentMenuEvent$ = this.store.pipe(
    select(DocumentSelectors.getStateOfDocumentsState('menuEventPayload'))
  )

  /**
   * When a document is trigger, the state set truthy value.
   * Whatever is loaded after this action, from there it has been set to falsy.
   */
  public isDocumentMenuLoading$ = this.store.pipe(
    select(DocumentSelectors.getStateOfDocumentsState('isDocumentMenuLoading'))
  )

  public selectDocumentPayloadData = this.store.pipe(
    select(DocumentSelectors.selectDocumentPayloadData)
  )

  public resetDocumentState(
    stateKey?: keyof DocumentsState | Array<keyof DocumentsState>
  ): void {
    this.store.dispatch(DocumentsActions.resetDocumentState({ stateKey }))
  }

  public setCurrentDocument(documentId: number): void {
    this.store.dispatch(
      DocumentsActions.setCurrentDocument({ payload: { documentId } })
    )
  }

  public navigateDocumentByFileId(
    currentDocumentId: number,
    documentIds: number[],
    selectedDocumentIds: number[],
    resetSelection: boolean
  ): void {
    this.store.dispatch(
      DocumentsActions.navigateDocumentByFileId({
        payload: {
          currentDocumentId,
          documentIds,
          selectedDocumentIds,
          resetSelection,
        },
      })
    )
  }

  public setCurrentDocumentDetails(
    currentDocument: number,
    currentFileName: string
  ): void {
    this.store.dispatch(
      DocumentsActions.setCurrentDocumentDetails({
        payload: { currentDocument, currentFileName },
      })
    )
  }

  public setSelectedDocuments(documentIds: number[]): void {
    this.store.dispatch(
      DocumentsActions.setSelectedDocuments({
        payload: { selectedDocuments: documentIds },
      })
    )
  }

  public addToSelectedDocuments(documentIds: number[]): void {
    this.store.dispatch(
      DocumentsActions.addToSelectedDocuments({ payload: { documentIds } })
    )
  }

  public removeFromSelectedDocuments(documentIds: number[]): void {
    this.store.dispatch(
      DocumentsActions.removeFromSelectedDocuments({ payload: { documentIds } })
    )
  }

  public setUnSelectedDocuments(unselectedDocuments: number[]): void {
    this.store.dispatch(
      DocumentsActions.setUnSelectedDocuments({
        payload: { unselectedDocuments },
      })
    )
  }

  public setIsBatchSelection(isBatchSelected: boolean): void {
    this.store.dispatch(
      DocumentsActions.setIsBatchSelection({
        payload: { isBatchSelection: isBatchSelected },
      })
    )
  }

  public moveToFirstDocument(): void {
    this.store.dispatch(DocumentsActions.moveToFirstDocument())
  }

  public moveToLastDocument(): void {
    this.store.dispatch(DocumentsActions.moveToLastDocument())
  }

  public moveToNextDocument(
    documentIds: number[],
    currentDocumentId: number
  ): void {
    this.store.dispatch(
      DocumentsActions.moveToNextDocument({
        payload: { documentIds, currentDocumentId },
      })
    )
  }

  public moveToPreviousDocument(
    documentIds: number[],
    currentDocumentId: number
  ): void {
    this.store.dispatch(
      DocumentsActions.moveToPreviousDocument({
        payload: { documentIds, currentDocumentId },
      })
    )
  }

  public navigateDocumentByNumber(seqNo: number): void {
    this.store.dispatch(
      DocumentsActions.navigateDocumentByNumber({ payload: { seqNo } })
    )
  }

  public navigateEmailThreadByNumber(seqNo: number): void {
    this.store.dispatch(
      DocumentsActions.navigateEmailThreadByNumber({ payload: { seqNo } })
    )
  }

  public triggerMenuEvent(menuEventPayload: DocumentMenuType): void {
    this.store.dispatch(
      DocumentsActions.documentMenuEvent({ payload: { menuEventPayload } })
    )
  }

  public setCurrentDocumentTablePageNumber(
    pageNumber: number,
    resetSelectionItem?: string
  ): void {
    this.store.dispatch(
      DocumentsActions.setCurrentDocumentTablePageNumber({
        payload: {
          pageNumber: pageNumber,
          resetSelectionItem: resetSelectionItem ? resetSelectionItem : '',
        },
      })
    )
  }

  public exportToFile(): void {
    this.store.dispatch(DocumentsActions.downloadCSVReviewDocument())
  }

  public updateIsBulkDocument(isBulkDocument: boolean): void {
    this.store.dispatch(DocumentsActions.isBulkDocument({ isBulkDocument }))
  }

  public fetchTagSummary(): void {
    this.store.dispatch(DocumentsActions.fetchTagSummary())
  }

  public fetchTagHistory(): void {
    this.store.dispatch(DocumentsActions.fetchTagHistory())
  }

  public fetchSelectedFileDocumentTableFolders(
    selectedFileIds: number[]
  ): void {
    this.store.dispatch(
      DocumentsActions.fetchSelectedFileDocumentTableFolders({
        selectedFileIds,
      })
    )
  }

  public saveDocumentViewerLog(
    projectId: number,
    documentViewerLogModel: DocumentViewerLogModel
  ): void {
    this.store.dispatch(
      DocumentsActions.saveDocumentViewerLog({
        projectId,
        documentViewerLogModel,
      })
    )
  }

  public updateCurrentDocumentTablePageNumber(pageNumber: number): void {
    this.store.dispatch(
      DocumentsActions.updateCurrentDocumentTablePageNumber({
        payload: { pageNumber },
      })
    )
  }
}
