import { Injectable } from '@angular/core'
import { select, Store } from '@ngrx/store'
import * as DeleteDocumentActions from './delete-document.actions'
import * as DeleteDocumentSelectors from './delete-document.selectors'
import { DeleteMode } from '../../models/interfaces/delete-document.model'
import { take } from 'rxjs'
import { UuidGenerator } from '@venio/util/uuid'

@Injectable({
  providedIn: 'root',
})
export class DeleteDocumentFacade {
  public getDeleteDocumentSummary$ = this.store.pipe(
    select(
      DeleteDocumentSelectors.getStateOfDeleteDocument('deleteDocumentSummary')
    )
  )

  public getCurrentDeleteOption$ = this.store.pipe(
    select(
      DeleteDocumentSelectors.getStateOfDeleteDocument('currentDeleteOption')
    )
  )

  public getIsFileToBeDeletedInFolder$ = this.store.pipe(
    select(
      DeleteDocumentSelectors.getStateOfDeleteDocument(
        'isFileToBeDeletedInFolder'
      )
    )
  )

  public getIsSummaryLoaded$ = this.store.pipe(
    select(DeleteDocumentSelectors.getStateOfDeleteDocument('isSummaryLoaded'))
  )

  public getDeleteDocumentStatus$ = this.store.pipe(
    select(
      DeleteDocumentSelectors.getStateOfDeleteDocument('deleteDocumentStatus')
    )
  )

  public getIsDeletingStatus$ = this.store.pipe(
    select(DeleteDocumentSelectors.getStateOfDeleteDocument('isDeleting'))
  )

  public getCurrentSessionId$ = this.store.pipe(
    select(DeleteDocumentSelectors.getStateOfDeleteDocument('currentSessionId'))
  )

  public getDeleteMessage$ = this.store.pipe(
    select(DeleteDocumentSelectors.getStateOfDeleteDocument('deleteMessage'))
  )

  public getDeleteFileCount$ = this.store.pipe(
    select(DeleteDocumentSelectors.getStateOfDeleteDocument('deleteFileCount'))
  )

  constructor(private readonly store: Store) {}

  public fetchDeleteDocumentSummary(projectId: number): void {
    this.store.dispatch(
      DeleteDocumentActions.getDeleteDocumentSummary({
        payload: {
          projectId,
          sessionId: this.getRandomSessionId(),
        },
      })
    )
  }

  public checkIfFilesToBeDeletedInFolder(projectId: number): void {
    this.getCurrentDeleteOption$
      .pipe(take(1))
      .subscribe((deleteDocumentOptions) => {
        const sessionId = this.getRandomSessionId()
        this.store.dispatch(
          DeleteDocumentActions.checkIfFilesToBeDeletedInFolder({
            payload: {
              projectId,
              deleteDocumentOptions,
              sessionId,
            },
          })
        )
      })
  }

  public deleteDocument(projectId: number): void {
    //generate session id
    this.store.dispatch(
      DeleteDocumentActions.setSessionId({
        payload: {
          sessionId: this.getRandomSessionId(),
        },
      })
    )

    //retrieve latest delete options and call api to delete document
    this.getCurrentDeleteOption$
      .pipe(take(1))
      .subscribe((deleteDocumentOptions) => {
        this.store.dispatch(
          DeleteDocumentActions.deleteDocument({
            payload: {
              projectId,
              deleteDocumentOptions,
            },
          })
        )
      })
  }

  public fetchDeleteStatus(projectId: number): void {
    this.getCurrentDeleteOption$
      .pipe(take(1))
      .subscribe((deleteDocumentOptions) => {
        const sessionId = deleteDocumentOptions.SessionId
        if (sessionId) {
          this.store.dispatch(
            DeleteDocumentActions.getDeleteDocumentStatus({
              payload: {
                projectId,
                sessionId,
              },
            })
          )
        }
      })
  }

  public setCurrentDeleteOption(
    selectedDeleteOptions: DeleteMode[],
    deleteChildRecords: boolean,
    deleteMediaIfAllFilesAreDeleted: boolean
  ): void {
    this.store.dispatch(
      DeleteDocumentActions.setDeleteOption({
        payload: {
          selectedDeleteOptions,
          deleteChildRecords,
          deleteMediaIfAllFilesAreDeleted,
        },
      })
    )
  }

  public setDeleteFileCount(totalDeleteFileCount: number): void {
    this.store.dispatch(
      DeleteDocumentActions.setDeleteFileCount({
        payload: {
          deleteFileCount: totalDeleteFileCount,
        },
      })
    )
  }

  public setDeletingStatus(isDeleting: boolean): void {
    this.store.dispatch(
      DeleteDocumentActions.setDeletingStatus({
        payload: {
          isDeleting: isDeleting,
        },
      })
    )
  }

  public clearDeleteError(): void {
    this.store.dispatch(
      DeleteDocumentActions.deleteDocumentFailure({
        payload: {
          error: '',
        },
      })
    )
  }

  public resetAllDeleteOptions(): void {
    this.store.dispatch(DeleteDocumentActions.resetAllDeleteOptions())
  }

  private getRandomSessionId(): string {
    return UuidGenerator.uuid.replace(/-/g, '_')
  }
}
