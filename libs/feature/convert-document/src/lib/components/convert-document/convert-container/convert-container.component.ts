import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  On<PERSON><PERSON><PERSON>,
  OnInit,
  ViewChild,
} from '@angular/core'
import { DialogContentBase, DialogRef } from '@progress/kendo-angular-dialog'
import { SelectEvent, TabStripComponent } from '@progress/kendo-angular-layout'
import { Subject, filter, take, takeUntil } from 'rxjs'
import {
  ConvertDocumentFacade,
  ConvertDocumentTab,
  UserRights,
} from '@venio/data-access/review'
import { VenioNotificationService } from '@venio/feature/notification'
import { ConvertDocumentTabModel } from '../../../models/convert-document-tab.model'
import { toSignal } from '@angular/core/rxjs-interop'

@Component({
  selector: 'venio-convert-container',
  templateUrl: './convert-container.component.html',
  styleUrls: ['./convert-container.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ConvertContainerComponent
  extends DialogContentBase
  implements OnInit, AfterViewInit, OnD<PERSON>roy
{
  private readonly toDestroy$ = new Subject<void>()

  public message: string

  public launchVisible = true

  public launchEnabled = true

  public launchChecked = false

  public relaunchVisible = true

  public relaunchEnabled = true

  public relaunchChecked = false

  public convertToHtmlComponent: any = null

  public convertToRtfComponent: any = null

  public convertToImageComponent: any = null

  public ocrImageComponent: any = null

  public ocrRedactedImageComponent: any = null

  public ocrGeneratedImageComponent: any = null

  public createSlipsheetComponent: any = null

  public audioTranscribeComponent: any = null

  public showSpinner = false

  public readonly rights = UserRights

  public isOcrTabSelected = false

  public isAudioTranscribeSelected = false

  @ViewChild('convertDocTabStrip') public tabstrip: TabStripComponent

  // Collection of convert tabs. The tabs appear in the order they are defined here.
  public readonly tabCollection: ConvertDocumentTabModel[] = [
    {
      title: 'To HTML',
      disabled: false,
      componentRef: this.convertToHtmlComponent,
      convertDocumentTab: ConvertDocumentTab.HtmlTab,
      right: UserRights.ALLOW_TO_CONVERT_TO_HTML,
    },
    {
      title: 'To RTF',
      disabled: false,
      componentRef: this.convertToRtfComponent,
      convertDocumentTab: ConvertDocumentTab.RtfTab,
      right: UserRights.ALLOW_TO_CONVERT_TO_HTML,
    },
    {
      title: 'To Image',
      disabled: false,
      componentRef: this.convertToImageComponent,
      convertDocumentTab: ConvertDocumentTab.ImageTab,
      right: UserRights.ALLOW_TO_BULK_TIFF,
    },
    {
      title: 'Image/PDF To OCR',
      disabled: false,
      componentRef: this.ocrImageComponent,
      convertDocumentTab: ConvertDocumentTab.ImageOcr,
      right: UserRights.ALLOW_TO_LAUNCH_OCR,
    },
    {
      title: 'Generated Images To OCR',
      disabled: false,
      componentRef: this.ocrGeneratedImageComponent,
      convertDocumentTab: ConvertDocumentTab.GeneratedImageOcr,
      right: UserRights.ALLOW_TO_LAUNCH_OCRGENERATEDTIFF,
    },
    {
      title: 'Redacted Images To OCR',
      disabled: false,
      componentRef: this.ocrRedactedImageComponent,
      convertDocumentTab: ConvertDocumentTab.RedactedImageOcr,
      right: UserRights.ALLOW_TO_LAUNCH_OCRREDACTEDTIFF,
    },
    {
      title: 'To Slipsheet',
      disabled: false,
      componentRef: this.createSlipsheetComponent,
      convertDocumentTab: ConvertDocumentTab.SlipsheetTab,
      right: UserRights.ALLOW_TO_BULK_TIFF,
    },
    {
      title: 'Audio Transcribe',
      disabled: false,
      componentRef: this.audioTranscribeComponent,
      convertDocumentTab: ConvertDocumentTab.AudioTranscribe,
      right: UserRights.ALLOW_TO_LAUNCH_TRANSCRIBING,
    },
  ]

  // This is needed to keep track of the tabs and its index because kendo tabstrip returns selected index and not the tab itself.
  private tabIndexMap: { [index: number]: ConvertDocumentTabModel } = {}

  public currentTab = toSignal(this.convertDocumentFacade.getCurrentTab$) // current tab selected

  constructor(
    public dialog: DialogRef,
    private cdr: ChangeDetectorRef,
    private convertDocumentFacade: ConvertDocumentFacade,
    private noticationService: VenioNotificationService
  ) {
    super(dialog)
  }

  public ngOnInit(): void {
    this.initSlices()
    this.#disableInaccessibleTabs()
  }

  public ngAfterViewInit(): void {
    // update tab index map after view is initialized
    this.updateTabIndexMap()

    // set initial tab after view is fully initialized
    setTimeout(() => {
      this.setupInitialTab()
      this.cdr.detectChanges()
    }, 10)
  }

  #disableInaccessibleTabs(): void {
    this.tabCollection.forEach((tab) => {
      this.convertDocumentFacade.hasRights(tab.right).subscribe((hasRight) => {
        tab.disabled = !hasRight
      })
    })
  }

  /**
   * Sets first enabled tab as selected.
   * @returns {void}
   */
  private setupInitialTab(): void {
    const enabledTabs = this.getEnabledTabs()

    if (enabledTabs.length > 0) {
      const tabIndex = enabledTabs.findIndex(
        (x) => x.title === this.tabstrip.tabs.first.title
      )
      this.tabstrip.selectTab(0)
      this.setupTab(tabIndex)
    }
  }

  // Updates tabIndexMap with enabled tabs.
  // This needs to be updated anytime a tab is enabled/disabled dynamically.
  private updateTabIndexMap(): void {
    this.getEnabledTabs().forEach((tab, index) => {
      this.tabIndexMap[index] = tab
    })
  }

  // Returns all enabled tabs. This is assigned to the kendo grid.
  public getEnabledTabs(): ConvertDocumentTabModel[] {
    return this.tabCollection.filter((tab) => !tab.disabled)
  }

  private initSlices(): void {
    // enable/disable launch button
    this.convertDocumentFacade.getLaunchOptionEnableState$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((enableState) => {
        if (!enableState && this.launchChecked) {
          this.launchChecked = false
        }
        this.launchEnabled = enableState
        this.cdr.detectChanges()
      })

    // enable/disable relaunch button
    this.convertDocumentFacade.getRelaunchOptionEnableState$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((enableState) => {
        if (!enableState && this.relaunchChecked) {
          this.relaunchChecked = false
        }
        this.relaunchEnabled = enableState
        this.cdr.detectChanges()
      })

    // set checked state for launch button
    this.convertDocumentFacade.getLaunchOptionState$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((checkState) => {
        this.launchChecked = checkState
      })

    // set checked state for relaunch button
    this.convertDocumentFacade.getRelaunchOptionState$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((checkState) => {
        this.relaunchChecked = checkState
      })

    // get error/success message and disable spinner
    this.convertDocumentFacade.getResponseMessage$
      .pipe(
        filter((msg) => !!msg && !!msg.message),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response) => {
        this.showSpinner = false
        if (response.success) {
          this.noticationService.showSuccess(response.message)
        } else {
          this.noticationService.showError(response.message)
        }
        setTimeout(() => {
          this.convertDocumentFacade.setResponseMessage(null)
        }, 500)
        this.cdr.detectChanges()
      })
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public convertClicked(): void {
    this.showSpinner = true
    this.convertDocumentFacade.getCurrentTab$
      .pipe(take(1))
      .subscribe((currentTab) => this.handleConvert(currentTab))
    this.cdr.detectChanges()
  }

  private handleConvert(currentTab: ConvertDocumentTab): void {
    this.convertDocumentFacade.startConvertDocument$.next(currentTab)
  }

  public tabSelected(e: SelectEvent): void {
    if (this.currentTab() === e.title) return
    const tabIndex = this.getEnabledTabs().findIndex((x) => x.title === e.title)
    this.setupTab(tabIndex)
    this.cdr.detectChanges()
  }

  // Sets up the tab by loading the component and setting the current tab in the facade.
  private setupTab(tabIndex: number): void {
    const tabModel = this.tabIndexMap[tabIndex]
    if (tabModel) {
      // set selected tab in the state
      this.convertDocumentFacade.setCurrentTab(tabModel.convertDocumentTab)

      // Check if the selected tab is one of the OCR tabs
      this.isOcrTabSelected = [
        ConvertDocumentTab.ImageOcr,
        ConvertDocumentTab.GeneratedImageOcr,
        ConvertDocumentTab.RedactedImageOcr,
      ].includes(tabModel.convertDocumentTab)

      // Check if the selected tab is audio transcribe
      this.isAudioTranscribeSelected = [
        ConvertDocumentTab.AudioTranscribe,
      ].includes(tabModel.convertDocumentTab)

      // load the component
      const componentRefPromise = this.loadTabContents(
        tabModel.convertDocumentTab
      )
      componentRefPromise.then((componentRef) => {
        tabModel.componentRef = componentRef
        this.cdr.detectChanges()
      })

      // set enable/disabled and visibility for launch/relaunch buttons
      this.setLaunchButtonState(tabModel.convertDocumentTab)
    }
  }

  /*
   * Lazy loads tab content and initializes the componentRefs
   */
  private loadTabContents(tab: ConvertDocumentTab): Promise<any> {
    return new Promise((resolve) => {
      let componentRef: any = null
      switch (tab) {
        case ConvertDocumentTab.HtmlTab:
          if (!this.convertToHtmlComponent) {
            this.convertToHtmlComponent = import(
              '@venio/shared/convert-to-html'
            ).then(({ ConvertToHtmlComponent }) => ConvertToHtmlComponent)
          }
          componentRef = this.convertToHtmlComponent
          break
        case ConvertDocumentTab.RtfTab:
          if (!this.convertToRtfComponent) {
            this.convertToRtfComponent = import(
              '@venio/shared/convert-to-rtf'
            ).then(({ ConvertToRtfComponent }) => ConvertToRtfComponent)
          }
          componentRef = this.convertToRtfComponent
          break
        case ConvertDocumentTab.ImageTab:
          if (!this.convertToImageComponent) {
            this.convertToImageComponent = import(
              '@venio/shared/convert-to-image'
            ).then(({ ConvertToImageComponent }) => ConvertToImageComponent)
          }
          componentRef = this.convertToImageComponent
          break
        case ConvertDocumentTab.ImageOcr:
          if (!this.ocrImageComponent) {
            this.ocrImageComponent = import('@venio/shared/ocr-image').then(
              ({ OcrImageComponent }) => OcrImageComponent
            )
          }
          componentRef = this.ocrImageComponent
          break
        case ConvertDocumentTab.GeneratedImageOcr:
          if (!this.ocrGeneratedImageComponent) {
            this.ocrGeneratedImageComponent = import(
              '@venio/shared/ocr-generated-image'
            ).then(
              ({ OcrGeneratedImageComponent }) => OcrGeneratedImageComponent
            )
          }
          componentRef = this.ocrGeneratedImageComponent
          break
        case ConvertDocumentTab.RedactedImageOcr:
          if (!this.ocrRedactedImageComponent) {
            this.ocrRedactedImageComponent = import(
              '@venio/shared/ocr-redacted-image'
            ).then(({ OcrRedactedImageComponent }) => OcrRedactedImageComponent)
          }
          componentRef = this.ocrRedactedImageComponent
          break
        case ConvertDocumentTab.SlipsheetTab:
          if (!this.createSlipsheetComponent) {
            this.createSlipsheetComponent = import(
              '@venio/shared/create-slipsheet'
            ).then(({ CreateSlipsheetComponent }) => CreateSlipsheetComponent)
          }
          componentRef = this.createSlipsheetComponent
          break
        case ConvertDocumentTab.AudioTranscribe:
          if (!this.audioTranscribeComponent) {
            this.audioTranscribeComponent = import(
              '@venio/shared/audio-transcribe'
            ).then(({ AudioTranscribeComponent }) => AudioTranscribeComponent)
          }
          componentRef = this.audioTranscribeComponent
          break
      }
      resolve(componentRef)
    })
  }

  // Used to set the launch button checked/uncheked/enabled/disabled state for the selected tab.
  private setLaunchButtonState(tab: ConvertDocumentTab): void {
    let launchState = true
    // set launch button state to unchecked for redacted image to ocr. it is only enabled after redactions are selected and summary is retrieved.
    if (tab === ConvertDocumentTab.RedactedImageOcr) {
      launchState = false
    }

    this.convertDocumentFacade.setLaunchOption(launchState, false)

    //hide launch and relaunch buttons for slipsheet tab
    if (tab === ConvertDocumentTab.SlipsheetTab) {
      this.launchVisible = false
      this.relaunchVisible = false
    } else {
      this.launchVisible = true
      this.relaunchVisible = true
    }
  }

  public onCancelAction(): void {
    this.convertDocumentFacade.resetConvertDocumentState()
    this.dialog.close()
  }

  public disableConvertButton(): boolean {
    return this.showSpinner || (!this.launchChecked && !this.relaunchChecked)
  }

  public launchRelaunchChanged(): void {
    this.convertDocumentFacade.setLaunchOption(
      this.launchChecked,
      this.relaunchChecked
    )
  }
}
