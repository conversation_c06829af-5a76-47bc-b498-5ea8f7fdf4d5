import { ComponentFixture, TestBed } from '@angular/core/testing'
import { MultimediaViewerComponent } from './multimedia-viewer.component'
import {
  VenioNotificationModule,
  VenioNotificationService,
} from '@venio/feature/notification'
import { NearNativeFacade } from '../../services/near-native.facade'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('MultimediaViewerComponent', () => {
  let component: MultimediaViewerComponent
  let fixture: ComponentFixture<MultimediaViewerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [MultimediaViewerComponent, VenioNotificationModule],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        NearNativeFacade,
        VenioNotificationService,
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(MultimediaViewerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
