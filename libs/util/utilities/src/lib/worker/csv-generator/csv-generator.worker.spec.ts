import { CSVGeneratorWorkerService } from './csv-generator-worker.service'
import { ColumnConfig } from './csv-generator-worker.type'
import { saveAs } from '@progress/kendo-file-saver'
import { fakeAsync, flush, tick } from '@angular/core/testing'

// Mock the saveAs function
jest.mock('@progress/kendo-file-saver', () => ({
  saveAs: jest.fn(),
}))

const mockPostMessage = jest.fn()
const mockTerminate = jest.fn()

describe('CSVGeneratorWorkerService', () => {
  let csvService: CSVGeneratorWorkerService
  let mockWorker: any

  beforeEach(() => {
    mockWorker = {
      postMessage: mockPostMessage,
      terminate: mockTerminate,
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
    }
    csvService = new CSVGeneratorWorkerService()

    global.Worker = jest.fn(() => mockWorker) as any
  })

  afterEach(() => {
    jest.clearAllMocks() // Clear mocks after each test
  })

  it('returns false for an empty data array', fakeAsync(() => {
    // GIVEN an empty array of data
    const data: object[] = []
    const columns: ColumnConfig[] = [{ title: 'Name', field: 'name' }]

    // WHEN generateCSV is invoked
    const promise = csvService.generateCSV('empty.csv', columns, data)
    tick(200)
    flush()

    // THEN expect the result to be false
    promise.then((result) => {
      expect(result).toBe(false)
    })
    // AND expect saveAs not to be called
    expect(saveAs).not.toHaveBeenCalled()
  }))
  it('creates worker pool with size zero', fakeAsync(() => {
    // GIVEN a dataset
    const data = [{ name: 'Alice', age: 25 }]
    const columns: ColumnConfig[] = [
      { title: 'Name', field: 'name' },
      { title: 'Age', field: 'age' },
    ]

    // Spy on the createWorkerPool method
    const createWorkerPoolSpy = jest
      .spyOn(csvService as any, 'createWorkerPool')
      .mockReturnValue([])

    // WHEN generateCSV is invoked with zero worker pool size
    csvService['workerPoolSize'] = 0
    const promise = csvService.generateCSV('zeroWorkers.csv', columns, data)

    tick(200)
    flush()

    // THEN expect the worker pool to be created with size zero
    promise.then((result) => {
      expect(result).toBe(false)
      expect(createWorkerPoolSpy).toHaveBeenCalledWith(0)
    })

    // AND expect saveAs not to be called
    expect(saveAs).not.toHaveBeenCalled()
  }))
  it('terminates worker pool when already empty', fakeAsync(() => {
    // GIVEN an empty worker pool
    csvService['workerPool'] = []

    // WHEN terminateWorkerPool is invoked
    const terminateSpy = jest.spyOn(csvService as any, 'terminateWorkerPool')
    csvService['terminateWorkerPool'](csvService['workerPool'])

    // THEN expect the terminate method to be called, but no errors should occur
    expect(terminateSpy).toHaveBeenCalled()
    expect(csvService['workerPool']).toHaveLength(0)
  }))
  it('splits data into chunks when data length is smaller than number of chunks', () => {
    // GIVEN a small dataset and a large number of chunks
    const data = [{ name: 'Alice', age: 25 }]
    const totalChunks = 5

    // WHEN splitDataIntoChunks is invoked
    const chunks = csvService['splitDataIntoChunks'](data, totalChunks)

    // THEN expect the chunks to be split correctly
    expect(chunks).toHaveLength(totalChunks)
    expect(chunks[0]).toHaveLength(1)
    for (let i = 1; i < totalChunks; i++) {
      expect(chunks[i]).toHaveLength(0)
    }
  })

  it('throws error for invalid chunk size', async () => {
    // GIVEN a dataset and invalid chunk size
    const data = [{ name: 'Alice', age: 25 }]
    const columns: ColumnConfig[] = [
      { title: 'Name', field: 'name' },
      { title: 'Age', field: 'age' },
    ]
    const invalidChunkSize = -1

    // WHEN generateCSV is invoked
    await expect(
      csvService.generateCSV(
        'invalidChunkSize.csv',
        columns,
        data,
        invalidChunkSize
      )
    ).rejects.toBe('Invalid chunk size provided')

    // AND expect saveAs not to be called
    expect(saveAs).not.toHaveBeenCalled()
  })

  it('generates a CSV file for data with special characters and edge cases', fakeAsync(() => {
    // GIVEN data with special characters
    const data = [
      { name: 'Alice & Bob', age: 25 },
      { name: 'Charlie "Chuck"', age: 30 },
      { name: 'Dave\nDoe', age: 35 },
      { name: 'Eve, Eva', age: 40 },
    ]
    const columns: ColumnConfig[] = [
      { title: 'Name', field: 'name' },
      { title: 'Age', field: 'age' },
    ]

    // WHEN generateCSV is invoked
    const promise = csvService.generateCSV(
      'specialCharsEdgeCases.csv',
      columns,
      data
    )

    tick(200)
    flush()

    // THEN expect the result to be true (indicating successful CSV generation)
    promise.then((result) => {
      expect(result).toBe(true)
      // AND expect saveAs to be called correctly
      expect(saveAs).toHaveBeenCalledWith(
        expect.any(Blob),
        'specialCharsEdgeCases.csv'
      )
    })
  }))
  it('handles a small dataset with a single worker', fakeAsync(() => {
    // GIVEN a small dataset that fits in a single worker
    const data = [{ name: 'Alice', age: 25 }]
    const columns: ColumnConfig[] = [
      { title: 'Name', field: 'name' },
      { title: 'Age', field: 'age' },
    ]

    // WHEN generateCSV is invoked
    const promise = csvService.generateCSV('small.csv', columns, data)
    tick(200)
    flush()

    // THEN expect the result to be true (indicating successful CSV generation)
    promise.then((result) => {
      expect(result).toBe(true)
      // AND expect saveAs to be called correctly
      expect(saveAs).toHaveBeenCalledWith(expect.any(Blob), 'small.csv')
    })
  }))
  it('correctly splits data into chunks for multiple workers', fakeAsync(() => {
    // GIVEN a larger dataset to be split across multiple workers
    const data = Array.from({ length: 3000 }, (_, i) => ({
      name: `Name${i}`,
      age: i,
    }))
    const columns: ColumnConfig[] = [
      { title: 'Name', field: 'name' },
      { title: 'Age', field: 'age' },
    ]

    // WHEN generateCSV is invoked
    const promise = csvService.generateCSV('large.csv', columns, data)
    tick(200)
    flush()

    // THEN expect the result to be true (indicating successful CSV generation)
    promise.then((result) => {
      expect(result).toBe(true)
      // AND expect saveAs to be called correctly
      expect(saveAs).toHaveBeenCalledWith(expect.any(Blob), 'large.csv')
    })
  }))
  it('returns false for null or undefined data array', fakeAsync(() => {
    // GIVEN a null data array
    const data = null as unknown as object[]
    const columns: ColumnConfig[] = [{ title: 'Name', field: 'name' }]

    // WHEN generateCSV is invoked
    const promise = csvService.generateCSV('null.csv', columns, data)
    tick(200)
    flush()

    // THEN expect the result to be false
    promise.then((result) => {
      expect(result).toBe(false)
      // AND expect saveAs not to be called
      expect(saveAs).not.toHaveBeenCalled()
    })
  }))
  it('handles invalid column configurations gracefully', fakeAsync(() => {
    // GIVEN an invalid column configuration
    const data = [{ name: 'Alice', age: 25 }]
    const columns: ColumnConfig[] = [
      { title: 'Name', field: 'invalidField' } as unknown as ColumnConfig,
    ]

    // WHEN generateCSV is invoked
    const promise = csvService.generateCSV('invalidColumns.csv', columns, data)
    tick(200)
    flush()

    // THEN expect the result to be false (indicating CSV generation failed)
    promise.then((result) => {
      expect(result).toBe(false)
      // AND expect saveAs not to be called
      expect(saveAs).not.toHaveBeenCalled()
    })
  }))
  it('generates a CSV file for data with special characters', fakeAsync(() => {
    // GIVEN data with special characters
    const data = [{ name: 'Alice & Bob', age: 25 }]
    const columns: ColumnConfig[] = [
      { title: 'Name', field: 'name' },
      { title: 'Age', field: 'age' },
    ]

    // WHEN generateCSV is invoked
    const promise = csvService.generateCSV('specialChars.csv', columns, data)
    tick(200)
    flush()

    // THEN expect the result to be true (indicating successful CSV generation)
    promise.then((result) => {
      expect(result).toBe(true)
      // AND expect saveAs to be called correctly
      expect(saveAs).toHaveBeenCalledWith(expect.any(Blob), 'specialChars.csv')
    })
  }))
  it('correctly handles different data types', fakeAsync(() => {
    // GIVEN data with different types
    const data = [
      { name: 'Alice', age: 25, active: true },
      { name: 'Bob', age: 30, active: false },
    ]
    const columns: ColumnConfig[] = [
      { title: 'Name', field: 'name' },
      { title: 'Age', field: 'age' },
      { title: 'Active', field: 'active' },
    ]

    // WHEN generateCSV is invoked
    const promise = csvService.generateCSV('differentTypes.csv', columns, data)
    tick(200)
    flush()

    // THEN expect the result to be true (indicating successful CSV generation)
    promise.then((result) => {
      expect(result).toBe(true)
      // AND expect saveAs to be called correctly
      expect(saveAs).toHaveBeenCalledWith(
        expect.any(Blob),
        'differentTypes.csv'
      )
    })
  }))
  it('handles chunk sizes larger than data length', fakeAsync(() => {
    // GIVEN a chunk size larger than the data length
    const data = [{ name: 'Alice', age: 25 }]
    const columns: ColumnConfig[] = [
      { title: 'Name', field: 'name' },
      { title: 'Age', field: 'age' },
    ]

    // WHEN generateCSV is invoked with a large chunk size
    const promise = csvService.generateCSV('largeChunk.csv', columns, data, 100)
    tick(200)
    flush()

    // THEN expect the result to be true (indicating successful CSV generation)
    promise.then((result) => {
      expect(result).toBe(true)
      // AND expect saveAs to be called correctly
      expect(saveAs).toHaveBeenCalledWith(expect.any(Blob), 'largeChunk.csv')
    })
  }))
  it('terminates workers after generating CSV', fakeAsync(() => {
    // GIVEN a dataset
    const data = [{ name: 'Alice', age: 25 }]
    const columns: ColumnConfig[] = [
      { title: 'Name', field: 'name' },
      { title: 'Age', field: 'age' },
    ]

    // Spy on the worker termination method
    const terminateSpy = jest.spyOn(csvService as any, 'terminateWorkerPool')

    // WHEN generateCSV is invoked
    const promise = csvService.generateCSV(
      'terminateWorkers.csv',
      columns,
      data
    )
    tick(200)
    flush()

    // THEN expect the workers to be terminated
    promise.then(() => {
      expect(terminateSpy).toHaveBeenCalled()
    })
  }))
  it('handles 1 million records efficiently', fakeAsync(() => {
    // GIVEN a dataset with 1 million records
    const data = Array.from({ length: 1000000 }, (_, i) => ({
      name: `Name${i}`,
      age: i,
    }))
    const columns: ColumnConfig[] = [
      { title: 'Name', field: 'name' },
      { title: 'Age', field: 'age' },
    ]

    // WHEN generateCSV is invoked
    const startTime = performance.now()
    const promise = csvService.generateCSV('millionRecords.csv', columns, data)
    tick(2000) // Simulate time passing for async operations
    flush()
    const endTime = performance.now()

    // THEN expect the result to be true (indicating successful CSV generation)
    promise.then((result) => {
      expect(result).toBe(true)
      // AND expect the operation to complete within a reasonable time
      expect(endTime - startTime).toBeLessThan(3000) // Adjust threshold as needed
      // AND expect saveAs to be called correctly
      expect(saveAs).toHaveBeenCalledWith(
        expect.any(Blob),
        'millionRecords.csv'
      )
    })
  }))
  it('handles column merging correctly', fakeAsync(() => {
    // GIVEN a dataset with columns that need merging
    const data = [
      {
        fullName: 'Alice Johnson',
        startDate: '2021-01-01',
        endDate: '2021-12-31',
      },
      { fullName: 'Bob Smith', startDate: '2020-05-15', endDate: '2020-11-15' },
    ]
    const columns: ColumnConfig[] = [
      { title: 'Full Name', field: 'fullName' },
      {
        title: 'Activity Period',
        field: 'startDate',
        mergeFields: ['endDate'],
      },
    ]

    // WHEN generateCSV is invoked
    const promise = csvService.generateCSV('mergedColumns.csv', columns, data)
    tick(200) // Simulate time passing for async operations
    flush()

    // THEN expect the result to be true (indicating successful CSV generation)
    promise.then((result) => {
      expect(result).toBe(true)
      // AND expect saveAs to be called correctly
      expect(saveAs).toHaveBeenCalledWith(expect.any(Blob), 'mergedColumns.csv')
    })
  }))
  it('handles duplicate columns gracefully', fakeAsync(() => {
    // GIVEN a dataset with duplicate columns
    const data = [
      { name: 'Alice', age: 25 },
      { name: 'Bob', age: 30 },
    ]
    const columns: ColumnConfig[] = [
      { title: 'Name', field: 'name' },
      { title: 'Name Duplicate', field: 'name' },
      { title: 'Age', field: 'age' },
    ]

    // WHEN generateCSV is invoked
    const promise = csvService.generateCSV(
      'duplicateColumns.csv',
      columns,
      data
    )
    tick(200) // Simulate time passing for async operations
    flush()

    // THEN expect the result to be true (indicating successful CSV generation)
    promise.then((result) => {
      expect(result).toBe(true)
      // AND expect saveAs to be called correctly
      expect(saveAs).toHaveBeenCalledWith(
        expect.any(Blob),
        'duplicateColumns.csv'
      )
    })
  }))
  it('skips invalid data rows gracefully', fakeAsync(() => {
    // GIVEN a dataset with some invalid rows
    const data = [
      { name: 'Alice', age: 25 },
      null,
      undefined,
      { name: 'Bob', age: 30 },
    ]
    const columns: ColumnConfig[] = [
      { title: 'Name', field: 'name' },
      { title: 'Age', field: 'age' },
    ]

    // WHEN generateCSV is invoked
    const promise = csvService.generateCSV('skipInvalidRows.csv', columns, data)
    tick(200) // Simulate time passing for async operations
    flush()

    // THEN expect the result to be true (indicating successful CSV generation)
    promise.then((result) => {
      expect(result).toBe(true)
      // AND expect saveAs to be called correctly
      expect(saveAs).toHaveBeenCalledWith(
        expect.any(Blob),
        'skipInvalidRows.csv'
      )
    })
  }))
  it('returns false for valid columns but empty data', fakeAsync(async () => {
    // GIVEN valid columns but empty data
    const data: object[] = []
    const columns: ColumnConfig[] = [
      { title: 'Name', field: 'name' },
      { title: 'Age', field: 'age' },
    ]

    // WHEN generateCSV is invoked
    const result = await csvService.generateCSV('emptyData.csv', columns, data)

    // THEN expect the result to be false (indicating no data to export)
    expect(result).toBe(false)
    // AND expect saveAs not to be called
    expect(saveAs).not.toHaveBeenCalled()
  }))
  it('handles non-string fields in data', fakeAsync(() => {
    // GIVEN data with non-string fields
    const data = [{ name: 'Alice', age: 25, details: { address: 'Unknown' } }]
    const columns: ColumnConfig[] = [
      { title: 'Name', field: 'name' },
      {
        title: 'Age',
        field: 'age',
      },
      { title: 'Details', field: 'details' },
    ]

    // WHEN generateCSV is invoked
    const promise = csvService.generateCSV('nonStringFields.csv', columns, data)
    tick(200)
    flush()

    // THEN expect the result to be true
    promise.then((result) => {
      expect(result).toBe(true)
      // AND expect saveAs to be called
      expect(saveAs).toHaveBeenCalledWith(
        expect.any(Blob),
        'nonStringFields.csv'
      )
    })
  }))
  it('handles special characters in column titles', fakeAsync(() => {
    // GIVEN columns with special characters in titles
    const data = [{ name: 'Alice', age: 25 }]
    const columns: ColumnConfig[] = [
      { title: 'Name "Full"', field: 'name' },
      { title: 'Age\nYears', field: 'age' },
    ]

    // WHEN generateCSV is invoked
    const promise = csvService.generateCSV(
      'specialCharTitles.csv',
      columns,
      data
    )
    tick(200)
    flush()

    // THEN expect the result to be true
    promise.then((result) => {
      expect(result).toBe(true)
      // AND expect saveAs to be called
      expect(saveAs).toHaveBeenCalledWith(
        expect.any(Blob),
        'specialCharTitles.csv'
      )
    })
  }))
  it('handles empty strings in data', fakeAsync(() => {
    // GIVEN data with empty strings
    const data = [{ name: 'Alice', age: '', details: 'No details' }]
    const columns: ColumnConfig[] = [
      { title: 'Name', field: 'name' },
      {
        title: 'Age',
        field: 'age',
      },
      { title: 'Details', field: 'details' },
    ]

    // WHEN generateCSV is invoked
    const promise = csvService.generateCSV('emptyStrings.csv', columns, data)
    tick(200)
    flush()

    // THEN expect the result to be true
    promise.then((result) => {
      expect(result).toBe(true)
      // AND expect saveAs to be called
      expect(saveAs).toHaveBeenCalledWith(expect.any(Blob), 'emptyStrings.csv')
    })
  }))
  it('ensures correct key mapping for case sensitivity', fakeAsync(() => {
    // GIVEN data with case-sensitive keys
    const data = [{ Name: 'Alice', AGE: 25 }]
    const columns: ColumnConfig[] = [
      { title: 'Name', field: 'name' },
      { title: 'Age', field: 'age' },
    ]

    // WHEN generateCSV is invoked
    const promise = csvService.generateCSV('caseSensitivity.csv', columns, data)
    tick(200)
    flush()

    // THEN expect the result to be true
    promise.then((result) => {
      expect(result).toBe(true)
      // AND expect saveAs to be called
      expect(saveAs).toHaveBeenCalledWith(
        expect.any(Blob),
        'caseSensitivity.csv'
      )
    })
  }))
  it('generates CSV for data with nested objects', fakeAsync(() => {
    // GIVEN data with nested objects
    const data = [{ name: 'Alice', details: { age: 25, address: 'Unknown' } }]
    const columns: ColumnConfig[] = [
      { title: 'Name', field: 'name' },
      { title: 'Details', field: 'details' },
    ]

    // WHEN generateCSV is invoked
    const promise = csvService.generateCSV('nestedObjects.csv', columns, data)
    tick(200)
    flush()

    // THEN expect the result to be true
    promise.then((result) => {
      expect(result).toBe(true)
      // AND expect saveAs to be called
      expect(saveAs).toHaveBeenCalledWith(expect.any(Blob), 'nestedObjects.csv')
    })
  }))
  it('handles large number of columns', fakeAsync(() => {
    // GIVEN data with a large number of columns
    const data = [
      {
        name: 'Alice',
        age: 25,
        address: 'Unknown',
        email: '<EMAIL>',
        phone: '1234567890',
      },
    ]
    const columns: ColumnConfig[] = [
      { title: 'Name', field: 'name' },
      { title: 'Age', field: 'age' },
      { title: 'Address', field: 'address' },
      { title: 'Email', field: 'email' },
      { title: 'Phone', field: 'phone' },
    ]

    // WHEN generateCSV is invoked
    const promise = csvService.generateCSV('largeColumns.csv', columns, data)
    tick(200)
    flush()

    // THEN expect the result to be true
    promise.then((result) => {
      expect(result).toBe(true)
      // AND expect saveAs to be called
      expect(saveAs).toHaveBeenCalledWith(expect.any(Blob), 'largeColumns.csv')
    })
  }))
  it('validates column configuration without field property', fakeAsync(() => {
    // GIVEN a column configuration without the field property
    const data = [{ name: 'Alice', age: 25 }]
    const columns: ColumnConfig[] = [
      { title: 'Name' } as unknown as ColumnConfig,
    ]

    // WHEN generateCSV is invoked
    const promise = csvService.generateCSV('missingField.csv', columns, data)
    tick(200)
    flush()

    // THEN expect the result to be false
    promise.then((result) => {
      expect(result).toBe(false)
      // AND expect saveAs not to be called
      expect(saveAs).not.toHaveBeenCalled()
    })
  }))
  it('handles merge fields with empty data', fakeAsync(() => {
    // GIVEN data with merge fields and some empty values
    const data = [{ name: 'Alice', startDate: '2021-01-01', endDate: '' }]
    const columns: ColumnConfig[] = [
      { title: 'Name', field: 'name' },
      { title: 'Period', field: 'startDate', mergeFields: ['endDate'] },
    ]

    // WHEN generateCSV is invoked
    const promise = csvService.generateCSV(
      'emptyMergeFields.csv',
      columns,
      data
    )
    tick(200)
    flush()

    // THEN expect the result to be true
    promise.then((result) => {
      expect(result).toBe(true)
      // AND expect saveAs to be called
      expect(saveAs).toHaveBeenCalledWith(
        expect.any(Blob),
        'emptyMergeFields.csv'
      )
    })
  }))
  it('handles missing merge fields gracefully', fakeAsync(() => {
    // GIVEN a dataset with missing merge fields
    const data = [
      { name: 'Alice', startDate: '2021-01-01' },
      { name: 'Bob', startDate: '2021-06-01' },
    ]
    const columns: ColumnConfig[] = [
      { title: 'Name', field: 'name' },
      { title: 'Period', field: 'startDate', mergeFields: ['endDate'] },
    ]

    // WHEN generateCSV is invoked
    const promise = csvService.generateCSV(
      'missingMergeFields.csv',
      columns,
      data
    )
    tick(200)
    flush()

    // THEN expect the result to be true
    promise.then((result) => {
      expect(result).toBe(true)
      // AND expect saveAs to be called
      expect(saveAs).toHaveBeenCalledWith(
        expect.any(Blob),
        'missingMergeFields.csv'
      )
    })
  }))
  it('generates CSV for data with mixed types in columns', fakeAsync(() => {
    // GIVEN a dataset with mixed types in columns
    const data = [
      { name: 'Alice', age: '25' },
      { name: 'Bob', age: 30 },
      { name: 'Charlie', age: true },
    ]
    const columns: ColumnConfig[] = [
      { title: 'Name', field: 'name' },
      { title: 'Age', field: 'age' },
    ]

    // WHEN generateCSV is invoked
    const promise = csvService.generateCSV('mixedTypes.csv', columns, data)
    tick(200)
    flush()

    // THEN expect the result to be true
    promise.then((result) => {
      expect(result).toBe(true)
      // AND expect saveAs to be called
      expect(saveAs).toHaveBeenCalledWith(expect.any(Blob), 'mixedTypes.csv')
    })
  }))
  it('handles large data set with small chunk size', fakeAsync(() => {
    // GIVEN a large dataset with a small chunk size
    const data = Array.from({ length: 10000 }, (_, i) => ({
      name: `Name${i}`,
      age: i,
    }))
    const columns: ColumnConfig[] = [
      { title: 'Name', field: 'name' },
      { title: 'Age', field: 'age' },
    ]
    const chunkSize = 10

    // WHEN generateCSV is invoked
    const promise = csvService.generateCSV(
      'largeDataSmallChunk.csv',
      columns,
      data,
      chunkSize
    )
    tick(200)
    flush()

    // THEN expect the result to be true
    promise.then((result) => {
      expect(result).toBe(true)
      // AND expect saveAs to be called
      expect(saveAs).toHaveBeenCalledWith(
        expect.any(Blob),
        'largeDataSmallChunk.csv'
      )
    })
  }))
  it('handles special characters in data fields', fakeAsync(() => {
    // GIVEN a dataset with special characters in data fields
    const data = [
      { name: 'Alice', age: 25 },
      { name: 'Bob "the Builder"', age: 30 },
      { name: 'Charlie, the great', age: 35 },
    ]
    const columns: ColumnConfig[] = [
      { title: 'Name', field: 'name' },
      { title: 'Age', field: 'age' },
    ]

    // WHEN generateCSV is invoked
    const promise = csvService.generateCSV(
      'specialCharsInFields.csv',
      columns,
      data
    )
    tick(200)
    flush()

    // THEN expect the result to be true
    promise.then((result) => {
      expect(result).toBe(true)
      // AND expect saveAs to be called
      expect(saveAs).toHaveBeenCalledWith(
        expect.any(Blob),
        'specialCharsInFields.csv'
      )
    })
  }))
  it('handles null and undefined values in data', fakeAsync(async () => {
    // GIVEN a dataset with null and undefined values
    const data = [
      { name: 'Alice', age: null },
      { name: 'Bob', age: undefined },
      { name: 'Charlie', age: 35 },
    ]
    const columns: ColumnConfig[] = [
      { title: 'Name', field: 'name' },
      { title: 'Age', field: 'age' },
    ]

    // WHEN generateCSV is invoked
    const promise = csvService.generateCSV(
      'nullUndefinedValues.csv',
      columns,
      data
    )
    tick(200)
    flush()

    // THEN expect the result to be true
    promise.then((result) => {
      expect(result).toBeTruthy()
      // AND expect saveAs to be called
      expect(saveAs).toHaveBeenCalledWith(
        expect.any(Blob),
        'nullUndefinedValues.csv'
      )
    })
  }))
})
