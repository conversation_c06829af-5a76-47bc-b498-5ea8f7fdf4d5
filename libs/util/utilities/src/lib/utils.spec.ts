import { extractTagRuleIdPatterns } from './utils'

describe('extractTagRuleIdPatterns', () => {
  it('should extract Tag Rule IDs and normalize a single correctly formatted pattern', () => {
    // GIVEN an input text with one correctly formatted "TagRuleId IN (...)" pattern
    const inputText = 'TagRuleId IN (1,2,3)'

    // WHEN the function is called with the input text
    const result = extractTagRuleIdPatterns(inputText)

    // THEN it should return the correct Tag Rule IDs and normalized syntax
    expect(result.ruleIds).toEqual([1, 2, 3])
    expect(result.syntax).toEqual(['TagRuleId In (1,2,3)'])
  })

  it('should extract Tag Rule IDs and normalize multiple correctly formatted patterns', () => {
    // GIVEN an input text with multiple correctly formatted "TagRuleId IN (...)" patterns
    const inputText = 'TagRuleId IN (1,2,3) AND TagRuleId IN (4,5,6)'

    // WHEN the function is called with the input text
    const result = extractTagRuleIdPatterns(inputText)

    // THEN it should return all Tag Rule IDs and normalized syntax for each pattern
    expect(result.ruleIds).toEqual([1, 2, 3, 4, 5, 6])
    expect(result.syntax).toEqual([
      'TagRuleId In (1,2,3)',
      'TagRuleId In (4,5,6)',
    ])
  })

  it('should handle varying capitalization and spacing in patterns', () => {
    // GIVEN input texts with varying capitalization and irregular spacing
    const inputText1 = 'tagruleid in(1,2,3)'
    const inputText2 = 'TAGRULEID    IN   (1, 2,   3 )'
    const inputText3 = 'TaGrUlEiD iN (1 2 3)'

    // WHEN the function is called with the input texts
    const result1 = extractTagRuleIdPatterns(inputText1)
    const result2 = extractTagRuleIdPatterns(inputText2)
    const result3 = extractTagRuleIdPatterns(inputText3)

    // THEN it should normalize the patterns correctly
    expect(result1.syntax).toEqual(['TagRuleId In (1,2,3)'])
    expect(result2.syntax).toEqual(['TagRuleId In (1,2,3)'])
    expect(result3.syntax).toEqual(['TagRuleId In (1,2,3)'])
  })

  it('should ignore patterns containing invalid Tag Rule IDs', () => {
    // GIVEN an input text with patterns containing non-numeric values as Tag Rule IDs
    const inputText = 'TagRuleId IN (a,b,c)'

    // WHEN the function is called with the input text
    const result = extractTagRuleIdPatterns(inputText)

    // THEN it should return empty arrays for ruleIds and syntax
    expect(result.ruleIds).toEqual([])
    expect(result.syntax).toEqual([])
  })

  it('should ignore the entire pattern if it contains any invalid Tag Rule IDs', () => {
    // GIVEN an input text with a pattern containing both valid and invalid Tag Rule IDs
    const inputText = 'TagRuleId IN (1, a, 2, b, 3)'

    // WHEN the function is called with the input text
    const result = extractTagRuleIdPatterns(inputText)

    // THEN it should ignore the pattern entirely and return empty arrays
    expect(result.ruleIds).toStrictEqual([])
    expect(result.syntax).toStrictEqual([])
  })

  it('should skip patterns with no Tag Rule IDs inside the parentheses', () => {
    // GIVEN input texts with patterns that have empty or non-numeric content
    const inputText1 = 'TagRuleId IN ()'
    const inputText2 = 'TagRuleId IN ( , , )'

    // WHEN the function is called with the input texts
    const result1 = extractTagRuleIdPatterns(inputText1)
    const result2 = extractTagRuleIdPatterns(inputText2)

    // THEN it should return empty arrays for ruleIds and syntax
    expect(result1.ruleIds).toEqual([])
    expect(result1.syntax).toEqual([])
    expect(result2.ruleIds).toEqual([])
    expect(result2.syntax).toEqual([])
  })

  it('should ignore incomplete or malformed patterns', () => {
    // GIVEN input texts with incomplete or malformed patterns
    const inputText1 = 'TagRuleId IN (1,2' // Missing closing parenthesis
    const inputText2 = 'TagRuleId IN 1,2,3)' // Missing opening parenthesis
    const inputText3 = 'TagRuleId IN (1,2))' // Extra closing parenthesis

    // WHEN the function is called with the input texts
    const result1 = extractTagRuleIdPatterns(inputText1)
    const result2 = extractTagRuleIdPatterns(inputText2)
    const result3 = extractTagRuleIdPatterns(inputText3)

    // THEN it should ignore these patterns
    expect(result1.ruleIds).toEqual([])
    expect(result1.syntax).toEqual([])
    expect(result2.ruleIds).toEqual([])
    expect(result2.syntax).toEqual([])
    // For inputText3, the regex may still capture a valid pattern despite extra closing parenthesis
    expect(result3.ruleIds).toEqual([1, 2])
    expect(result3.syntax).toEqual(['TagRuleId In (1,2)'])
  })

  it('should return empty arrays when the input text contains no patterns', () => {
    // GIVEN an input text with no patterns
    const inputText = 'This is a test string with no patterns.'

    // WHEN the function is called with the input text
    const result = extractTagRuleIdPatterns(inputText)

    // THEN it should return empty arrays for ruleIds and syntax
    expect(result.ruleIds).toEqual([])
    expect(result.syntax).toEqual([])
  })

  it('should handle duplicates in Tag Rule IDs and include all patterns', () => {
    // GIVEN an input text with duplicate Tag Rule IDs and patterns
    const inputText = 'TagRuleId IN (1,2,2,3,3,3) AND TagRuleId IN (1,1,2)'

    // WHEN the function is called with the input text
    const result = extractTagRuleIdPatterns(inputText)

    // THEN it should return unique Tag Rule IDs and include all patterns
    expect(result.ruleIds).toEqual([1, 2, 3])
    expect(result.syntax).toEqual([
      'TagRuleId In (1,2,3)',
      'TagRuleId In (1,2)',
    ])
  })

  it('should handle Tag Rule IDs separated by multiple spaces, tabs, or mixed commas and spaces', () => {
    // GIVEN an input text with irregular separators
    const inputText = 'TagRuleId IN (1    2\t3 ,   4)'

    // WHEN the function is called with the input text
    const result = extractTagRuleIdPatterns(inputText)

    // THEN it should extract the Tag Rule IDs correctly and normalize the pattern
    expect(result.ruleIds).toEqual([1, 2, 3, 4])
    expect(result.syntax).toEqual(['TagRuleId In (1,2,3,4)'])
  })

  it('should ignore patterns with extra parentheses', () => {
    // GIVEN an input text with extra parentheses
    const inputText = 'TagRuleId IN ((1,2,3))'

    // WHEN the function is called with the input text
    const result = extractTagRuleIdPatterns(inputText)

    // THEN it should not extract any Tag Rule IDs or syntax
    expect(result.ruleIds).toEqual([])
    expect(result.syntax).toEqual([])
  })

  it('should ignore patterns where "TagRuleId" is misspelled', () => {
    // GIVEN input texts where "TagRuleId" is misspelled
    const inputText1 = 'TagRule IN (1,2,3)'
    const inputText2 = 'TagRuleIdd IN (1,2,3)'

    // WHEN the function is called with the input texts
    const result1 = extractTagRuleIdPatterns(inputText1)
    const result2 = extractTagRuleIdPatterns(inputText2)

    // THEN it should return empty arrays for ruleIds and syntax
    expect(result1.ruleIds).toEqual([])
    expect(result1.syntax).toEqual([])
    expect(result2.ruleIds).toEqual([])
    expect(result2.syntax).toEqual([])
  })

  it('should handle Tag Rule IDs with leading zeros', () => {
    // GIVEN an input text with Tag Rule IDs that have leading zeros
    const inputText = 'TagRuleId IN (01,002,0003)'

    // WHEN the function is called with the input text
    const result = extractTagRuleIdPatterns(inputText)

    // THEN it should parse the Tag Rule IDs correctly
    expect(result.ruleIds).toEqual([1, 2, 3])
    expect(result.syntax).toEqual(['TagRuleId In (1,2,3)'])
  })

  it('should ignore patterns containing negative Tag Rule IDs', () => {
    // GIVEN an input text with negative Tag Rule IDs
    const inputText = 'TagRuleId IN (-1, -2, 3)'

    // WHEN the function is called with the input text
    const result = extractTagRuleIdPatterns(inputText)

    // THEN it should ignore the entire pattern and not extract any Tag Rule IDs
    expect(result.ruleIds).toEqual([])
    expect(result.syntax).toEqual([])
  })

  it('should extract zero as a valid Tag Rule ID', () => {
    // GIVEN an input text with zero included as a Tag Rule ID
    const inputText = 'TagRuleId IN (0,1,2)'

    // WHEN the function is called with the input text
    const result = extractTagRuleIdPatterns(inputText)

    // THEN it should include zero in the Tag Rule IDs
    expect(result.ruleIds).toEqual([0, 1, 2])
    expect(result.syntax).toEqual(['TagRuleId In (0,1,2)'])
  })

  it('should ignore patterns containing decimal Tag Rule IDs', () => {
    // GIVEN an input text with decimal numbers as Tag Rule IDs
    const inputText = 'TagRuleId IN (1.1,2.2,3.3)'

    // WHEN the function is called with the input text
    const result = extractTagRuleIdPatterns(inputText)

    // THEN it should ignore the entire pattern
    expect(result.ruleIds).toEqual([])
    expect(result.syntax).toEqual([])
  })

  it('should ignore patterns with mismatched parentheses', () => {
    // GIVEN an input text with mismatched parentheses
    const inputText = 'TagRuleId IN (1,2,3))'

    // WHEN the function is called with the input text
    const result = extractTagRuleIdPatterns(inputText)

    // THEN it should ignore the pattern and not extract any Tag Rule IDs
    expect(result.ruleIds).toEqual([1, 2, 3])
    expect(result.syntax).toEqual(['TagRuleId In (1,2,3)'])
  })

  it('should handle complex text with multiple valid patterns', () => {
    // GIVEN an input text with complex text and multiple valid patterns
    const inputText =
      'Some text TagRuleId IN (1,2,3) some more text TagRuleId IN (4,5,6) end text'

    // WHEN the function is called with the input text
    const result = extractTagRuleIdPatterns(inputText)

    // THEN it should extract all Tag Rule IDs and normalize all patterns
    expect(result.ruleIds).toEqual([1, 2, 3, 4, 5, 6])
    expect(result.syntax).toEqual([
      'TagRuleId In (1,2,3)',
      'TagRuleId In (4,5,6)',
    ])
  })

  it('should handle invalid input types gracefully', () => {
    // GIVEN an input that is null or undefined
    const inputTextNull = null as any
    const inputTextUndefined = undefined as any

    // WHEN the function is called with the invalid inputs
    // THEN it should not throw exceptions and return empty arrays
    expect(() => extractTagRuleIdPatterns(inputTextNull)).not.toThrow()
    expect(() => extractTagRuleIdPatterns(inputTextUndefined)).not.toThrow()

    const resultNull = extractTagRuleIdPatterns(inputTextNull)
    const resultUndefined = extractTagRuleIdPatterns(inputTextUndefined)

    expect(resultNull.ruleIds).toEqual([])
    expect(resultNull.syntax).toEqual([])
    expect(resultUndefined.ruleIds).toEqual([])
    expect(resultUndefined.syntax).toEqual([])
  })

  it('should ignore patterns with Tag Rule IDs containing special characters', () => {
    // GIVEN an input text with Tag Rule IDs containing special characters
    const inputText = 'TagRuleId IN (1,2#,3$)'

    // WHEN the function is called with the input text
    const result = extractTagRuleIdPatterns(inputText)

    // THEN it should ignore the entire pattern and not extract any Tag Rule IDs
    expect(result.ruleIds).toEqual([])
    expect(result.syntax).toEqual([])
  })

  it('should ignore patterns with unsupported Tag Rule ID separators', () => {
    // GIVEN an input text with Tag Rule IDs separated by semicolons
    const inputText = 'TagRuleId IN (1;2;3)'

    // WHEN the function is called with the input text
    const result = extractTagRuleIdPatterns(inputText)

    // THEN it should not extract any Tag Rule IDs
    expect(result.ruleIds).toEqual([])
    expect(result.syntax).toEqual([])
  })

  it('should handle very large Tag Rule IDs', () => {
    // GIVEN an input text with very large Tag Rule IDs
    const largeNumber = '*********'
    const inputText = `TagRuleId IN (${largeNumber})`

    // WHEN the function is called with the input text
    const result = extractTagRuleIdPatterns(inputText)
    const parsedNumber = parseInt(largeNumber, 10)

    // THEN it should include the large Tag Rule ID
    expect(result.ruleIds).toStrictEqual([parsedNumber])
    expect(result.syntax).toStrictEqual([`TagRuleId In (${largeNumber})`])
  })

  it('should handle patterns with additional words correctly', () => {
    // GIVEN an input text with additional words after the pattern
    const inputText = 'TagRuleId IN (1,2,3) AND other conditions'

    // WHEN the function is called with the input text
    const result = extractTagRuleIdPatterns(inputText)

    // THEN it should extract the Tag Rule IDs and normalize the pattern
    expect(result.ruleIds).toEqual([1, 2, 3])
    expect(result.syntax).toEqual(['TagRuleId In (1,2,3)'])
  })

  it('should ignore patterns with invalid Tag Rule ID representations like NaN, undefined, null', () => {
    // GIVEN an input text with invalid Tag Rule ID representations
    const inputText = 'TagRuleId IN (1, NaN, undefined, null, 2)'

    // WHEN the function is called with the input text
    const result = extractTagRuleIdPatterns(inputText)

    // THEN it should ignore the entire pattern and not extract any Tag Rule IDs
    expect(result.ruleIds).toEqual([])
    expect(result.syntax).toEqual([])
  })

  it('should handle Tag Rule IDs separated by tabs', () => {
    // GIVEN an input text with Tag Rule IDs separated by tabs
    const inputText = 'TagRuleId IN (1\t2\t3)'

    // WHEN the function is called with the input text
    const result = extractTagRuleIdPatterns(inputText)

    // THEN it should extract the Tag Rule IDs and normalize the pattern
    expect(result.ruleIds).toEqual([1, 2, 3])
    expect(result.syntax).toEqual(['TagRuleId In (1,2,3)'])
  })

  it('should ignore patterns with nested parentheses', () => {
    // GIVEN an input text with nested parentheses
    const inputText = 'TagRuleId IN (1,(2,3))'

    // WHEN the function is called with the input text
    const result = extractTagRuleIdPatterns(inputText)

    // THEN it should ignore the pattern as nested parentheses are not handled
    expect(result.ruleIds).toEqual([])
    expect(result.syntax).toEqual([])
  })

  it('should ignore extremely malformed patterns without throwing exceptions', () => {
    // GIVEN an input text with severely malformed patterns
    const inputText = 'TagRuleId IN (((1,2,3)))'

    // WHEN the function is called with the input text
    const result = extractTagRuleIdPatterns(inputText)

    // THEN it should ignore the pattern and not extract any Tag Rule IDs
    expect(result.ruleIds).toEqual([])
    expect(result.syntax).toEqual([])
  })

  it('should not extract unicode digits as Tag Rule IDs', () => {
    // GIVEN an input text with unicode digits
    const inputText = 'TagRuleId IN (１,２,３)' // Full-width digits

    // WHEN the function is called with the input text
    const result = extractTagRuleIdPatterns(inputText)

    // THEN it should not extract any Tag Rule IDs
    expect(result.ruleIds).toEqual([])
    expect(result.syntax).toEqual([])
  })

  it('should handle empty string input', () => {
    // GIVEN an empty input text
    const inputText = ''

    // WHEN the function is called with the empty input text
    const result = extractTagRuleIdPatterns(inputText)

    // THEN it should return empty arrays for ruleIds and syntax
    expect(result.ruleIds).toEqual([])
    expect(result.syntax).toEqual([])
  })

  it('should ignore patterns with non-integer Tag Rule IDs like floats and exponential notation', () => {
    // GIVEN an input text with non-integer Tag Rule IDs
    const inputText = 'TagRuleId IN (1.5, 2e3, 3.14159)'

    // WHEN the function is called with the input text
    const result = extractTagRuleIdPatterns(inputText)

    // THEN it should ignore the entire pattern
    expect(result.ruleIds).toEqual([])
    expect(result.syntax).toEqual([])
  })

  it('should match patterns case-insensitively even with different "IN" keywords', () => {
    // GIVEN input texts with variations in the "IN" keyword
    const inputText1 = 'TagRuleId In (1,2,3)'
    const inputText2 = 'TagRuleId iN (1,2,3)'
    const inputText3 = 'TagRuleId IN (1,2,3)'

    // WHEN the function is called with the input texts
    const result1 = extractTagRuleIdPatterns(inputText1)
    const result2 = extractTagRuleIdPatterns(inputText2)
    const result3 = extractTagRuleIdPatterns(inputText3)

    // THEN it should normalize and extract the patterns
    expect(result1.syntax).toEqual(['TagRuleId In (1,2,3)'])
    expect(result2.syntax).toEqual(['TagRuleId In (1,2,3)'])
    expect(result3.syntax).toEqual(['TagRuleId In (1,2,3)'])
  })

  it('should handle very large input texts efficiently', () => {
    // GIVEN a very large input text with multiple patterns
    const patterns = Array(1000)
      .fill('TagRuleId IN (1,2,3)')
      .join(' some text ')
    const inputText = patterns

    // WHEN the function is called with the large input text
    const startTime = Date.now()
    const result = extractTagRuleIdPatterns(inputText)
    const endTime = Date.now()

    // THEN it should process efficiently and extract all patterns
    expect(result.ruleIds).toEqual([1, 2, 3])
    expect(result.syntax).toHaveLength(1000)
    expect(endTime - startTime).toBeLessThan(2000) // Should complete within 2 seconds
  })
})
