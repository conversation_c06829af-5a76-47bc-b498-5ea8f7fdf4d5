import {
  ChangeDetectionStrategy,
  Component,
  inject,
  OnDestroy,
  OnInit,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { Subject, combineLatest, filter, takeUntil } from 'rxjs'
import {
  AiFacade,
  EdaiDocumentRelevancyResponseModel,
} from '@venio/data-access/ai'
import { NotificationService, Type } from '@progress/kendo-angular-notification'
import { UuidGenerator } from '@venio/util/uuid'
import { toSignal } from '@angular/core/rxjs-interop'
import { SkeletonComponent } from '@progress/kendo-angular-indicators'

@Component({
  selector: 'venio-edai-document-relevancy-list',
  standalone: true,
  imports: [CommonModule, SkeletonComponent],
  templateUrl: './edai-document-relevancy-list.component.html',
  styleUrl: './edai-document-relevancy-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EdaiDocumentRelevancyListComponent implements OnInit, OnDestroy {
  private readonly toDestroy = new Subject<void>()

  public readonly eDaiDocumentRelevance =
    signal<EdaiDocumentRelevancyResponseModel>(undefined)

  private readonly aiFacade = inject(AiFacade)

  public readonly isRelevanceDocumentLoading = toSignal(
    this.aiFacade.isEdaiDocumentRelevanceLoading$,
    {
      initialValue: true,
    }
  )

  private notificationService = inject(NotificationService)

  public ngOnInit(): void {
    this.#selectDocumentRelevancy()
  }

  public ngOnDestroy(): void {
    this.toDestroy.next()
    this.toDestroy.complete()
  }

  #showMessage(content = '', type: Type): void {
    if (!content?.trim()) return

    const notificationRef = this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 10000,
      width: 300,
    })

    notificationRef.notification.location.nativeElement.onclick = (): void => {
      notificationRef.hide()
    }
  }

  #selectDocumentRelevancy(): void {
    combineLatest([
      this.aiFacade.selectEdaiDocumentRelevancySuccess$,
      this.aiFacade.selectEdaiDocumentRelevancyError$,
    ])
      .pipe(
        filter(([success, error]) => Boolean(success) || Boolean(error)),
        takeUntil(this.toDestroy)
      )
      .subscribe(([success, error]) => {
        const isError = Boolean(error?.status)
        const style = success?.message ? 'success' : 'error'
        const message = success?.message || error?.message
        const results = {
          ...success.data,
          issueDetail:
            success.data.issueDetail.map((r) => ({
              ...r,
              id: UuidGenerator.uuid,
            })) || [],
        } as EdaiDocumentRelevancyResponseModel
        this.eDaiDocumentRelevance.set(results)

        // Only display the message if it is an error
        if (isError) {
          this.#showMessage(message, { style })
        }
      })
  }
}
