import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  OnDestroy,
  OnInit,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { Subject, combineLatest, filter, takeUntil } from 'rxjs'
import { AiFacade, EdaiDocumentPrivilegeResponse } from '@venio/data-access/ai'
import { NotificationService, Type } from '@progress/kendo-angular-notification'
import { toSignal } from '@angular/core/rxjs-interop'
import { SkeletonComponent } from '@progress/kendo-angular-indicators'

@Component({
  selector: 'venio-edai-document-privilege-list',
  standalone: true,
  imports: [CommonModule, SkeletonComponent],
  templateUrl: './edai-document-privilege-list.component.html',
  styleUrl: './edai-document-privilege-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EdaiDocumentPrivilegeListComponent implements OnInit, OnDestroy {
  private readonly toDestroy = new Subject<void>()

  private readonly aiFacade = inject(AiFacade)

  private readonly notificationService = inject(NotificationService)

  public readonly eDaiDocumentPrivilege =
    signal<EdaiDocumentPrivilegeResponse>(undefined)

  public readonly valueExists = computed(
    () =>
      this.eDaiDocumentPrivilege()?.privilegeLogEntry?.trim() &&
      this.eDaiDocumentPrivilege()?.privilegeLogEntry?.trim()
  )

  public readonly isPrivilegeDocumentLoading = toSignal(
    this.aiFacade.selectIsEdaiDocumentPrivilegeLoading$,
    {
      initialValue: true,
    }
  )

  public ngOnInit(): void {
    this.#selectDocumentPrivilege()
  }

  public ngOnDestroy(): void {
    this.toDestroy.next()
    this.toDestroy.complete()
  }

  #showMessage(content = '', type: Type): void {
    if (!content?.trim()) return

    const notificationRef = this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 10000,
      width: 300,
    })

    // When the user clicks on the notification, it should hide
    notificationRef.notification.location.nativeElement.onclick = (): void => {
      notificationRef.hide()
    }
  }

  #selectDocumentPrivilege(): void {
    combineLatest([
      this.aiFacade.selectEdaiDocumentPrivilegeSuccess$,
      this.aiFacade.selectEdaiDocumentPrivilegeError$,
    ])
      .pipe(
        filter(([success, error]) => Boolean(success) || Boolean(error)),
        takeUntil(this.toDestroy)
      )
      .subscribe(([success, error]) => {
        const isError = Boolean(error?.status)
        const style = success?.message ? 'success' : 'error'
        const message = success?.message || error?.message
        const results = success.data as EdaiDocumentPrivilegeResponse
        this.eDaiDocumentPrivilege.set(results)

        if (isError) {
          this.#showMessage(message, { style })
        }
      })
  }
}
