import { FormArray, FormControl, FormGroup } from '@angular/forms'
import {
  AIJobType,
  BasicJobForm,
  GeneralForm,
  JobForm,
  PIIJobForm,
  PrivilegeJobForm,
  PrivilegeTypeInfoForm,
  PrivilegeTypes,
  RelevanceJobForm,
} from '@venio/data-access/ai'

const createIssueFormGroup = (): FormGroup<GeneralForm> => {
  return new FormGroup<GeneralForm>({
    name: new FormControl<string>('', { nonNullable: true }),
    description: new FormControl<string>('', { nonNullable: true }),
  })
}

const createPrivilegeTypeInfoFormGroup = (
  privilegeType: PrivilegeTypes,
  formState: string | boolean
): FormGroup<PrivilegeTypeInfoForm> => {
  return new FormGroup<PrivilegeTypeInfoForm>({
    privilegeType: new FormControl<PrivilegeTypes>(privilegeType, {
      nonNullable: true,
    }),
    name: new FormControl<string | boolean>(formState, { nonNullable: true }),
    description: new FormControl<string>('', { nonNullable: true }),
  })
}

export const createJobFormGroup = (): FormGroup<JobForm> => {
  return new FormGroup<JobForm>({
    jobType: new FormControl<AIJobType>(AIJobType.Relevance, {
      nonNullable: true,
    }),
    basicJobModel: new FormGroup<BasicJobForm>({
      jobName: new FormControl<string>('', { nonNullable: true }),
      searchTempTable: new FormControl<string>('', { nonNullable: true }),
      selectedFileIds: new FormControl<number[]>([], { nonNullable: true }),
      unSelectedFileIds: new FormControl<number[]>([], { nonNullable: true }),
      isBatchSelected: new FormControl<boolean>(false, { nonNullable: true }),
    }),
    relevanceJobModel: new FormGroup<RelevanceJobForm>({
      issues: new FormArray<FormGroup<GeneralForm>>([createIssueFormGroup()]),
    }),
    privilegeJobModel: new FormGroup<PrivilegeJobForm>({
      attorneyList: new FormControl<string>('', { nonNullable: true }),
      domains: new FormControl<string>('', { nonNullable: true }),
      privilegeTypes: new FormArray<FormGroup<PrivilegeTypeInfoForm>>([
        createPrivilegeTypeInfoFormGroup(PrivilegeTypes.AttorneyClient, false),
        createPrivilegeTypeInfoFormGroup(PrivilegeTypes.AttorneyClient, false),
        createPrivilegeTypeInfoFormGroup(PrivilegeTypes.CustomType1, ''),
        createPrivilegeTypeInfoFormGroup(PrivilegeTypes.CustomType2, ''),
      ]),
    }),
    piiJobModel: new FormGroup<PIIJobForm>({
      defaultTypes: new FormArray<FormGroup<GeneralForm>>([
        createIssueFormGroup(),
      ]),
      customTypes: new FormArray<FormGroup<GeneralForm>>([
        createIssueFormGroup(),
      ]),
      isPIIExtractJob: new FormControl<boolean>(false),
    }),
  })
}
