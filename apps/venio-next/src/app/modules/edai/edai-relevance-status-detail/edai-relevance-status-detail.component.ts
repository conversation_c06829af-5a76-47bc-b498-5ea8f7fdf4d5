import {
  ChangeDetectionStrategy,
  Component,
  inject,
  OnDestroy,
  OnInit,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ColumnComponent, GridComponent } from '@progress/kendo-angular-grid'
import { Subject, combineLatest, filter, takeUntil } from 'rxjs'
import { AiFacade } from '@venio/data-access/ai'
import { NotificationService, Type } from '@progress/kendo-angular-notification'

@Component({
  selector: 'venio-edai-relevance-status-detail',
  standalone: true,
  imports: [CommonModule, ColumnComponent, GridComponent],
  templateUrl: './edai-relevance-status-detail.component.html',
  styleUrl: './edai-relevance-status-detail.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EdaiRelevanceStatusDetailComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  private readonly aiFacade = inject(AiFacade)

  private notificationService = inject(NotificationService)

  public readonly statusDetailData = signal([])

  public ngOnInit(): void {
    this.#selectStatusJobDetail()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  #showMessage(content = '', type: Type): void {
    if (!content?.trim()) return

    const notificationRef = this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 10000,
      width: 300,
    })

    // When the user clicks on the notification, it should hide
    notificationRef.notification.location.nativeElement.onclick = (): void => {
      notificationRef.hide()
    }
  }

  #selectStatusJobDetail(): void {
    combineLatest([
      this.aiFacade.selectEdaiJobStatusDetailsSuccess$,
      this.aiFacade.selectEdaiJobStatusDetailsError$,
    ])
      .pipe(
        filter(([success, error]) => Boolean(success) || Boolean(error)),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([success, error]) => {
        const isError = Boolean(error?.status)
        const style = success?.message ? 'success' : 'error'
        const message = success?.message || error?.message
        const results = success?.data || []
        this.statusDetailData.set(results)

        // Only display the message if it is an error
        if (isError) {
          this.#showMessage(message, { style })
        }
      })
  }
}
