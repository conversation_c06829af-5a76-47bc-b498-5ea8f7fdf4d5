import {
  ChangeDetectionStrategy,
  Component,
  input,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonComponent } from '@progress/kendo-angular-buttons'
import {
  DropDownListComponent,
  SuffixTemplateDirective,
} from '@progress/kendo-angular-dropdowns'
import { caretAltDownIcon } from '@progress/kendo-svg-icons'
import { TextBoxComponent } from '@progress/kendo-angular-inputs'
import { FormGroup, ReactiveFormsModule } from '@angular/forms'
import { AIJobType, JobForm } from '@venio/data-access/ai'
import { toObservable, toSignal } from '@angular/core/rxjs-interop'
import { map } from 'rxjs/operators'
import { switchMap } from 'rxjs'

@Component({
  selector: 'venio-edai-job-inputs',
  standalone: true,
  imports: [
    CommonModule,
    ButtonComponent,
    TextBoxComponent,
    SuffixTemplateDirective,
    ReactiveFormsModule,
    DropDownListComponent,
  ],
  templateUrl: './edai-job-inputs.component.html',
  styleUrl: './edai-job-inputs.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EdaiJobInputsComponent {
  public edaiFormGroup = input.required<FormGroup<JobForm>>()

  public readonly isPiiType = toSignal(
    toObservable(signal(false)).pipe(
      switchMap(() => this.edaiFormGroup().valueChanges),
      map(({ jobType }) => jobType === AIJobType.PII)
    )
  )

  public readonly downIcon = caretAltDownIcon

  public jobTypes: Array<{ text: string; value: AIJobType }> = [
    { text: 'Relevance', value: AIJobType.Relevance },
    { text: 'Privilege', value: AIJobType.Privilege },
    { text: 'PII', value: AIJobType.PII },
  ]
}
