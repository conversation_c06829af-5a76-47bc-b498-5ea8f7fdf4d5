import { ComponentFixture, TestBed } from '@angular/core/testing'
import { EdaiJobInputsComponent } from './edai-job-inputs.component'
import { DialogRef } from '@progress/kendo-angular-dialog'
import { provideMockStore } from '@ngrx/store/testing'
import {
  DocumentsFacade,
  FieldFacade,
  SearchFacade,
} from '@venio/data-access/review'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { ActivatedRoute } from '@angular/router'
import { createJobFormGroup } from '../mocks/form-group.mock'

describe('EdaiIssueInputsComponent', () => {
  let component: EdaiJobInputsComponent
  let fixture: ComponentFixture<EdaiJobInputsComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [EdaiJobInputsComponent],
      providers: [
        { provide: DialogRef, useValue: {} },
        provideMockStore({}),
        DocumentsFacade,
        SearchFacade,
        FieldFacade,
        provideHttpClientTesting(),
        provideHttpClient(),
        provideNoopAnimations(),
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                projectId: 1,
              },
            },
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(EdaiJobInputsComponent)
    component = fixture.componentInstance
    // Create the FormGroup for JobForm
    const mockFormGroup = createJobFormGroup()

    // Set the input for the component
    fixture.componentRef.setInput('edaiFormGroup', mockFormGroup)
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
