import { ComponentFixture, TestBed } from '@angular/core/testing'
import { EdaiStatusGridComponent } from './edai-status-grid.component'
import { DialogRef } from '@progress/kendo-angular-dialog'
import { provideMockStore } from '@ngrx/store/testing'
import {
  DocumentsFacade,
  FieldFacade,
  SearchFacade,
  SearchResultFacade,
} from '@venio/data-access/review'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { ActivatedRoute } from '@angular/router'
import { AiFacade } from '@venio/data-access/ai'
import { of } from 'rxjs'
import { BreadcrumbFacade } from '@venio/data-access/breadcrumbs'

describe('EdaiStatusGridComponent', () => {
  let component: EdaiStatusGridComponent
  let fixture: ComponentFixture<EdaiStatusGridComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [EdaiStatusGridComponent],
      providers: [
        { provide: DialogRef, useValue: {} },
        provideMockStore({}),
        DocumentsFacade,
        SearchFacade,
        FieldFacade,
        provideHttpClientTesting(),
        provideHttpClient(),
        provideNoopAnimations(),
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                projectId: 1,
              },
            },
          },
        },
        {
          provide: AiFacade,
          useValue: {
            selectEdaiStatusResults$: of({}),
            isEdaiStatusLoading$: of(false),
          },
        },
        {
          provide: SearchResultFacade,
          useValue: {
            getSearchResultFieldValues: of([]),
          },
        },
        {
          provide: SearchFacade,
          useValue: {
            search: jest.fn(),
            selectSearchFormValues$: of({}),
          },
        },
        {
          provide: BreadcrumbFacade,
          useValue: {
            storeBreadcrumbs: jest.fn(),
            selectBreadcrumbStack$: of([]),
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(EdaiStatusGridComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
