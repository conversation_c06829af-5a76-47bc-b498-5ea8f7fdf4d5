<div class="t-flex t-gap-4 t-flex-auto t-mt-3">
  <div
    class="t-flex t-flex-col t-gap-3 t-bg-[#F6F6F6] t-rounded t-p-6 t-flex-shrink-0">
    @if(!isPIIEntityLoading()){
    <label
      class="t-flex t-items-center t-gap-2 t-cursor-pointer"
      *ngFor="let label of piiDefaultEntities()">
      <input
        kendoCheckBox
        type="checkbox"
        [checked]="isTypeSelected(label.piiTypeName)"
        (change)="checkboxChange($event.target['checked'], label)" />
      <span class="t-text-gray-700 t-font-medium">
        {{ label.piiTypeName }}
      </span> </label
    >} @else {
    <div class="t-flex t-flex-col t-gap-2">
      @for(n of [1,2,3,4,5]; track n) {
      <div class="t-flex t-flex-row t-items-center t-gap-2">
        <kendo-skeleton shape="text" [width]="15" />
        <kendo-skeleton shape="text" [width]="115" />
      </div>
      }
    </div>
    }
  </div>

  <div
    [formGroup]="piiFormGroup()"
    class="t-flex-grow t-bg-[#F6F6F6] t-flex t-flex-col t-gap-6 t-rounded t-p-6">
    <ng-container formArrayName="customTypes">
      @for(control of customTypeControls().controls; track
      control.controls.name.value + index; let index = $index; let count =
      $count; let last = $last){
      <div class="t-flex t-flex-wrap t-gap-3" [formGroupName]="index">
        <div class="t-w-full">
          @if(!isPIIEntityLoading()){
          <kendo-textbox
            formControlName="name"
            placeholder="Custom PII Type"
            class="t-w-64" />
          } @else {
          <kendo-skeleton shape="rectangle" [height]="34" [width]="200" />
          }
        </div>
        <div class="t-w-full t-flex t-flex-row t-items-center t-gap-4">
          <div class="t-w-1/3">
            @if(!isPIIEntityLoading()){
            <kendo-textarea
              formControlName="description"
              resizable="none"
              [rows]="5"
              class="t-w-full"
              placeholder="Custom PII Type Definition" />
            @if(getValidation(index)) {
            <span class="t-text-error"> Definition is required </span>
            } } @else {
            <kendo-skeleton shape="rectangle" [height]="80" width="100%" />
            }
          </div>
          <div class="t-flex t-flex-col t-gap-4 t-justify-between t-mt-2">
            @if(!isPIIEntityLoading()){
            <kendo-svg-icon
              *ngIf="count > 1"
              kendoTooltip
              (click)="removeCustomType(index)"
              [icon]="removeIcon"
              title="Remove"
              size="large"
              class="t-cursor-pointer t-flex t-items-center t-bg-error t-rounded-full t-text-white" />
            <kendo-svg-icon
              *ngIf="last"
              (click)="addCustomType()"
              [icon]="addIcon"
              kendoTooltip
              title="Add Another Custom Type"
              size="large"
              class="t-cursor-pointer t-flex t-items-center t-bg-[#1EBADC] t-rounded-full t-text-white" />
            } @else {
            <kendo-skeleton shape="circle" [height]="20" [width]="20" />
            <kendo-skeleton shape="circle" [height]="20" [width]="20" />
            }
          </div>
        </div>
      </div>
      }
    </ng-container>
    <label class="t-flex t-items-center t-w-20 t-gap-2 t-cursor-pointer">
      <input kendoCheckBox formControlName="isPIIExtractJob" type="checkbox" />
      <span class="t-text-gray-700 t-font-medium"> Extract </span>
    </label>

    @if(totalPIIControls() > 15) {
    <div class="t-w-full t-text-error">Only 15 PII options can be selected</div>
    }
  </div>
</div>
