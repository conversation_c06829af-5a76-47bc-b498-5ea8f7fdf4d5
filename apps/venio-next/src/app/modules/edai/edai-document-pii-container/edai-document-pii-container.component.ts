import {
  ChangeDetectionStrategy,
  Component,
  inject,
  input,
  OnD<PERSON>roy,
  OnInit,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { AiFacade } from '@venio/data-access/ai'
import { ActivatedRoute } from '@angular/router'
import { DocumentsFacade } from '@venio/data-access/review'
import { debounceTime, filter, Subject, takeUntil } from 'rxjs'
import { SkeletonComponent } from '@progress/kendo-angular-indicators'
import { EdaiDocumentPiiListComponent } from '../edai-document-pii-list/edai-document-pii-list.component'
import { UtilityPanelItem } from '@venio/data-access/document-utility'
import { UtilityPanelType } from '@venio/shared/models/constants'

@Component({
  selector: 'venio-edai-document-pii-container',
  standalone: true,
  imports: [CommonModule, SkeletonComponent, EdaiDocumentPiiListComponent],
  templateUrl: './edai-document-pii-container.component.html',
  styleUrl: './edai-document-pii-container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EdaiDocumentPiiContainerComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  private readonly aiFacade = inject(AiFacade)

  private readonly documentFacade = inject(DocumentsFacade)

  private readonly activatedRoute = inject(ActivatedRoute)

  /**
   * The property being set from where it has been used and the object
   * is being utilized to display the PII types.
   *
   * There are two types now, one is PIIDetect and the other is PIIExtract
   * @see UtilityPanelType
   * @see DocumentUtilityPanelComponent
   */
  public readonly panelItem = input<UtilityPanelItem>(undefined)

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public ngOnInit(): void {
    this.#selectSelectedFileId()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  #fetchDocumentPII(fileId: number): void {
    const isPiiDetect =
      this.panelItem().componentId === UtilityPanelType.EDAI_AI_PII_DETECT
    this.aiFacade.fetchEdaiDocumentPii(this.projectId, fileId, isPiiDetect)
  }

  #selectSelectedFileId(): void {
    this.documentFacade.getCurrentDocument$
      .pipe(
        filter((id) => id > 0),
        debounceTime(100), // Do not accept too many requests to avoid server overload
        takeUntil(this.toDestroy$)
      )
      .subscribe((id) => {
        this.aiFacade.resetAiState([
          'edaiDocumentPIISuccess',
          'edaiDocumentPIIError',
          'isEdaiDocumentPIILoading',
        ])
        this.#fetchDocumentPII(id)
      })
  }
}
