import {
  ChangeDetectionStrategy,
  Component,
  inject,
  input,
  OnD<PERSON>roy,
  OnInit,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { Subject, combineLatest, filter, takeUntil } from 'rxjs'
import { AiFacade, EdaiDocumentPIIResponse } from '@venio/data-access/ai'
import { NotificationService, Type } from '@progress/kendo-angular-notification'
import { toSignal } from '@angular/core/rxjs-interop'
import { SkeletonComponent } from '@progress/kendo-angular-indicators'
import { UtilityPanelItem } from '@venio/data-access/document-utility'
import { map } from 'rxjs/operators'

@Component({
  selector: 'venio-edai-document-pii-list',
  standalone: true,
  imports: [CommonModule, SkeletonComponent],
  templateUrl: './edai-document-pii-list.component.html',
  styleUrl: './edai-document-pii-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EdaiDocumentPiiListComponent implements OnInit, OnD<PERSON>roy {
  private readonly toDestroy = new Subject<void>()

  private readonly aiFacade = inject(AiFacade)

  private readonly notificationService = inject(NotificationService)

  /**
   * The property being set from where it has been used and the object
   * is being utilized to display the PII types.
   *
   * There are two types now, one is PIIDetect and the other is PIIExtract
   */
  public readonly panelItem = input.required<UtilityPanelItem>()

  public readonly eDaiDocumentPII = signal<EdaiDocumentPIIResponse[]>([])

  public readonly isPIIDocumentLoading = toSignal(
    this.aiFacade.selectEdaiDocumentPiiLoading$,
    {
      initialValue: true,
    }
  )

  public ngOnInit(): void {
    this.#selectDocumentPII()
  }

  public ngOnDestroy(): void {
    this.toDestroy.next()
    this.toDestroy.complete()
  }

  #showMessage(content = '', type: Type): void {
    if (!content?.trim()) return

    const notificationRef = this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 10000,
      width: 300,
    })

    notificationRef.notification.location.nativeElement.onclick = (): void => {
      notificationRef.hide()
    }
  }

  /**
   * Pulls off the content based on PII types from record object.
   *
   * Currently, we have two types of PII, one is PIIDetect and the other is PIIExtract.
   * @returns {void}
   */
  #selectDocumentPII(): void {
    combineLatest([
      this.aiFacade.selectEdaiDocumentPiiSuccess$.pipe(
        map((d) => d?.[this.panelItem().componentId])
      ),
      this.aiFacade.selectEdaiDocumentPiiError$.pipe(
        map((d) => d?.[this.panelItem().componentId])
      ),
    ])
      .pipe(
        filter(([success, error]) => Boolean(success) || Boolean(error)),
        takeUntil(this.toDestroy)
      )
      .subscribe(([success, error]) => {
        const isError = Boolean(error?.status)
        const style = success?.message ? 'success' : 'error'
        const message = success?.message || error?.message
        const results = success?.data as EdaiDocumentPIIResponse[]

        this.eDaiDocumentPII.set(results)

        if (isError) {
          this.#showMessage(message, { style })
        }
      })
  }
}
