import {
  ChangeDetectionStrategy,
  Component,
  input,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  AbstractControl,
  FormArray,
  FormGroup,
  ReactiveFormsModule,
} from '@angular/forms'
import {
  CheckBoxDirective,
  ErrorComponent,
  TextAreaComponent,
  TextBoxComponent,
} from '@progress/kendo-angular-inputs'
import { toObservable, toSignal } from '@angular/core/rxjs-interop'
import { map, combineLatest, switchMap, startWith, of } from 'rxjs'
import {
  JobForm,
  PrivilegeTypes,
  PrivilegeTypeInfoForm,
  PrivilegeJobForm,
} from '@venio/data-access/ai'

@Component({
  selector: 'venio-edai-privilege-form',
  standalone: true,
  imports: [
    CommonModule,
    ErrorComponent,
    TextBoxComponent,
    TextAreaComponent,
    CheckBoxDirective,
    ReactiveFormsModule,
  ],
  templateUrl: './edai-privilege-form.component.html',
  styleUrl: './edai-privilege-form.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EdaiPrivilegeFormComponent {
  public edaiFormGroup = input.required<FormGroup<JobForm>>()

  public readonly privilegeType = PrivilegeTypes

  private readonly customTypes = [
    PrivilegeTypes.CustomType1,
    PrivilegeTypes.CustomType2,
  ]

  public readonly attorneyOrProductSelected = toSignal(
    toObservable(signal(true)).pipe(
      switchMap(() => {
        const formArray = this.edaiFormGroup().get(
          'privilegeJobModel.privilegeTypes'
        ) as FormArray<FormGroup<PrivilegeTypeInfoForm>>

        const controls = [
          PrivilegeTypes.AttorneyClient,
          PrivilegeTypes.WorkProduct,
        ]
          .map((type) =>
            formArray.controls
              .find((c) => c.get('privilegeType')?.value === type)
              ?.get('name')
          )
          .filter((c): c is AbstractControl<boolean> => !!c)

        return combineLatest([
          ...controls.map((c) => c.valueChanges.pipe(startWith(c.value))),
          ...controls.map((c) => c.statusChanges.pipe(startWith(c.status))),
        ]).pipe(
          map(
            () =>
              !controls.some((c) => c.dirty && c.touched) ||
              controls.some((c) => !!c.value)
          )
        )
      })
    ),
    { initialValue: true }
  )

  public readonly customTypesDefinitionValidation = toSignal(
    toObservable(signal(true)).pipe(
      switchMap(() => {
        // 1) Grab the privilegeJobModel from your main form
        const privilegeJobModel = this.edaiFormGroup().get(
          'privilegeJobModel'
        ) as FormGroup<PrivilegeJobForm>

        // 2) Grab the privilegeTypes array
        const privTypesArray = privilegeJobModel.get(
          'privilegeTypes'
        ) as FormArray<FormGroup<PrivilegeTypeInfoForm>>

        // 3) For each custom type, build a separate boolean stream
        const streams = this.customTypes.map((type) => {
          // find the group corresponding to that custom type
          const group = privTypesArray.controls.find(
            (ctrl) => ctrl.value.privilegeType === type
          )

          // If there's no matching group, treat it as automatically valid
          if (!group) {
            return of(true)
          }

          // Grab 'name' & 'description'
          const nameCtrl = group.get('name')
          const descCtrl = group.get('description')

          // Combine name + desc changes
          return combineLatest([
            nameCtrl.valueChanges.pipe(startWith(nameCtrl.value)),
            nameCtrl.statusChanges.pipe(startWith(nameCtrl.status)),
            descCtrl.valueChanges.pipe(startWith(descCtrl.value)),
            descCtrl.statusChanges.pipe(startWith(descCtrl.status)),
          ]).pipe(
            map(() => {
              // If "name" has a value (string), "description" is required
              // If "name" is empty, "description" is optional
              const nameVal =
                typeof nameCtrl.value === 'string' ? nameCtrl.value.trim() : ''
              const descVal = (descCtrl.value ?? '').trim()

              if (nameVal) {
                // If name is non-empty => ensure desc is non-empty & valid
                return descVal.length > 0 && descCtrl.valid
              }
              // If name is empty => no requirement for desc
              return true
            })
          )
        })

        return combineLatest(streams).pipe(
          // We can return the entire array as-is
          map((validities) => validities)
        )
      })
    ),
    {
      initialValue: [],
    }
  )
}
