import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  Input,
  ViewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { TextAreaComponent } from '@progress/kendo-angular-inputs'
import { FormGroup, ReactiveFormsModule } from '@angular/forms'

@Component({
  selector: 'venio-edai-issue-row',
  standalone: true,
  imports: [CommonModule, TextAreaComponent, ReactiveFormsModule],
  templateUrl: './edai-issue-row.component.html',
  styleUrl: './edai-issue-row.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EdaiIssueRowComponent implements AfterViewInit {
  @Input({ required: true })
  public index: number

  @Input({ required: true })
  public issueFormControl: FormGroup

  @ViewChild(TextAreaComponent)
  public textAreaComponent: TextAreaComponent

  public ngAfterViewInit(): void {
    this.textAreaComponent.focus()
  }
}
