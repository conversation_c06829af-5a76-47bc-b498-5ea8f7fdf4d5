import { ComponentFixture, TestBed } from '@angular/core/testing'
import { EdaiIssueRowComponent } from './edai-issue-row.component'
import { DialogRef } from '@progress/kendo-angular-dialog'
import { provideMockStore } from '@ngrx/store/testing'
import {
  DocumentsFacade,
  FieldFacade,
  SearchFacade,
} from '@venio/data-access/review'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { ActivatedRoute } from '@angular/router'
import { FormControl, FormGroup } from '@angular/forms'

describe('EdaiIssueRowComponent', () => {
  let component: EdaiIssueRowComponent
  let fixture: ComponentFixture<EdaiIssueRowComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [EdaiIssueRowComponent],
      providers: [
        { provide: DialogRef, useValue: {} },
        provideMockStore({}),
        DocumentsFacade,
        SearchFacade,
        FieldFacade,
        provideHttpClientTesting(),
        provideHttpClient(),
        provideNoopAnimations(),
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                projectId: 1,
              },
            },
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(EdaiIssueRowComponent)
    component = fixture.componentInstance
    component.issueFormControl = new FormGroup({
      name: new FormControl(),
      description: new FormControl(),
    })
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
