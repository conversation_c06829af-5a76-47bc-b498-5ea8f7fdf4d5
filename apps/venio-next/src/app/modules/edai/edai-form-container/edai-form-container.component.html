<div
  class="t-w-full t-h-full t-pt-4 t-relative t-overflow-hidden"
  [formGroup]="edaiFormGroup()">
  @defer{
  <venio-edai-job-inputs [edaiFormGroup]="edaiFormGroup()" />
  } @defer { @switch (selectedJobType()){ @case (aiJobTypes.Relevance) {
  <venio-edai-issue-form
    class="t-inline-block t-relative t-w-full"
    [edaiFormGroup]="edaiFormGroup()" />
  } @case (aiJobTypes.Privilege) {
  <venio-edai-privilege-form
    class="t-inline-block t-relative t-w-full"
    [edaiFormGroup]="edaiFormGroup()" />
  } @case (aiJobTypes.PII){
  <venio-edai-pii-form
    class="t-inline-block t-relative t-w-full"
    [edaiFormGroup]="edaiFormGroup()" />
  } } }
</div>
