import { ComponentFixture, TestBed } from '@angular/core/testing'
import { EdaiDocumentPrivilegeContainerComponent } from './edai-document-privilege-container.component'
import { DialogRef } from '@progress/kendo-angular-dialog'
import { provideMockStore } from '@ngrx/store/testing'
import {
  DocumentsFacade,
  FieldFacade,
  SearchFacade,
} from '@venio/data-access/review'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { ActivatedRoute } from '@angular/router'

describe('EdaiDocumentRelevancyContainerComponent', () => {
  let component: EdaiDocumentPrivilegeContainerComponent
  let fixture: ComponentFixture<EdaiDocumentPrivilegeContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [EdaiDocumentPrivilegeContainerComponent],
      providers: [
        { provide: DialogRef, useValue: {} },
        provideMockStore({}),
        DocumentsFacade,
        SearchFacade,
        FieldFacade,
        provideHttpClientTesting(),
        provideHttpClient(),
        provideNoopAnimations(),
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                projectId: 1,
              },
            },
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(EdaiDocumentPrivilegeContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
