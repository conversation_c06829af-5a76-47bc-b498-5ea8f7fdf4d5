import {
  ChangeDetectionStrategy,
  Component,
  inject,
  OnDestroy,
  OnInit,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { AiFacade } from '@venio/data-access/ai'
import { ActivatedRoute } from '@angular/router'
import { DocumentsFacade } from '@venio/data-access/review'
import { debounceTime, filter, Subject, takeUntil } from 'rxjs'
import { SkeletonComponent } from '@progress/kendo-angular-indicators'
import { EdaiDocumentPrivilegeListComponent } from '../edai-document-privilege-list/edai-document-privilege-list.component'

@Component({
  selector: 'venio-edai-document-privilege-container',
  standalone: true,
  imports: [
    CommonModule,
    SkeletonComponent,
    EdaiDocumentPrivilegeListComponent,
  ],
  templateUrl: './edai-document-privilege-container.component.html',
  styleUrl: './edai-document-privilege-container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EdaiDocumentPrivilegeContainerComponent
  implements OnInit, OnDestroy
{
  private readonly toDestroy$ = new Subject<void>()

  private readonly aiFacade = inject(AiFacade)

  private readonly documentFacade = inject(DocumentsFacade)

  private readonly activatedRoute = inject(ActivatedRoute)

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public ngOnInit(): void {
    this.#selectSelectedFileId()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  #fetchDocumentPrivilege(fileId: number): void {
    this.aiFacade.fetchEdaiDocumentPrivilege(this.projectId, fileId)
  }

  #selectSelectedFileId(): void {
    this.documentFacade.getCurrentDocument$
      .pipe(
        filter((id) => id > 0),
        debounceTime(100), // Do not accept too many requests to avoid server overload
        takeUntil(this.toDestroy$)
      )
      .subscribe((id) => {
        this.aiFacade.resetAiState([
          'edaiDocumentPrivilegeSuccess',
          'edaiDocumentPrivilegeError',
          'isEdaiDocumentPrivilegeLoading',
        ])
        this.#fetchDocumentPrivilege(id)
      })
  }
}
