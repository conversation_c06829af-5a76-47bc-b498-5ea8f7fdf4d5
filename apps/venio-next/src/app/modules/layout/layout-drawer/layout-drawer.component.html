<kendo-drawer-container class="t-h-[100vh] t-w-full t-overflow-y-hidden">
  <kendo-drawer
    #drawer
    [items]="items"
    mode="push"
    [mini]="!isLeftAsideVisible()"
    [expanded]="false"
    (select)="onSelect($event)"
    [miniWidth]="!isLeftAsideVisible() ? 90 : 0">
    <ng-template kendoDrawerItemTemplate let-item>
      <div
        #parentEl
        class="t-w-full t-text-center t-flex t-items-center t-flex-col t-py-2 t-transition-colors hover:t-text-[#FFBB12]">
        <span
          [parentElement]="parentEl"
          venioSvgLoader
          [isSelectedState]="item.selected"
          [svgUrl]="'assets/svg/' + item.icon"
          [width]="item.x"
          [height]="item.y"
          hoverColor="#FFBB12">
          <kendo-loader size="small"></kendo-loader>
        </span>
        <div
          [ngClass]="!item.selected ? '' : 't-text-[#FFBB12]'"
          class="!t-font-medium t-tracking-[0.135rem] t-text-[0.75rem]">
          {{ item.text }}
        </div>
      </div>
    </ng-template>
  </kendo-drawer>

  <kendo-drawer-content class="t-relative t-h-full !t-w-[calc(100%_-_90px)]">
    <router-outlet></router-outlet>
  </kendo-drawer-content>
</kendo-drawer-container>
