<ng-container
  *ngComponentOutlet="navigationProgressBarComp | async"></ng-container>
<div
  class="t-flex t-justify-between t-items-center t-shadow-[0_0_5px_-3px_rgba(0,0,0,0.8)] t-relative t-sticky t-top-0 t-z-10 t-bg-[#FFFFFF]">
  <div class="t-w-1/3">
    <venio-toolbar-logo />
  </div>
  <div class="t-w-1/3 t-flex t-justify-center t-items-center">
    <!-- All Documents -->
  </div>
  <div class="t-w-1/3 t-flex t-justify-end t-items-end">
    <div class="t-px-5 t-w-full t-flex t-justify-end t-items-end t-gap-2">
      <venio-toolbar-history-log />
      <venio-toolbar-notification />
      <venio-toolbar-user />
    </div>
  </div>
</div>
