import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ProjectInfoComponent } from './project-info.component'
import { provideMockStore } from '@ngrx/store/testing'
import { CaseInfoFacade } from '@venio/data-access/review'

describe('ProjectInfoComponent', () => {
  let component: ProjectInfoComponent
  let fixture: ComponentFixture<ProjectInfoComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ProjectInfoComponent],
      providers: [provideMockStore({}), CaseInfoFacade],
    }).compileComponents()

    fixture = TestBed.createComponent(ProjectInfoComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
