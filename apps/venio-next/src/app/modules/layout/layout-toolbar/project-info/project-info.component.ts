import {
  ChangeDetectionStrategy,
  Component,
  Inject,
  Injector,
  OnInit,
  Optional,
  Type,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { Subject } from 'rxjs'

@Component({
  selector: 'venio-project-info',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './project-info.component.html',
  styleUrl: './project-info.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ProjectInfoComponent implements OnInit {
  public readonly toDestroy$ = new Subject<void>()

  public projectSelectorComp: Promise<Type<unknown>>

  constructor(
    @Optional() @Inject('selectorView') public selectorView = 'home',
    private injector: Injector
  ) {}

  public ngOnInit(): void {
    this.loadProjectSelectorComp()
  }

  private loadProjectSelectorComp(): void {
    this.projectSelectorComp = import(
      '../../../layout/layout-toolbar/project-selector/project-selector.component'
    ).then(({ ProjectSelectorComponent }) => {
      this.createInjector(this.selectorView)
      return ProjectSelectorComponent
    })
  }

  private createInjector(selectorViewValue: string): Injector {
    return Injector.create({
      providers: [{ provide: 'selectorView', useValue: selectorViewValue }],
      parent: this.injector,
    })
  }
}
