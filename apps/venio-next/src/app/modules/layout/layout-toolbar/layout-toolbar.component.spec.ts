import { ComponentFixture, TestBed } from '@angular/core/testing'
import { LayoutToolbarComponent } from './layout-toolbar.component'
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('LayoutToolbarComponent', () => {
  let component: LayoutToolbarComponent
  let fixture: ComponentFixture<LayoutToolbarComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [LayoutToolbarComponent],
      providers: [provideHttpClient(), provideHttpClientTesting()],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents()

    fixture = TestBed.createComponent(LayoutToolbarComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
