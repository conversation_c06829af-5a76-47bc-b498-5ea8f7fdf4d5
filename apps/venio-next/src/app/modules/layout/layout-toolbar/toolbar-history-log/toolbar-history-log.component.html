<kendo-dropdownbutton
  buttonClass="!t-px-0 !t-py-1 t-text-[#1EBADC] t-rounded-[4px] t-border-[1px] t-border-[#BEBEBE] hover:t-opacity-90"
  fillMode="outline"
  [data]="historyDropdownData"
  (itemClick)="onMenuClick($event, dialogActions)"
  [popupSettings]="{
    align: 'right',
    popupClass:
      '!t-mt-2.5 !t-p-[0.625rem] !t-border-0  !t-shadow-lg v-custom-theme'
  }">
  <span
    class="t-w-full"
    #parentElTag
    [parentElement]="parentElTag"
    venioSvgLoader
    color="#1EBADC"
    svgUrl="assets/svg/icon-history.svg"
    height="1.5rem"
    width="3rem">
    <kendo-loader size="small"></kendo-loader>
  </span>
</kendo-dropdownbutton>

<ng-template #dialogActions>
  <div class="t-flex t-gap-4 t-justify-end">
    <button kendoButton (click)="close()" fillMode="outline" themeColor="dark">
      CANCEL
    </button>
  </div>
</ng-template>

<div kendoDialogContainer></div>
