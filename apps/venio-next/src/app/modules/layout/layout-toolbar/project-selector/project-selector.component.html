<div class="t-h-full t-flex t-items-center t-justify-center t-w-56">
  <kendo-dropdownlist
    [data]="filteredProjects | async"
    textField="projectName"
    valueField="projectId"
    [ngModel]="selectedProjectId()"
    [valuePrimitive]="true"
    [filterable]="true"
    class="v-custom-project-selector"
    [ngClass]="{ 'v-hide-dropdown': selectorView === 'review' }"
    [disabled]="selectorView === 'review'"
    (filterChange)="onFilterChange($event)"
    (valueChange)="onProjectChange($event)">
    <ng-template kendoDropDownListNoDataTemplate>
      {{ noDataText }}
    </ng-template>
  </kendo-dropdownlist>
</div>
