import { ChangeDetectionStrategy, Component } from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  DropDownButtonModule,
  ListItemModel,
} from '@progress/kendo-angular-buttons'
import { logoutIcon, userIcon } from '@progress/kendo-svg-icons'

@Component({
  selector: 'venio-toolbar-user',
  standalone: true,
  imports: [CommonModule, DropDownButtonModule],
  templateUrl: './toolbar-user.component.html',
  styleUrls: ['./toolbar-user.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ToolbarUserComponent {
  public data: ListItemModel[] = [
    {
      text: 'Profile',
      svgIcon: userIcon,
    },
    {
      text: 'Log Out',
      svgIcon: logoutIcon,
    },
  ]
}
