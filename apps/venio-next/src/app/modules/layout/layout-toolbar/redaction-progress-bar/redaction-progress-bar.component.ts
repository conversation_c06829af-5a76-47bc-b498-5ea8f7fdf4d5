import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  OnD<PERSON>roy,
  OnInit,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  LabelFn,
  LabelSettings,
  ProgressBarModule,
} from '@progress/kendo-angular-progressbar'
import {
  filter,
  Subject,
  Subscription,
  switchMap,
  takeUntil,
  tap,
  timer,
} from 'rxjs'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { ActivatedRoute } from '@angular/router'
import {
  BulkRedactFacade,
  SearchDupOption,
  SearchFacade,
  SearchRequestModel,
  ViewerService,
} from '@venio/data-access/review'
import { DialogRef, DialogService } from '@progress/kendo-angular-dialog'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'

@Component({
  selector: 'venio-redaction-progress-bar',
  standalone: true,
  imports: [CommonModule, ProgressBarModule, TooltipsModule],
  templateUrl: './redaction-progress-bar.component.html',
  styleUrl: './redaction-progress-bar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RedactionProgressBarComponent implements OnInit, OnDestroy {
  private viewerService = inject(ViewerService)

  private cdr = inject(ChangeDetectorRef)

  private activatedRoute = inject(ActivatedRoute)

  private dialogService = inject(DialogService)

  private searchFacade = inject(SearchFacade)

  private bulkPdfRedactionFacade = inject(BulkRedactFacade)

  private dialogRef: DialogRef

  private toDestroy$: Subject<void> = new Subject<void>()

  private get projectId(): number {
    return Number(this.activatedRoute.snapshot.queryParams['projectId'])
  }

  public label: LabelSettings = {}

  public completedCount: number

  public totalCount: number

  public inProgressFailureCount: number

  private timerSubscription: Subscription = null

  public ngOnInit(): void {
    this.bulkPdfRedactionFacade.getQueueFilesForBulkRedactionSuccessResponse$
      .pipe(
        filter((result) => Boolean(result)),
        tap(() => this.stopTimer()),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response) => {
        if (!this.timerSubscription) this.startTimer()
      })

    this.startTimer()
  }

  private startTimer(): void {
    this.timerSubscription = timer(0, 5000)
      .pipe(
        switchMap(() => this.viewerService.redactionStatus(this.projectId)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((response: ResponseModel) => {
        this.cdr.markForCheck()
        this.completedCount = response.data.completedCount
        this.totalCount = response.data.totalCount
        this.inProgressFailureCount = response.data.inProgressFailureCount
        this.label = {
          format: this.formatter,
          position: 'end',
        }
        if (this.totalCount === 0 || this.totalCount === this.completedCount)
          this.stopTimer()
      })
  }

  public stopTimer(): void {
    this.timerSubscription?.unsubscribe()
    this.timerSubscription = null
  }

  public changeProgress(): void {
    this.label = {
      format: this.formatter,
      position: 'end',
    }
  }

  public formatter: LabelFn = (value: number): string => {
    return `${value}/${this.totalCount}`
  }

  public onProgressBarClick(): void {
    import(
      '../../../document-utility/bulk-redaction/bulk-redaction-grid/bulk-redaction-grid.component'
    ).then((d) => {
      this.dialogRef = this.dialogService.open({
        content: d.BulkRedactionGridComponent,
        maxHeight: '90vh',
        maxWidth: '90vw',
        minHeight: '90vh',
        minWidth: '90vw',
      })

      const bulkRedactHistory = this.dialogRef.content.instance
      bulkRedactHistory.showFilter = false
      bulkRedactHistory.showProgressDetail = true
      bulkRedactHistory.hideColumn = true
      bulkRedactHistory.hideDownloadButton = true
      bulkRedactHistory.successfulRedacted = this.completedCount
      bulkRedactHistory.failedRedacted = this.inProgressFailureCount

      bulkRedactHistory.viewFailedDocuments
        .pipe(takeUntil(this.toDestroy$))
        .subscribe((searchExpression: string) => {
          this.onViewFailedDocumentsClick(searchExpression)
        })
    })
  }

  public onViewFailedDocumentsClick(searchExpression: string): void {
    const searchRequestModel: SearchRequestModel = {
      searchExpression: searchExpression,
      includePC: false,
      projectId: this.projectId.toString(),
      lstMedia: null,
      searchGuid: '',
      userType:
        localStorage.getItem('DocShareUserRole')?.toLowerCase() === 'external'
          ? 'EXTERNAL'
          : 'INTERNAL',
      baseGUID: '',
      isForwardFilter: false,
      searchDuplicateOption: SearchDupOption.SHOW_ALL_DUPS,
    }

    this.searchFacade.search(searchRequestModel)

    // Close the dialog
    this.dialogRef?.close()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }
}
