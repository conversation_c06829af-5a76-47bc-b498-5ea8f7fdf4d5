import { ComponentFixture, TestBed } from '@angular/core/testing'
import { RedactionProgressBarComponent } from './redaction-progress-bar.component'
import { ActivatedRoute } from '@angular/router'
import { of } from 'rxjs'
import {
  DialogContainerService,
  DialogService,
} from '@progress/kendo-angular-dialog'
import {
  BulkRedactFacade,
  FieldFacade,
  SearchFacade,
} from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('RedactionProgressBarComponent', () => {
  let component: RedactionProgressBarComponent
  let fixture: ComponentFixture<RedactionProgressBarComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [RedactionProgressBarComponent],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        DialogService,
        DialogContainerService,
        BulkRedactFacade,
        SearchFacade,
        FieldFacade,
        {
          provide: ActivatedRoute,
          useValue: {
            queryParams: of({ projectId: '2' }),
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(RedactionProgressBarComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
