import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ToolbarNotificationComponent } from './toolbar-notification.component'
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('ToolbarNotificationComponent', () => {
  let component: ToolbarNotificationComponent
  let fixture: ComponentFixture<ToolbarNotificationComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ToolbarNotificationComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(ToolbarNotificationComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
