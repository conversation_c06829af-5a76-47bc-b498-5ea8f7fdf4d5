import { ChangeDetectionStrategy, Component } from '@angular/core'
import { CommonModule, NgOptimizedImage } from '@angular/common'
import { BadgeModule, LoaderModule } from '@progress/kendo-angular-indicators'
import { DropDownButtonModule } from '@progress/kendo-angular-buttons'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'

@Component({
  selector: 'venio-toolbar-notification',
  standalone: true,
  imports: [
    CommonModule,
    BadgeModule,
    DropDownButtonModule,
    NgOptimizedImage,
    SvgLoaderDirective,
    LoaderModule,
  ],
  templateUrl: './toolbar-notification.component.html',
  styleUrls: ['./toolbar-notification.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ToolbarNotificationComponent {}
