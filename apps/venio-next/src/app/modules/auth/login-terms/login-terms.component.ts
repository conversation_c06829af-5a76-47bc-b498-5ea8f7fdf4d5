import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  inject,
  OnDestroy,
  OnInit,
  signal,
  ViewEncapsulation,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  DialogComponent,
  DialogRef,
  DialogTitleBarComponent,
} from '@progress/kendo-angular-dialog'
import { LabelComponent } from '@progress/kendo-angular-label'
import { SVGIconComponent } from '@progress/kendo-angular-icons'
import { chevronLeftIcon } from '@progress/kendo-svg-icons'
import { WINDOW } from '@venio/data-access/iframe-messenger'
import { HttpClient } from '@angular/common/http'
import { Subject, take, takeUntil } from 'rxjs'
import { SkeletonComponent } from '@progress/kendo-angular-indicators'
import { environment } from '@venio/shared/environments'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { ContentType } from '../login-container/login-container.component'

@Component({
  selector: 'venio-login-terms',
  standalone: true,
  imports: [
    CommonModule,
    DialogComponent,
    DialogTitleBarComponent,
    LabelComponent,
    SVGIconComponent,
    SkeletonComponent,
  ],
  templateUrl: './login-terms.component.html',
  styleUrl: './login-terms.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class LoginTermsComponent implements AfterViewInit, OnDestroy, OnInit {
  private readonly toDestroy$ = new Subject<void>()

  public readonly iconLeft = chevronLeftIcon

  private readonly dialogRef = inject(DialogRef)

  private readonly windowRef = inject(WINDOW)

  private readonly httpClient = inject(HttpClient)

  public readonly isEndUserLicenseAgreementLoading = signal(true)

  public readonly endUserLicenseAgreement = signal<string>('')

  public contentType: ContentType

  public ngOnInit(): void {
    this.#fetchEndUserLicenseAgreement()
  }

  public ngAfterViewInit(): void {
    this.#handleDialogTitleBarAction()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public closeDialog(): void {
    this.dialogRef.close()
  }

  #handleDialogTitleBarAction(): void {
    const button = this.windowRef.document.getElementsByClassName(
      'k-dialog-titlebar-action'
    )
    if (button.length) {
      button[0].addEventListener('click', () => {
        this.dialogRef.close()
      })
    }
  }

  private get _apiUrl(): string {
    return environment.apiUrl
  }

  #fetchEndUserLicenseAgreement(): void {
    let obs
    if (this.contentType === ContentType.PRIVACY_POLICY)
      obs = this.httpClient.get(this._apiUrl + 'login/policy')
    else obs = this.httpClient.get(this._apiUrl + 'login/terms-of-service')

    obs.pipe(take(1), takeUntil(this.toDestroy$)).subscribe({
      next: (response: ResponseModel) => {
        this.endUserLicenseAgreement.set(response.data)
        this.isEndUserLicenseAgreementLoading.set(false)
      },
      error: (error: unknown) => {
        this.endUserLicenseAgreement.set(
          'Failed to load the data. Please try again later.'
        )
        this.isEndUserLicenseAgreementLoading.set(false)
      },
    })
  }
}
