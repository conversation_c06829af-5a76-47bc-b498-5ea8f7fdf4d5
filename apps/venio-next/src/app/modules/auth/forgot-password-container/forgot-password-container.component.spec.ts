import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ForgotPasswordContainerComponent } from './forgot-password-container.component'
import {
  AppIdentitiesTypes,
  IframeMessengerModule,
  WINDOW,
  windowFactory,
} from '@venio/data-access/iframe-messenger'
import { EffectsModule } from '@ngrx/effects'
import { StoreModule } from '@ngrx/store'
import { provideMockStore } from '@ngrx/store/testing'
import { provideAnimations } from '@angular/platform-browser/animations'
import {
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA,
  PLATFORM_ID,
} from '@angular/core'
import { DialogRef } from '@progress/kendo-angular-dialog'
import { AuthFacade } from '@venio/data-access/auth'
import { ActivatedRoute } from '@angular/router'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { of } from 'rxjs'

describe('ForgotPasswordContainerComponent', () => {
  let component: ForgotPasswordContainerComponent
  let fixture: ComponentFixture<ForgotPasswordContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        ForgotPasswordContainerComponent,
        IframeMessengerModule.forRoot({
          origin: '*',
          iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
        }),
        EffectsModule.forRoot([]),
        StoreModule.forRoot({}),
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
      providers: [
        provideMockStore({}),
        provideAnimations(),
        {
          provide: WINDOW,
          useFactory: windowFactory,
          deps: [PLATFORM_ID],
        },
        {
          provide: DialogRef,
          useValue: {
            close: jest.fn(),
          },
        },
        {
          provide: AuthFacade,
          useValue: {
            login: jest.fn(),
            fetchLoginSettings: jest.fn(),
            selectLoginSettingSuccess$: of(undefined),
          },
        },
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                returnUrl: '/test',
              },
            },
          },
        },
        provideHttpClient(),
        provideHttpClientTesting(),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ForgotPasswordContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
