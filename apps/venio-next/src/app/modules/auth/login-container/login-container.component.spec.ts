import { ComponentFixture, TestBed } from '@angular/core/testing'
import { LoginContainerComponent } from './login-container.component'
import {
  AppIdentitiesTypes,
  IframeMessengerModule,
  WINDOW,
  windowFactory,
} from '@venio/data-access/iframe-messenger'
import { EffectsModule } from '@ngrx/effects'
import { StoreModule } from '@ngrx/store'
import { provideMockStore } from '@ngrx/store/testing'
import { provideAnimations } from '@angular/platform-browser/animations'
import { PLATFORM_ID } from '@angular/core'
import { DialogRef } from '@progress/kendo-angular-dialog'
import { AuthFacade } from '@venio/data-access/auth'
import { ActivatedRoute } from '@angular/router'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { of } from 'rxjs'
import {
  ControlSettingModel,
  ControlSettingService,
} from '@venio/data-access/control-settings'

const mockControlSettingService = {
  getControlSetting: {
    LOGIN_LOGO_PATH: 'mocked/path/to/logo.png',
  } as ControlSettingModel,
}

describe('LoginContainerComponent', () => {
  let component: LoginContainerComponent
  let fixture: ComponentFixture<LoginContainerComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        LoginContainerComponent,
        IframeMessengerModule.forRoot({
          origin: '*',
          iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
        }),
        EffectsModule.forRoot([]),
        StoreModule.forRoot({}),
      ],
      providers: [
        provideMockStore({}),
        provideAnimations(),
        {
          provide: WINDOW,
          useFactory: windowFactory,
          deps: [PLATFORM_ID],
        },
        {
          provide: DialogRef,
          useValue: {
            close: jest.fn(),
          },
        },
        {
          provide: AuthFacade,
          useValue: {
            selectLoginSuccess$: of(undefined),
            selectLoginError$: of(undefined),
            selectCurrentUserDetails$: of(undefined),
            login: jest.fn(),
          },
        },
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                returnUrl: '/test',
              },
            },
          },
        },
        provideHttpClient(),
        provideHttpClientTesting(),
        {
          provide: ControlSettingService,
          useValue: mockControlSettingService,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(LoginContainerComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
