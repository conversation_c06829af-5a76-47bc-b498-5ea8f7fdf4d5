import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  isDevMode,
  OnD<PERSON>roy,
  signal,
  ViewChild,
  ViewContainerRef,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { DialogService, DialogsModule } from '@progress/kendo-angular-dialog'
import { InputsModule, TextBoxComponent } from '@progress/kendo-angular-inputs'
import { eyeIcon, eyeSlashIcon } from '@progress/kendo-svg-icons'
import {
  FormBuilder,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms'
import { LabelModule } from '@progress/kendo-angular-label'
import { IconsModule } from '@progress/kendo-angular-icons'
import { ButtonComponent } from '@progress/kendo-angular-buttons'
import { environment } from '@venio/shared/environments'
import { LoaderComponent } from '@progress/kendo-angular-indicators'
import {
  AuthFacade,
  AuthService,
  TokenResponseModel,
} from '@venio/data-access/auth'
import {
  combineLatest,
  debounceTime,
  EMPTY,
  filter,
  Observable,
  Subject,
  switchMap,
  take,
  takeUntil,
  tap,
} from 'rxjs'
import {
  AppIdentitiesTypes,
  IframeMessengerService,
  MessageContent,
  MessageType,
} from '@venio/data-access/iframe-messenger'
import { ActivatedRoute } from '@angular/router'
import { LoginResetPasswordComponent } from '../login-reset-password/login-reset-password.component'
import { LoginForgotPasswordComponent } from '../login-forgot-password/login-forgot-password.component'
import { CookieService } from 'ngx-cookie-service'
import { catchError, finalize, map } from 'rxjs/operators'
import { ModuleLoginStateService, UserFacade } from '@venio/data-access/common'
import { NotificationService, Type } from '@progress/kendo-angular-notification'
import {
  ControlSettingModel,
  ControlSettingService,
} from '@venio/data-access/control-settings'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { HttpErrorResponse } from '@angular/common/http'
import { ResponseModel } from '@venio/shared/models/interfaces'

interface LoginFormModel {
  username: FormControl<string>
  password: FormControl<string>
  rememberMe: FormControl<boolean>
  verificationCode: FormControl<string>
  remember2FA: FormControl<boolean>
}
@Component({
  selector: 'venio-login-form',
  standalone: true,
  imports: [
    CommonModule,
    InputsModule,
    LabelModule,
    DialogsModule,
    IconsModule,
    ButtonComponent,
    ReactiveFormsModule,
    LoaderComponent,
    SvgLoaderDirective,
  ],
  providers: [CookieService],
  templateUrl: './login-form.component.html',
  styleUrl: './login-form.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LoginFormComponent implements AfterViewInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  @ViewChild('username')
  public username: TextBoxComponent

  private readonly cookieService = inject(CookieService)

  private readonly activatedRoute = inject(ActivatedRoute)

  private readonly authFacade = inject(AuthFacade)

  private readonly iframeMessengerService = inject(IframeMessengerService)

  private readonly dialogService = inject(DialogService)

  private readonly authService = inject(AuthService)

  private readonly userFacade = inject(UserFacade)

  private readonly controlSettingService = inject(ControlSettingService)

  private readonly viewContainerRef = inject(ViewContainerRef)

  private readonly formBuilder = inject(FormBuilder)

  private readonly notificationService = inject(NotificationService)
  private readonly moduleLoginState = inject(ModuleLoginStateService)

  public readonly iconEye = eyeIcon

  public readonly iconSlashEye = eyeSlashIcon

  public loginFormGroup: FormGroup<LoginFormModel>

  public readonly isLoginLoading = signal(false)

  public readonly togglePasswordVisibility = signal(false)

  public readonly isResetPasswordDialogVisible = signal(false)

  public readonly baseSettingInfo = signal<any>(undefined)

  public readonly venioVersion = signal<string>('')

  public showAzureLoginOption = false

  public showOktaLoginOption = false

  public isADEnabled = false

  /**
   * The environment variables are automatically populated based on app-settings.json is loaded.
   * To get the updated values, use the `computed` method to recompute the value.
   */
  public readonly appVersion = computed(() => {
    if (isDevMode()) {
      // In development mode, we want to show the full version string e.g., v1.0.0-build_1234
      return this.venioVersion() || environment.version || ''
    }

    // In production mode, we only want to show the version number e.g., v1.0.0
    return this.venioVersion() || environment.version.split('-')[0] || ''
  })

  /**
   * Since the parent app can send a query parameter to indicate that the session has expired,
   * We use this computed property to check if the session has expired. Later when the new application
   * is built, this property may not be needed or may be implemented differently.
   *
   * @returns {boolean} - True if the session has expired, otherwise false
   */
  private readonly isSessionExpired = computed(
    () =>
      this.activatedRoute.snapshot.queryParams['session']?.toLowerCase() ===
      'expire'
  )

  public isTwoFactorAuthenticationNotificationSent = signal(false)

  public tokenResponse: TokenResponseModel

  public get formControls(): LoginFormModel {
    return this.loginFormGroup?.controls
  }

  private get controlSettings(): ControlSettingModel {
    return this.controlSettingService.getControlSetting
  }

  public readonly loginServerMessage = signal<string>('')

  public readonly verificationFailedMessage = signal<string>('')

  public readonly verificationCodeSentNotificationMessage = signal(
    'Verification code has been sent to your email ID'
  )

  private hasVerificationCodeReSent = false

  public isUserLocked = signal<boolean>(false)

  public idpLoginUrl: string

  constructor() {
    this.#initForm()
    this.#selectBaseSettingInfo()
    this.#selectNotificationSentMessage()
    this.moduleLoginState.reset()
  }

  public ngAfterViewInit(): void {
    this.#selectLoginResponses()
    this.#selectLoginErrorResponse()
    this.#selectTwoFactorVerificationResponses()
    this.#focusOnUsername()
    this.#checkSessionExpired()
    this.#selectParentNotifiedMessage()

    const params = this.activatedRoute.snapshot.queryParams
    if (params['status'] === 'failed') {
      const errorMessage: string = params['error']
      this.#showMessage(errorMessage, { style: 'error' })
    }
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public loginClick(): void {
    this.loginServerMessage.set('')

    if (this.isLoginLoading()) {
      return
    }

    Object.entries(this.formControls).forEach(([key, control]) => {
      if (key === 'verificationCode') {
        if (this.isTwoFactorAuthenticationNotificationSent()) {
          control.markAsTouched()
          control.markAsDirty()
        }
      } else control.markAsTouched()
      control.markAsDirty()
    })

    if (this.loginFormGroup.invalid) {
      return
    }

    this.isLoginLoading.set(true)
    if (this.isTwoFactorAuthenticationNotificationSent()) {
      this.#verifyTwoFA()
    } else {
      const { username, rememberMe, password } =
        this.loginFormGroup.getRawValue()

      if (!username.trim() || !password.trim()) {
        this.isLoginLoading.set(false)
        return
      }

      this.authFacade.login({
        password,
        username,
        rememberMe,
      })
    }
  }

  public openPasswordResetDialog(
    isPasswordExpired = false,
    remarkMessage = ''
  ): void {
    // If the dialog is already open, we don't want to open it again
    if (this.isResetPasswordDialogVisible()) return

    this.isResetPasswordDialogVisible.set(true)
    const ref = this.dialogService.open({
      appendTo: this.viewContainerRef,
      content: LoginResetPasswordComponent,
      autoFocusedElement: 'kendo-textbox',
      minWidth: 250,
      maxWidth: 398,
      width: '60%',
    })

    ref.dialog.onDestroy(() => {
      this.isResetPasswordDialogVisible.set(undefined)
    })

    ref.result.pipe(take(1), takeUntil(this.toDestroy$)).subscribe((_) => {
      this.isResetPasswordDialogVisible.set(undefined)
    })

    if (!isPasswordExpired) return

    ref.content.instance.passwordResetFormGroup.patchValue({
      userName: this.formControls.username.value,
      oldPassword: this.formControls.password.value,
      isPasswordExpired,
      remarkMessage,
    })

    ref.content.instance.passwordResetFormGroup.controls.userName.disable()

    setTimeout(() => {
      ref.content.instance.newPasswordControl?.focus()
    })
  }

  public openForgotPasswordDialog(): void {
    this.dialogService.open({
      appendTo: this.viewContainerRef,
      content: LoginForgotPasswordComponent,
      maxHeight: 416,
      width: '398px',
    })
  }

  public passwordVisibilityClicked(event: KeyboardEvent): void {
    event.preventDefault()
    event.stopPropagation()
    this.togglePasswordVisibility.set(!this.togglePasswordVisibility())
  }

  #showMessage(content = '', type: Type): void {
    if (!content?.trim()) return

    const notificationRef = this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 10000,
      width: 300,
    })

    // When the user clicks on the notification, it should hide
    notificationRef.notification.location.nativeElement.onclick = (): void => {
      notificationRef.hide()
    }
  }

  #initForm(): void {
    this.loginFormGroup = this.formBuilder.group<LoginFormModel>({
      username: this.formBuilder.control('', {
        validators: [Validators.required],
      }),
      password: this.formBuilder.control('', {
        validators: [Validators.required],
      }),
      rememberMe: this.formBuilder.control(false),
      verificationCode: this.formBuilder.control('', { validators: [] }),
      remember2FA: this.formBuilder.control(false),
    })
  }

  #focusOnUsername(): void {
    setTimeout(() => {
      this.username.focus()
    }, 300)
  }

  #checkSessionExpired(): void {
    this.loginServerMessage.set('')
    if (this.isSessionExpired()) {
      this.loginServerMessage.set('Session has expired, Please login again!')
    }
  }

  #get2FAActiveOption(): { is2FAEnabled: boolean; isCodeRemembered: boolean } {
    const is2FAEnabled =
      this.baseSettingInfo()?.isTwoFactorAuthenticationEnabled
    const isCodeRemembered = this.cookieService.get('rememberCode') === 'true'
    return { is2FAEnabled, isCodeRemembered }
  }

  /**
   * Subscribes to the login success stream and processes authentication responses.
   *
   * This method listens for successful logins, retrieves credentials and user details,
   * and then handles password resets, 2FA notifications, and parent notifications.
   *
   * @returns {void}
   */
  #selectLoginResponses(): void {
    const loginState = { processed: false, twoFANotified: false }
    let shallNotify: any = null

    this.authFacade.selectLoginSuccess$
      .pipe(
        filter((success) => !!success),
        tap((data: any) => {
          this.userFacade.fetchCurrentUser()
          this.tokenResponse = data
        }),
        switchMap(() => this.#fetchCredentialsAndUserDetails()),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: ({ credentials, userDetail }) => {
          this.#processLoginResponse(
            { credentials, userDetail },
            loginState,
            (timeoutId: number) => {
              shallNotify = timeoutId
            }
          )
        },
        error: (error: unknown) => {
          this.isLoginLoading.set(false)
          clearTimeout(shallNotify)
        },
      })
  }

  /**
   * Retrieves authentication credentials and user details after login success.
   *
   * This method extracts the password from the login form, sets the loading state,
   * fetches the credentials, and then combines them with user details from the store.
   * In case of errors, it delegates to the error handler.
   *
   * @returns {Observable<Object>} An observable that emits combined credentials and user detail.
   */
  #fetchCredentialsAndUserDetails(): Observable<{
    credentials: any
    userDetail: any
  }> {
    const formData = this.loginFormGroup.getRawValue()
    const password = formData.password || ''
    this.isLoginLoading.set(true)

    return this.authService.fetchAuthenticationCredentials(password).pipe(
      catchError((err: unknown) => this.#handleFetchCredentialsError(err)),
      switchMap((response: any) => {
        if (this.isADEnabled) {
          this.userFacade.fetchCurrentUser()
        }
        return this.userFacade.selectCurrentUserDetails$.pipe(
          filter((userDetail: any) => !!userDetail),
          take(1),
          map((userDetail: any) => ({
            credentials: response.data,
            userDetail,
          })),
          finalize(() => this.#finalizeCredentialsFetch())
        )
      })
    )
  }

  /**
   * Handles errors that occur during the fetch of authentication credentials.
   *
   * Updates the loading state and server message based on the HTTP error received,
   * then terminates the stream by returning an empty observable.
   *
   * @param {unknown} err - The error object from the failed credentials fetch.
   * @returns {Observable<never>} An empty observable to terminate further processing.
   */
  #handleFetchCredentialsError(err: unknown): Observable<never> {
    this.isLoginLoading.set(false)
    const httpError = err as HttpErrorResponse
    if (httpError.error.data.remark) {
      this.loginServerMessage.set(httpError.error.data.remark)
    } else {
      this.loginServerMessage.set(httpError.error.message)
    }
    return EMPTY
  }

  /**
   * Finalizes the credentials fetch process.
   *
   * Checks the 2FA active options and, if 2FA is enabled and the code is not remembered,
   * clears the authentication tokens.
   *
   * @returns {void}
   */
  #finalizeCredentialsFetch(): void {
    const { is2FAEnabled, isCodeRemembered } = this.#get2FAActiveOption()
    if (is2FAEnabled && !isCodeRemembered) {
      this.authService.clearTokens()
    }
  }

  /**
   * Processes the combined authentication credentials and user details.
   *
   * This method determines if the user needs to reset their password,
   * handles two-factor authentication (2FA) notifications, and triggers
   * a delayed notification to the parent if login is successful.
   *
   * @param {Object} response - The combined credentials and user detail.
   * @param {Object} loginState - Object tracking whether login processing and 2FA notification have occurred.
   * @param {(timeoutId: number) => void} setNotifyTimeout - Callback to capture the timeout ID from the delayed notifier.
   * @returns {void}
   */
  #processLoginResponse(
    response: { credentials: any; userDetail: any },
    loginState: { processed: boolean; twoFANotified: boolean },
    setNotifyTimeout: (timeoutId: number) => void
  ): void {
    const { credentials, userDetail } = response
    if (!userDetail || loginState.processed) {
      this.isLoginLoading.set(false)
      return
    }

    const { passwordExpired } = credentials || {}
    const { forceUserToChangePassword } = userDetail || {}

    if (
      (forceUserToChangePassword || passwordExpired) &&
      !this.isResetPasswordDialogVisible()
    ) {
      this.isLoginLoading.set(false)
      this.#showMessage(
        forceUserToChangePassword
          ? 'You are required to change your password.'
          : passwordExpired
          ? 'Password has expired'
          : '',
        { style: 'warning' }
      )
      this.openPasswordResetDialog(true, '')
      return
    }

    const { is2FAEnabled, isCodeRemembered } = this.#get2FAActiveOption()

    if (is2FAEnabled && !isCodeRemembered && !loginState.twoFANotified) {
      this.isLoginLoading.set(false)
      this.#send2FANotification(true)
      loginState.twoFANotified = true
    } else {
      if (
        (!is2FAEnabled && !this.isResetPasswordDialogVisible()) ||
        (isCodeRemembered &&
          !this.isResetPasswordDialogVisible() &&
          is2FAEnabled)
      ) {
        setNotifyTimeout(this.#executeLoginNotifier(loginState))
      }
    }
  }

  /**
   * Executes a delayed notification for a successful login.
   *
   * Sets the login as processed and, after a short delay, persists the tokens
   * (if applicable) and notifies the parent window about the successful login.
   *
   * @param {{ processed: boolean }} loginState - Object tracking if the login has been processed.
   * @returns {number} The timeout ID for the delayed notification.
   */
  #executeLoginNotifier(loginState: { processed: boolean }): number {
    loginState.processed = true
    return setTimeout(() => {
      const { is2FAEnabled, isCodeRemembered } = this.#get2FAActiveOption()
      if (!is2FAEnabled || isCodeRemembered) {
        this.authService.setTokens(this.tokenResponse)
      }
      if (!this.isResetPasswordDialogVisible()) {
        this.#notifyParentLoginSuccess(this.tokenResponse)
      }
    }, 300) as unknown as number
  }

  #selectLoginErrorResponse(): void {
    this.authFacade.selectLoginError$
      .pipe(
        filter((error) => !!error),
        takeUntil(this.toDestroy$)
      )
      .subscribe((error) => {
        this.isLoginLoading.set(false)
        const isError = error?.['error']?.toLowerCase()?.length > 0
        if (isError) {
          this.loginServerMessage.set(error['error'])
        }
      })
  }

  #selectTwoFactorVerificationResponses(): void {
    // Timeout to notify the parent window
    let shallNotify = null
    combineLatest([
      this.authFacade.selectTwoFactorVerificationSuccess$,
      this.authFacade.selectTwoFactorVerificationFailure$,
    ])
      .pipe(
        filter(([success, error]) => Boolean(success) || Boolean(error)),
        tap(([data, error]) => {
          const isError = error?.['error']?.toLowerCase()?.length > 0
          if (isError) {
            this.loginServerMessage.set(error['error'])
          }
        }),
        debounceTime(10),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([data, _]) => {
        // Clear out the timer if it's set
        if (shallNotify) clearTimeout(shallNotify)
        this.isLoginLoading.set(false)
        this.isUserLocked.set(data?.isUserLocked)

        if (data?.isVerificationSuccessful) {
          const { remember2FA } = this.loginFormGroup.getRawValue()
          if (remember2FA) {
            this.cookieService.set('rememberCode', 'true', 30)
          }
          // Keep tokens set for successful verification
          shallNotify = setTimeout(
            () => this.#notifyParentLoginSuccess(this.tokenResponse),
            300
          )
        } else {
          this.verificationFailedMessage.set(data?.message)
          // Clear tokens on verification failure
          this.#manageTokens(false)
        }
      })
  }

  /**
   * Notify the parent window that the login was successful. In the future, once the new application is
   * built, this method will be removed.
   * @param {Object} tokenData - The token data returned from the login API
   * @param {boolean} isLoginSuccess - Flag to indicate if the login was successful
   * @returns {void}
   */
  #notifyParentLoginSuccess(tokenData: unknown, isLoginSuccess = true): void {
    /**
     * The Parent window will navigate to the launchpad page
     * Depending on logic, this can be changed to a different page
     * If there is any additional logic or route that user needs to be navigated,
     * maybe from here or from the parent app itself can be decided.
     */
    const navigateTo = '/launchpad/caselaunchpad'

    const formData = this.loginFormGroup.getRawValue()

    formData.password = this.authService.encryptStr(
      formData.password,
      tokenData['access_token']
    )

    // When `remember me` flag is checked, we need to remember this user for the next
    // REMEMBER_ME_TIME_SPAN days
    if (formData.rememberMe) {
      this.cookieService.set(
        'rememberMe',
        'true',
        this.controlSettings.REMEMBER_ME_TIME_SPAN
      )
    }

    this.iframeMessengerService.sendMessage({
      type: 'MICRO_APP_DATA_CHANGE',
      eventTriggeredBy: AppIdentitiesTypes.VENIO_NEXT,
      eventTriggeredFor: 'PARENT_WINDOW',
      iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
      payload: {
        type: MessageType.ROUTE_CHANGE,
        content: {
          isLoginSuccess,
          navigateTo,
          tokenData,
          formData,
          rememberMeTimeSpan: this.controlSettings.REMEMBER_ME_TIME_SPAN,
        },
      } as MessageContent,
    })
  }

  #selectBaseSettingInfo(): void {
    this.authService
      .fetchBaseSettingsInfo()
      .pipe(
        tap((data) => {
          const version = data['venioVersion']
          this.venioVersion.set(version)
          this.baseSettingInfo.set(data)
          this.showAzureLoginOption =
            data['isIdpEnabled'] && !data['isOktaIdpEnabled']
          this.showOktaLoginOption = data['isOktaIdpEnabled']
          this.isADEnabled = data['isADEnabled']
        }),
        switchMap((data) =>
          this.showAzureLoginOption
            ? this.authService.CreateSamlRequest('AZURE_AD').pipe(take(1))
            : this.showOktaLoginOption
            ? this.authService.CreateSamlRequest('OKTA').pipe(take(1))
            : EMPTY
        ),
        takeUntil(this.toDestroy$)
      )
      .subscribe({
        next: (response: ResponseModel) => {
          this.idpLoginUrl = response.data
        },
        error: (ex: unknown) => {
          const error = (ex as HttpErrorResponse).error
          this.#showMessage(
            error['message'] ||
              'An error occurred while processing your request',
            { style: 'error' }
          )
        },
      })
  }

  /**
   * Select the parent notified message to open the change password dialog.
   * @returns {void}
   */
  #selectParentNotifiedMessage(): void {
    this.iframeMessengerService.messageReceived
      .pipe(
        filter(
          (m) =>
            m.eventTriggeredBy === AppIdentitiesTypes.VOD &&
            m.eventTriggeredFor === 'FRAME_WINDOW' &&
            (m.payload as MessageContent)?.content?.[
              'openChangeCurrentPassword'
            ] &&
            (m.payload as MessageContent)?.type === MessageType.NOTIFY_CHANGE &&
            !this.isResetPasswordDialogVisible()
        ),
        map((mc) => (mc.payload as MessageContent)?.content),
        takeUntil(this.toDestroy$)
      )
      .subscribe((message: any) => {
        if (message['remarkMessage']) {
          this.#showMessage(message['remarkMessage'], { style: 'error' })
        }

        this.openPasswordResetDialog(true, '')
      })
  }

  #selectNotificationSentMessage(): void {
    this.authFacade.selectTwoFactorNotificationSent$
      ?.pipe(takeUntil(this.toDestroy$))
      .subscribe((isNotificationSent: boolean) => {
        if (isNotificationSent) {
          this.authFacade.resetAuthState('isTwoFactorNoticationSent')

          if (this.hasVerificationCodeReSent)
            this.verificationCodeSentNotificationMessage.set(
              'Verification code resent successfully'
            )
          this.isTwoFactorAuthenticationNotificationSent.set(true)
          this.#handle2FAFormControls()
        }
      })
  }

  #handle2FAFormControls(): void {
    this.formControls.username.disable()
    this.formControls.password.disable()
    this.formControls.rememberMe.disable()
    this.formControls.verificationCode.addValidators(Validators.required)
  }

  /**
   * Comprehensive token management method to centralize token setting logic
   * @param {boolean} shouldSetToken - Whether to set or clear the token
   * @param {number} clearDelay - Delay before clearing tokens if needed
   * @returns {void}
   */
  #manageTokens(shouldSetToken: boolean, clearDelay = 0): void {
    if (shouldSetToken && this.tokenResponse) {
      this.authService.setTokens(this.tokenResponse)
    } else if (!shouldSetToken) {
      if (clearDelay > 0) {
        setTimeout(() => this.authService.clearTokens(), clearDelay)
      } else {
        this.authService.clearTokens()
      }
    }
  }

  /**
   * Sends 2FA notification with proper token management
   * @param {boolean} resend - Whether this is a resend request
   * @returns {void}
   */
  #send2FANotification(resend: boolean): void {
    // Set tokens before sending notification
    this.#manageTokens(true)

    // Send notification
    this.authFacade.sendTwoFactorAuthenticationNotification(resend)

    // Clear tokens after a short delay to ensure request completes
    const { is2FAEnabled, isCodeRemembered } = this.#get2FAActiveOption()
    if (is2FAEnabled && !isCodeRemembered) {
      this.#manageTokens(false, 100)
    }
  }

  #verifyTwoFA(): void {
    // Set tokens for verification request
    this.#manageTokens(true)

    const { verificationCode, remember2FA } = this.loginFormGroup.getRawValue()
    this.authFacade.verifyTwoFactorAuthenticationCode(
      verificationCode,
      remember2FA
    )
  }

  public resendVerificationCode(): void {
    this.hasVerificationCodeReSent = true
    this.verificationFailedMessage.set('')
    this.formControls.verificationCode.setValue('')
    this.formControls.verificationCode.markAsUntouched()

    this.#send2FANotification(true)
  }

  public redirectToIdPLogin(): void {
    //For Azure AD authentication endpoints (login.microsoftonline.com) explicitly block loading within an iFrame due to security restrictions.
    //hence send message to parent window and then redirect to the Azure login URL
    const idpLoginUrl = this.idpLoginUrl
    this.iframeMessengerService.sendMessage({
      type: 'MICRO_APP_DATA_CHANGE',
      eventTriggeredBy: AppIdentitiesTypes.VENIO_NEXT,
      eventTriggeredFor: 'PARENT_WINDOW',
      iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
      payload: {
        type: MessageType.IDP_AUTHENTICATE,
        content: {
          idpLoginUrl,
        },
      } as MessageContent,
    })
  }
}
