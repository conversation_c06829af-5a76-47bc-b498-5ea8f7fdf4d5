import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  inject,
  On<PERSON><PERSON>roy,
  signal,
  ViewChild,
} from '@angular/core'
import { CommonModule, NgOptimizedImage } from '@angular/common'
import { FormsModule, NgForm } from '@angular/forms'
import {
  DialogRef,
  DialogTitleBarComponent,
} from '@progress/kendo-angular-dialog'
import { ButtonComponent } from '@progress/kendo-angular-buttons'
import { SVGIconComponent } from '@progress/kendo-angular-icons'
import { LabelComponent } from '@progress/kendo-angular-label'
import {
  FormFieldComponent,
  TextBoxComponent,
} from '@progress/kendo-angular-inputs'
import { LoaderComponent } from '@progress/kendo-angular-indicators'
import { WINDOW } from '@venio/data-access/iframe-messenger'
import { chevronLeftIcon } from '@progress/kendo-svg-icons'
import { AuthService } from '@venio/data-access/auth'
import { Subject, take, takeUntil } from 'rxjs'
import { HttpErrorResponse } from '@angular/common/http'
import { NotificationService, Type } from '@progress/kendo-angular-notification'
interface PasswordResetResponseModel {
  type: 'error' | 'success'
  message: string
}
@Component({
  selector: 'venio-login-forgot-password',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ButtonComponent,
    DialogTitleBarComponent,
    SVGIconComponent,
    LabelComponent,
    FormFieldComponent,
    TextBoxComponent,
    LoaderComponent,
    NgOptimizedImage,
  ],
  templateUrl: './login-forgot-password.component.html',
  styleUrl: './login-forgot-password.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LoginForgotPasswordComponent implements AfterViewInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  private readonly notificationService = inject(NotificationService)

  @ViewChild('usernameInput')
  public usernameControl: TextBoxComponent

  public username = signal('')

  public isLinkRequesting = signal(false)

  public resetLinkRequestResponse =
    signal<PasswordResetResponseModel>(undefined)

  public readonly iconLeft = chevronLeftIcon

  private dialogRef = inject(DialogRef)

  private windowRef = inject(WINDOW)

  private readonly authService = inject(AuthService)

  public ngAfterViewInit(): void {
    this.handleDialogTitleBarAction()
    this.#focusUsernameInput()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public closeDialog(): void {
    this.dialogRef.close()
  }

  private handleDialogTitleBarAction(): void {
    const button = this.windowRef.document.getElementsByClassName(
      'k-dialog-titlebar-action'
    )
    if (button.length) {
      button[0].addEventListener('click', () => {
        this.dialogRef.close()
      })
    }
  }

  public forgetPasswordClick(form: NgForm): void {
    if (this.isLinkRequesting()) {
      return
    }

    if (form.invalid || !this.username()?.trim()) {
      form.controls['username'].markAsTouched()
      form.controls['username'].markAsDirty()
      return
    }

    this.isLinkRequesting.set(true)

    this.#requestPasswordReset()
  }

  #showMessage(content = '', type: Type): void {
    if (!content?.trim()) return

    const notificationRef = this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 10000,
      width: 300,
    })

    // When the user clicks on the notification, it should hide
    notificationRef.notification.location.nativeElement.onclick = (): void => {
      notificationRef.hide()
    }
  }

  #focusUsernameInput(): void {
    setTimeout(() => {
      this.usernameControl.focus()
    }, 300)
  }

  #requestPasswordReset(): void {
    this.authService
      .passwordResetLinkRequest(this.username())
      .pipe(take(1), takeUntil(this.toDestroy$))
      .subscribe({
        next: (response) => {
          this.isLinkRequesting.set(false)
          this.resetLinkRequestResponse.set({
            type: response.data['isResetPasswordLinkSent']
              ? 'success'
              : 'error',
            message: response.data['message'],
          })
        },
        error: (ex: unknown) => {
          const error = (ex as HttpErrorResponse).error
          this.isLinkRequesting.set(false)

          this.#showMessage(
            error['message'] ||
              'An error occurred while processing your request',
            { style: 'error' }
          )
        },
      })
  }
}
