import {
  ChangeDetectionStrategy,
  Component,
  OnDestroy,
  OnInit,
  Type,
  ViewContainerRef,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { UtilityPanelFacade } from '@venio/data-access/document-utility'
import {
  DialogRef,
  DialogService,
  DialogsModule,
} from '@progress/kendo-angular-dialog'
import { filter, Subject, takeUntil } from 'rxjs'
import { UtilityPanelType } from '@venio/shared/models/constants'

@Component({
  selector: 'venio-document-utility-filter-panel-container',
  standalone: true,
  imports: [CommonModule, DialogsModule],
  templateUrl: './document-utility-filter-panel-container.component.html',
  styleUrl: './document-utility-filter-panel-container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentUtilityFilterPanelContainerComponent
  implements OnInit, OnDestroy
{
  private readonly toDestroy$ = new Subject<void>()

  private dialogRef: DialogRef

  public panelFilterEventType: UtilityPanelType

  constructor(
    private utilityPanelFacade: UtilityPanelFacade,
    private dialogService: DialogService,
    private vcr: ViewContainerRef
  ) {}

  public ngOnInit(): void {
    this.#selectedPanelFilterEvent()
  }

  #resetPanelFilterEventState(): void {
    this.utilityPanelFacade.resetUtilityPanelState('panelFilterEvent')
  }

  #handleEditDialogCloseEvent(): void {
    this.dialogRef.dialog.onDestroy(() => {
      this.#resetPanelFilterEventState()
    })
  }

  #launchDialogContent(dialogContent: Type<unknown>): void {
    this.dialogRef = this.dialogService.open({
      appendTo: this.vcr,
      content: dialogContent,
      maxWidth: '900px',
      maxHeight: '650px',
      width: '60%',
      height: '90vh',
    })
    const dialogInstance = this.dialogRef.content.instance
    dialogInstance.panelFilterEventType = this.panelFilterEventType
  }

  #handleLazyLoadedDialog(): void {
    import(
      '../document-utility-filter-panel-dialog/document-utility-filter-panel-dialog.component'
    ).then((d) => {
      // launch the dialog
      this.#launchDialogContent(d.DocumentUtilityFilterPanelDialogComponent)
      // once the dialogRef instance is created
      this.#handleEditDialogCloseEvent()
    })
  }

  #selectedPanelFilterEvent(): void {
    this.utilityPanelFacade.selectPanelFilterEvent$
      .pipe(
        filter((panelFilterEventType) => Boolean(panelFilterEventType)),
        takeUntil(this.toDestroy$)
      )
      .subscribe((panelFilterEventType) => {
        this.panelFilterEventType = panelFilterEventType
        // launch the dialog
        this.#handleLazyLoadedDialog()
      })
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
    this.#resetPanelFilterEventState()
  }
}
