<div class="t-pr-3 t-pt-1">
  <!-- Textarea Field -->
  <ng-container
    *ngIf="field.shouldShowTextField && field.shouldShowField"
    [ngTemplateOutlet]="fieldTemplate"
    [ngTemplateOutletContext]="{ field: field, type: 'textarea' }">
  </ng-container>

  <!-- Dropdownlist Field -->
  <ng-container
    *ngIf="field.shouldShowPredefinedCodingField && field.shouldShowField"
    [ngTemplateOutlet]="fieldTemplate"
    [ngTemplateOutletContext]="{ field: field, type: 'dropdown' }">
  </ng-container>

  <!-- Multi Text Values Field -->
  <ng-container
    *ngIf="field.shouldShowMultipleCodingValuesField && field.shouldShowField"
    [ngTemplateOutlet]="fieldTemplate"
    [ngTemplateOutletContext]="{ field: field, type: 'multiText' }">
  </ng-container>

  <!-- Field Template -->
  <ng-template #fieldTemplate let-field="field" let-type="type">
    <div *ngIf="field.shouldShowField">
      <!-- Common Label Template -->
      <ng-container
        *ngTemplateOutlet="
          commonLabelTemplate;
          context: { field: field }
        "></ng-container>

      <!-- Field Content Based on Type -->
      <div [ngSwitch]="type">
        <ng-container *ngSwitchCase="'textarea'">
          <div class="t-flex t-justify-center t-items-center t-gap-4">
            <kendo-textarea
              class="t-w-full !t-border-gray-300"
              [rows]="1"
              [formControl]="control"></kendo-textarea>
          </div>
        </ng-container>
        <ng-container *ngSwitchCase="'dropdown'">
          <div class="input-group pr-4">
            <kendo-dropdownlist
              [formControl]="control"
              [data]="field.fieldCodingValues"
              [valuePrimitive]="true"
              class="t-w-full !t-border-gray-300"></kendo-dropdownlist>
          </div>
        </ng-container>
        <ng-container *ngSwitchCase="'multiText'">
          <div class="t-flex t-justify-center t-items-center t-gap-4">
            <ng-container
              *ngIf="field.allowMultipleCodingValues; else elseTemplate">
              <kendo-textarea
                class="t-w-full !t-border-gray-300"
                [rows]="1"
                [formControl]="control"></kendo-textarea>
              <ng-container
                *ngTemplateOutlet="
                  multiDocumentTemplate;
                  context: {
                    field: field,
                    action: isBulkDocument
                      ? codingActionEvent.MULTIDOCUMENT
                      : codingActionEvent.MULTIVALUE,
                    svgUrl: isBulkDocument
                      ? 'assets/svg/icon-duplicate-document.svg'
                      : 'assets/svg/icon-dot-menu-more.svg'
                  }
                "></ng-container>
            </ng-container>
            <ng-template #elseTemplate>
              <kendo-dropdownlist
                [formControl]="control"
                [data]="field.fieldCodingValues"
                [valuePrimitive]="true"
                class="t-w-full !t-border-gray-300"></kendo-dropdownlist>
            </ng-template>
          </div>
        </ng-container>
      </div>
      <!-- Error Messages -->
      <div
        *ngIf="control.errors && (control.touched || control.dirty)"
        class="t-text-error t-flex t-flex-col t-w-full t-py-1 t-px-2">
        <div *ngFor="let errorKey of getErrorKeys(control.errors)">
          {{ control.errors[errorKey].message }}
        </div>
      </div>
    </div>
  </ng-template>

  <!-- Common Label Template -->
  <ng-template #commonLabelTemplate let-field="field">
    <kendo-label
      kendoTooltip
      filter=".gridTooltip"
      [tooltipTemplate]="tooltip"
      position="right"
      class="t-text-xs t-uppercase t-mb-3 t-tracking-widest t-font-bold gridTooltip"
      (mouseenter)="codingInfoToggleTemplate(field)"
      (mouseleave)="clearCodingInfo()"
      id="codingInfo{{ field?.fieldName }}"
      for="field.fieldName">
      {{ field.displayName }}
    </kendo-label>
    <kendo-label
      *ngIf="field.checkForDateType"
      class="t-text-xs t-mb-3 t-tracking-widest t-pl-2"
      for="field.fieldName"
      >Date Format: {{ field.dateFormat }}</kendo-label
    >
    <kendo-label
      *ngIf="field.shouldShowDelimiter"
      class="t-text-xs t-mb-3 t-tracking-widest t-pl-2"
      for="field.fieldName"
      >Delimiter: "{{ field.extractedDelimiter }}"</kendo-label
    >
  </ng-template>

  <!-- Multi Document Template -->
  <ng-template
    #multiDocumentTemplate
    let-field="field"
    let-actionEvent="action"
    let-svgUrl="svgUrl">
    <div class="t-flex-none t-w-10">
      <button
        kendoButton
        #copyDocument
        fillMode="outline"
        (click)="setMultiCodingValue(actionEvent, field)"
        type="button"
        class="!t-w-full !t-border-[#ccc]"
        [attr.data-qa]="'multi-document-button-' + field.fieldName">
        <div
          [parentElement]="copyDocument.element"
          venioSvgLoader
          color="#979797"
          hoverColor="#6B7280"
          [svgUrl]="svgUrl"
          height="1.26rem"
          width="1rem">
          <kendo-loader size="small"></kendo-loader>
        </div>
      </button>
    </div>
  </ng-template>

  <!-- Tooltip Template -->
  <ng-template #tooltip let-anchor>
    <div class="t-flex t-w-full">
      <div class="t-flex t-flex-col" style>
        <div class="t-flex t-flex-col">
          Field Type:{{ currentField?.uiInputType }}
        </div>
        <div class="t-flex t-flex-col" *ngIf="currentField?.length">
          Length:<span class="t-font-bold">{{ currentField?.length }}</span>
        </div>
        <div class="t-flex t-flex-col" *ngIf="currentField?.scale">
          Scale:<span class="t-font-bold">{{ currentField?.scale }}</span>
        </div>
        <div
          class="t-flex t-flex-col"
          *ngIf="currentField?.delimiterForCodingValues">
          Delimiter:<span class="t-font-bold">{{
            currentField.extractedDelimiter
          }}</span>
        </div>
        <div class="t-flex t-flex-col" *ngIf="currentField.description">
          Description:{{ currentField.description }}
        </div>
      </div>
    </div>
  </ng-template>
</div>
