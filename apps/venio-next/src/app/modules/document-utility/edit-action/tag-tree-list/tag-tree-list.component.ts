import {
  ChangeDetectionStrategy,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
  signal,
  TrackByFunction,
  ViewChild,
} from '@angular/core'
import { CommonModule, NgForOf } from '@angular/common'
import {
  ButtonGroupModule,
  ButtonModule,
} from '@progress/kendo-angular-buttons'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import {
  RowReorderEvent,
  SelectableSettings,
  TreeListComponent,
  TreeListItem,
  TreeListModule,
} from '@progress/kendo-angular-treelist'
import {
  SvgLoaderDirective,
  UserGroupRightCheckDirective,
} from '@venio/feature/shared/directives'
import { TagsFacade } from '@venio/data-access/common'
import { ActivatedRoute } from '@angular/router'
import { combineLatest, debounceTime, filter, Subject, takeUntil } from 'rxjs'
import { cloneDeep } from 'lodash'
import { CommonActionTypes } from '@venio/shared/models/constants'
import { DialogRef, DialogService } from '@progress/kendo-angular-dialog'
import { ConfirmationDialogComponent } from '@venio/feature/notification'
import { TagReOrderModel, TagsModel } from '@venio/shared/models/interfaces'
import {
  NotificationModule,
  NotificationService,
  Type,
} from '@progress/kendo-angular-notification'
import { TooltipModule } from '@progress/kendo-angular-tooltip'
import { ReviewSetStateService, UserRights } from '@venio/data-access/review'
import { XMLUtil } from '@venio/util/utilities'

interface TagActionIconModel {
  isLoading: boolean
  actionType: CommonActionTypes
  allowedPermission: UserRights | UserRights[]
  selectedAction: null
  iconPath: string
}
@Component({
  selector: 'venio-tag-tree-list',
  standalone: true,
  imports: [
    NgForOf,
    CommonModule,
    ButtonGroupModule,
    ButtonModule,
    LoaderModule,
    SvgLoaderDirective,
    TreeListModule,
    NotificationModule,
    TooltipModule,
    UserGroupRightCheckDirective,
  ],
  templateUrl: './tag-tree-list.component.html',
  styleUrls: ['./tag-tree-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TagTreeListComponent implements OnInit, OnDestroy {
  public readonly toDestroy$ = new Subject<void>()

  public selectedAction = new Map<number, CommonActionTypes>()

  public selectedTag: TagsModel

  public isDataLoaded = signal<boolean>(false)

  public tagActionIcons = signal<TagActionIconModel[]>(
    this.#initializeTagSvgIcons()
  )

  public readonly commonActionTypes = CommonActionTypes

  @ViewChild('tagTreeComponent', { static: true })
  public tagTreeComponent: TreeListComponent

  public selectableSettings: SelectableSettings = {
    mode: 'row',
    multiple: true,
    drag: false,
  }

  public tagTree = signal<TagsModel[]>([])

  public clonedTagTree = signal<TagsModel[]>([])

  public isTagTreeLoading$ = this.tagsFacade.selectIsTagTreeLoading$

  private confirmationDialogRef: DialogRef

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  private reviewSetId: number

  constructor(
    private tagsFacade: TagsFacade,
    private activatedRoute: ActivatedRoute,
    private dialogService: DialogService,
    private notificationService: NotificationService,
    private reviewSetState: ReviewSetStateService
  ) {}

  #initializeTagSvgIcons(): Array<TagActionIconModel> {
    const actions = [
      CommonActionTypes.DELETE,
      CommonActionTypes.EDIT,
      CommonActionTypes.CLONE,
    ]
    const iconPaths = [
      'assets/svg/icon-tagedit-delete.svg',
      'assets/svg/icon-action-grid-pencil.svg',
      'assets/svg/icon-tagedit-copy.svg',
    ]

    return actions.map((action, index) => ({
      actionType: action,
      allowedPermission:
        action === CommonActionTypes.DELETE
          ? UserRights.DELETE_TAG
          : action === CommonActionTypes.EDIT
          ? UserRights.EDIT_EXISTING_TAG
          : action === CommonActionTypes.CLONE
          ? UserRights.ADD_NEW_TAG
          : [],
      iconPath: iconPaths[index],
      isLoading: false,
      selectedAction: null,
    }))
  }

  #resetTagDeletionState(): void {
    this.tagsFacade.resetTagsState([
      'isTagDeleting',
      'tagDeleteErrorResponse',
      'tagDeleteSuccessResponse',
    ])
  }

  #resetTagReOrderState(): void {
    this.tagsFacade.resetTagsState([
      'tagReorderSuccessResponse',
      'tagReorderErrorResponse',
    ])
  }

  #initReviewSetId(): void {
    this.reviewSetId = this.reviewSetState.isBatchReview()
      ? this.reviewSetState.reviewSetId()
      : 0
  }

  #fetchTagTree(): void {
    this.tagsFacade.fetchTagTree(this.projectId, this.reviewSetId)
  }

  #extendTagTree(data: TagsModel[]): TagsModel[] {
    const [_, theData] = data.reduce(
      ([acc, upd], { treeKeyId, treeParentId, ...rest }) => [
        {
          ...acc,
          [treeKeyId]: (acc[treeKeyId] ??= Object.keys(acc).length + 1),
          [treeParentId]:
            treeParentId !== '-1'
              ? (acc[treeParentId] ??= Object.keys(acc).length + 1)
              : null,
        },
        [
          ...upd,
          {
            ...rest,
            treeKeyId,
            treeParentId,
            id: acc[treeKeyId],
            parentId: treeParentId !== '-1' ? acc[treeParentId] : null,
          },
        ],
      ],
      [{}, [] as TagsModel[]]
    )
    return theData as TagsModel[]
  }

  #selectTagTreeResponses(): void {
    combineLatest([
      this.tagsFacade.selectTagTreeSuccessResponse$,
      this.tagsFacade.selectTagTreeErrorResponse$,
    ])
      .pipe(
        filter(([success, error]) => !!success || !!error),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([success, error]) => {
        if (!success && !error) return

        this.#showMessage(success ? success.message : error.message, {
          style: success ? 'success' : 'error',
        })

        const data = cloneDeep(success.data)
        const extendedData = this.#extendTagTree(data)
        this.tagTree.set(extendedData)
        this.clonedTagTree.set(cloneDeep(extendedData))
        this.isDataLoaded.set(true)
      })
  }

  #setDialogInput(instance: ConfirmationDialogComponent): void {
    instance.title = 'Confirm Tag Deletion'
    instance.message = `You will lose all the selected tag(s) and their child tag(s),
                if any, even if the file(s) are associated with these tags.
                Are you sure you want to delete the selected tag(s)?`
  }

  #performTaskAfterConfirmation(): void {
    this.confirmationDialogRef.result
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((result) => {
        const isConfirm = typeof result === 'boolean' && result === true
        if (!isConfirm) return

        this.#updateIsLoadingStateForTagControls(
          CommonActionTypes.DELETE,
          isConfirm
        )
        this.tagsFacade.deleteTag(this.projectId, this.selectedTag.tagId)
      })
  }

  #launchAndSetupConfirmationDialog(): void {
    this.confirmationDialogRef = this.dialogService.open({
      content: ConfirmationDialogComponent,
      cssClass: 'v-confirmation-dialog v-dialog-delete',
      width: '35rem',
    })

    this.#setDialogInput(this.confirmationDialogRef.content.instance)
    this.#performTaskAfterConfirmation()
  }

  #showMessage(content = '', type: Type): void {
    if (!content?.trim()) return

    const notificationRef = this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 3500,
      width: 300,
    })

    // When the user clicks on the notification, it should hide
    notificationRef.notification.location.nativeElement.onclick = (): void => {
      notificationRef.hide()
    }
  }

  #selectTagDeletionResponses(): void {
    combineLatest([
      this.tagsFacade.selectDeleteTagSuccessResponse$,
      this.tagsFacade.selectDeleteTagErrorResponse$,
    ])
      .pipe(
        filter(([success, error]) => !!success || !!error),
        debounceTime(200),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([success, error]) => {
        if (!success && !error) return

        this.#showMessage(success ? success.message : error.message, {
          style: success ? 'success' : 'error',
        })

        this.selectedAction.clear()

        this.#updateIsLoadingStateForTagControls(
          CommonActionTypes.DELETE,
          false
        )
        this.#resetTagDeletionState()

        if (error) return

        this.#fetchTagTree()
      })
  }

  #updateIsLoadingStateForTagControls(
    action: CommonActionTypes,
    isLoading: boolean
  ): void {
    const data = this.tagActionIcons()
    this.tagActionIcons.set(
      data.map((icon) => ({
        ...icon,
        isLoading: action === icon.actionType && isLoading,
      }))
    )
  }

  #selectIsTagDeleting(): void {
    this.tagsFacade.selectIsTagDeleting$
      .pipe(
        filter((isDeleting) => isDeleting),
        takeUntil(this.toDestroy$)
      )
      .subscribe((isDeleting) => {
        this.#updateIsLoadingStateForTagControls(
          CommonActionTypes.DELETE,
          isDeleting
        )
      })
  }

  public trackByTreeFn = (
    _: number,
    item: TreeListItem
  ): TrackByFunction<TreeListItem> => item.data['tageName']

  public ngOnInit(): void {
    this.#initReviewSetId()
    this.#selectTagTreeResponses()
    this.#selectTagReorderResponses()
    this.#fetchTagTree()
    this.#selectIsTagDeleting()
    this.#selectTagDeletionResponses()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  /**
   * Determines whether a button should be disabled based on the current state of the icon and data item.
   *
   * The button is disabled if:
   * - The data item is currently in use and the action type of the icon is either DELETE or EDIT.
   * - The icon is loading, and the selected action for the data item's tag matches the icon's action type,
   *   and the selected tag's ID matches the data item's tag ID.
   *
   * @param {TagActionIconModel}icon - The icon object representing the action and its state.
   * @param {TagsModel}dataItem - The data item associated with this button.
   * @returns {boolean} `true` if the button should be disabled, otherwise `false`.
   */
  public isButtonDisabled(
    icon: TagActionIconModel,
    dataItem: TagsModel
  ): boolean {
    return (
      (dataItem.isInUse &&
        (icon.actionType === CommonActionTypes.DELETE ||
          icon.actionType === CommonActionTypes.EDIT)) ||
      (icon.isLoading &&
        this.selectedAction.get(dataItem.tagId) === icon.actionType &&
        this.selectedTag?.tagId === dataItem.tagId)
    )
  }

  /**
   * Generates the CSS class object for a button based on the current state of the icon and data item.
   *
   * The method returns an object where:
   * - '!t-pt-0 !t-pb-0' is `true` if the icon is loading, the selected action for the data item's tag matches the icon's action type, and the selected tag's ID matches the data item's tag ID.
   * - 't-opacity-50' is `true` if the data item is in use and the icon's action type is either DELETE or EDIT.
   *
   * @param {TagActionIconModel} icon - The icon object representing the action and its state.
   * @param {TagsModel} dataItem - The data item associated with this button.
   * @returns {Object} An object representing the CSS classes to be applied.
   */
  public getButtonClass(
    icon: TagActionIconModel,
    dataItem: TagsModel
  ): { '!t-pt-0 !t-pb-0': boolean; 't-opacity-50': boolean } {
    return {
      '!t-pt-0 !t-pb-0':
        icon.isLoading &&
        this.selectedAction.get(dataItem.tagId) === icon.actionType &&
        this.selectedTag?.tagId === dataItem.tagId,
      't-opacity-50':
        dataItem.isInUse &&
        (icon.actionType === CommonActionTypes.DELETE ||
          icon.actionType === CommonActionTypes.EDIT),
    }
  }

  /**
   * Determines the hover color for an icon based on its action type.
   *
   * This method returns a specific color code depending on the action type:
   * - Returns '#C85D1E' if the action type is DELETE (darker orange).
   * - Returns '#6B7280' for all other action types (darker gray).
   *
   * @param {CommonActionTypes} actionType - The action type associated with the icon. This is used to determine the color.
   * @returns {string} The color code as a string. It returns '#C85D1E' for DELETE action, and '#6B7280' for others.
   */
  public getIconHoverColor(
    actionType: CommonActionTypes
  ): '#C85D1E' | '#6B7280' {
    return actionType === CommonActionTypes.DELETE ? '#C85D1E' : '#6B7280'
  }

  public tagActionClicked(
    actionType: CommonActionTypes,
    selected: TagsModel
  ): void {
    this.selectedTag = selected
    this.selectedAction.set(selected.tagId, actionType)
    switch (actionType) {
      case CommonActionTypes.DELETE:
        this.#launchAndSetupConfirmationDialog()
        break
      case CommonActionTypes.EDIT:
      case CommonActionTypes.CLONE:
        this.tagsFacade.fetchSelectedTag({
          projectId: this.projectId,
          actionType,
          selected,
        })
        break
    }
  }

  #selectTagReorderResponses(): void {
    combineLatest([
      this.tagsFacade.selectTagReorderSuccessResponse$,
      this.tagsFacade.selectTagReorderErrorResponse$,
    ])
      .pipe(
        filter(([success, error]) => Boolean(success || error)),
        debounceTime(200),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([success, error]) => {
        this.#showMessage(success ? success.message : error.message, {
          style: success ? 'success' : 'error',
        })

        this.#resetTagReOrderState()
      })
  }

  #reorderData(
    source: TagsModel,
    sourceIndex: number,
    destinationIndex: number,
    dropPosition: string
  ): void {
    const data: TagsModel[] = cloneDeep(this.tagTree())

    const [removed] = data.splice(sourceIndex, 1)
    const newDestinationIndex =
      dropPosition === 'after' ? destinationIndex + 1 : destinationIndex

    data.splice(newDestinationIndex, 0, removed)
    this.tagTree.set(data)
    this.clonedTagTree.set(cloneDeep(this.tagTree()))

    const filteredAndSortedTagTree = data.filter(
      (tag) =>
        tag.parentTagId === source.parentTagId &&
        tag.tagGroupId === source.tagGroupId
    )

    this.#saveTagReorder(filteredAndSortedTagTree)
  }

  #saveTagReorder(filteredAndSortedTagTree: TagsModel[]): void {
    const tagOrderXml = this.#perpareReorderXMLString(filteredAndSortedTagTree)
    const isSaveXML = XMLUtil.isSafeXml(tagOrderXml, 'TagSortOrder', [
      'TagOrGroup',
      'ID',
      'Order',
    ])
    if (isSaveXML) {
      const tagReorder: TagReOrderModel = {
        tagOrderXml: tagOrderXml.toString(),
      }
      this.tagsFacade.saveTagReorder(this.projectId, tagReorder)
    }
  }

  #perpareReorderXMLString(data: TagsModel[]): string {
    let seqNo = 0
    const tagOrderXml = data
      .map((nodeData) => {
        const isGroup = nodeData.tagGroupId === -1
        const tagOrGroup = isGroup ? 'Group' : 'Tag'
        const id = isGroup ? nodeData.treeKeyId : nodeData.tagId.toString()

        return `<TagSortOrder>
        <TagOrGroup>${tagOrGroup}</TagOrGroup>
        <ID>${id}</ID>
        <Order>${seqNo++}</Order>
      </TagSortOrder>`
      })
      .join('')
    return tagOrderXml
  }

  public onRowReorder(event: RowReorderEvent): void {
    const { draggedRows, dropPosition, dropTargetRow } = event
    const source = draggedRows[0].dataItem
    const destination = dropTargetRow.dataItem

    // The Kendo TreeList has a limitation in its drag-and-drop behavior; it does not allow restriction of the drag over option.
    // Before the onRowReorder event is triggered, the data has already been modified. Therefore, we need to handle the data here as per our requirment.
    // If the drop position is 'over' or if the parent IDs of the source and destination items are different, reset the tag tree.

    const isSameParentTag = source.treeParentId === destination.treeParentId
    const cloneData = cloneDeep(this.clonedTagTree())
    if (dropPosition === 'over') {
      this.tagTree.set(cloneData)
      return
    }
    if (!isSameParentTag) {
      this.tagTree.set(cloneData)
      return
    }

    const sourceIndex = this.tagTree().findIndex(
      (item) => item.id === source.id
    )
    const destinationIndex = this.tagTree().findIndex(
      (item) => item.id === destination.id
    )

    // reorder the data
    this.#reorderData(source, sourceIndex, destinationIndex, dropPosition)
  }
}
