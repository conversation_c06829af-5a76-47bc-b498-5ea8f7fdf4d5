import {
  ChangeDetectionStrategy,
  Component,
  inject,
  On<PERSON><PERSON>roy,
  OnInit,
  signal,
  TrackByFunction,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { CommonActionTypes } from '@venio/shared/models/constants'
import {
  GridDataResult,
  GridItem,
  GridModule,
  PageChangeEvent,
  RowReorderEvent,
} from '@progress/kendo-angular-grid'
import {
  ButtonGroupModule,
  ButtonModule,
} from '@progress/kendo-angular-buttons'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { CodingFacade } from '@venio/data-access/common'
import { Subject, combineLatest, filter, takeUntil, debounceTime } from 'rxjs'
import { ActivatedRoute } from '@angular/router'
import { CompositeFilterDescriptor, filterBy } from '@progress/kendo-data-query'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import {
  CodingReorderModel,
  CustomFieldsModel,
} from '@venio/shared/models/interfaces'
import { ConfirmationDialogComponent } from '@venio/feature/notification'
import { DialogRef, DialogService } from '@progress/kendo-angular-dialog'
import { NotificationService, Type } from '@progress/kendo-angular-notification'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import { cloneDeep } from 'lodash'
import { CodingWorkerService, XMLUtil } from '@venio/util/utilities'
import { FieldFacade } from '@venio/data-access/review'

interface CodingActionIconModel {
  isLoading: boolean
  actionType: CommonActionTypes
  iconPath: string
}

@Component({
  selector: 'venio-coding-list',
  standalone: true,
  imports: [
    CommonModule,
    GridModule,
    ButtonGroupModule,
    ButtonModule,
    SvgLoaderDirective,
    LoaderModule,
    TooltipsModule,
  ],
  templateUrl: './coding-list.component.html',
  styleUrl: './coding-list.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CodingListComponent implements OnDestroy, OnInit {
  private readonly toDestroy$ = new Subject<void>()

  public selectedAction = new Map<number, CommonActionTypes>()

  private codingFacade = inject(CodingFacade)

  private fieldFacade = inject(FieldFacade)

  private activatedRoute = inject(ActivatedRoute)

  private dialogService = inject(DialogService)

  private notificationService = inject(NotificationService)

  public selectedCustomField: CustomFieldsModel

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public isDataLoaded = signal<boolean>(false)

  public isCodingFieldsLoading$ = this.codingFacade.selectIsCodingFieldsLoading$

  public codingFields: CustomFieldsModel[]

  public filter: CompositeFilterDescriptor = {
    logic: 'and',
    filters: [],
  }

  public gridView: GridDataResult

  public pageSize = 100

  public skip = 0

  private confirmationDialogRef: DialogRef

  public ngOnInit(): void {
    this.#selectCodingFetchCodingResponses()
    this.#selectCodingReorderResponses()
    this.#fetchCodingFields()
    this.#selectCodingFieldDeletionResponses()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public codingFieldActionIcons = signal<CodingActionIconModel[]>(
    this.#initializeCodingSvgIcons()
  )

  public trackByIconFn = (_: number, item: unknown): CommonActionTypes =>
    item['actionType'] as CommonActionTypes

  public codingFieldTrackByFn = (
    _: number,
    item: GridItem
  ): TrackByFunction<GridItem> =>
    item.data['customFieldId'] as TrackByFunction<GridItem>

  public filterChange(filter: CompositeFilterDescriptor): void {
    this.filter = filter
    this.#loadCodingFields()
  }

  public pageChange(event: PageChangeEvent): void {
    this.skip = event.skip
    this.#loadCodingFields()
  }

  /**
   * Determines whether a button should be disabled based on the current state of the icon and data item.
   *
   * The button is disabled if:
   * - The data item is an entity field and the action type of the icon is DELETE.
   * - The icon is loading, the selected action for the data item's custom field ID matches the icon's action type,
   *   and the selected custom field's custom field ID matches the data item's custom field ID.
   *
   * @param {CodingActionIconModel} icon - The icon object representing the action and its state.
   * @param {CustomFieldsModel} dataItem - The data item associated with this button.
   * @returns {boolean} `true` if the button should be disabled, otherwise `false`.
   */
  public isButtonDisabled(
    icon: CodingActionIconModel,
    dataItem: CustomFieldsModel
  ): boolean {
    // TODO: permission of add/edit are yet to be derived/implemented from the rights level
    return (
      (dataItem.isEntityField &&
        icon.actionType === CommonActionTypes.DELETE) ||
      (icon.isLoading &&
        this.selectedAction.get(dataItem.customFieldId) === icon.actionType &&
        this.selectedCustomField?.customFieldId === dataItem.customFieldId)
    )
  }

  /**
   * Generates the CSS class object for a button based on the current state of the icon and data item.
   *
   * The method returns an object where:
   * - '!t-pt-0 !t-pb-0' is `true` if the icon is loading, the selected action for the data item's custom field ID matches the icon's action type, and the selected custom field's custom field ID matches the data item's custom field ID.
   * - 't-opacity-50' is `true` if the data item is an entity field and the icon's action type is DELETE.
   *
   * @param {CodingActionIconModel} icon - The icon object representing the action and its state.
   * @param {CustomFieldsModel} dataItem - The data item associated with this button.
   * @returns {Object} An object representing the CSS classes to be applied.
   */
  public getButtonClass(
    icon: CodingActionIconModel,
    dataItem: CustomFieldsModel
  ): { '!t-pt-0 !t-pb-0': boolean; 't-opacity-50': boolean } {
    return {
      '!t-pt-0 !t-pb-0':
        icon.isLoading &&
        this.selectedAction.get(dataItem.customFieldId) === icon.actionType &&
        this.selectedCustomField?.customFieldId === dataItem.customFieldId,
      't-opacity-50':
        dataItem.isEntityField && icon.actionType === CommonActionTypes.DELETE,
    }
  }

  /**
   * Determines the hover color for an icon based on its action type.
   *
   * This method returns a specific color code depending on the action type:
   * - Returns '#ED7425' if the action type is DELETE.
   * - Returns '#FFBB12' for all other action types.
   *
   * @param {CommonActionTypes} actionType - The action type associated with the icon. This is used to determine the color.
   * @returns {string} The color code as a string. It returns '#ED7425' for DELETE action, and '#FFBB12' for others.
   */
  public getIconHoverColor(
    actionType: CommonActionTypes
  ): '#ED7425' | '#FFBB12' {
    return actionType === CommonActionTypes.DELETE ? '#ED7425' : '#FFBB12'
  }

  public codingActionClicked(
    actionType: CommonActionTypes,
    selected: CustomFieldsModel
  ): void {
    this.selectedCustomField = selected
    this.selectedAction.set(selected.customFieldId, actionType)
    switch (actionType) {
      case CommonActionTypes.DELETE:
        this.#launchAndSetupConfirmationDialog()
        break
      case CommonActionTypes.EDIT:
      case CommonActionTypes.CLONE:
        this.codingFacade.fetchCodingFieldById({
          actionType,
          customFieldId: selected.customFieldId,
        })
        break
    }
  }

  #resetCodingFieldDeleteState(): void {
    this.codingFacade.resetCodingState([
      'isDeleteCodingFieldLoading',
      'deleteCodingFieldSuccessResponse',
      'deleteCodingFieldErrorResponse',
    ])
  }

  #resetCodingReOrderState(): void {
    this.codingFacade.resetCodingState([
      'codingReorderSuccessResponse',
      'codingReorderErrorResponse',
    ])
  }

  #showMessage(content = '', type: Type): void {
    if (!content?.trim()) return

    const notificationRef = this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 3500,
      width: 300,
    })

    // When the user clicks on the notification, it should hide
    notificationRef.notification.location.nativeElement.onclick = (): void => {
      notificationRef.hide()
    }
  }

  #selectCodingFieldDeletionResponses(): void {
    combineLatest([
      this.codingFacade.selectDeleteCodingFieldSuccessResponse$,
      this.codingFacade.selectDeleteCodingFieldErrorResponse$,
    ])
      .pipe(
        filter(([success, error]) => !!success || !!error),
        debounceTime(200),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([success, error]) => {
        if (!success && !error) return

        this.#showMessage(success ? success.message : error.message, {
          style: success ? 'success' : 'error',
        })

        this.selectedAction.clear()

        this.#updateIsLoadingStateForCodingActions(
          CommonActionTypes.DELETE,
          false
        )
        this.#resetCodingFieldDeleteState()

        if (error) return

        this.fieldFacade.fetchAllCustomFields(this.projectId)
        this.#fetchCodingFields()
      })
  }

  #updateIsLoadingStateForCodingActions(
    action: CommonActionTypes,
    isLoading: boolean
  ): void {
    const data = this.codingFieldActionIcons()
    this.codingFieldActionIcons.set(
      data.map((icon) => ({
        ...icon,
        isLoading: action === icon.actionType && isLoading,
      }))
    )
  }

  #setDialogInput(instance: ConfirmationDialogComponent): void {
    instance.title = 'Confirm Custom Field Deletion'
    instance.message = `Are you sure you want to delete custom field?`
  }

  #performTaskAfterConfirmation(): void {
    this.confirmationDialogRef.result
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((result) => {
        const isConfirm = typeof result === 'boolean' && result === true
        if (!isConfirm) return

        this.#updateIsLoadingStateForCodingActions(
          CommonActionTypes.DELETE,
          isConfirm
        )
        this.codingFacade.deleteCodingField(
          this.selectedCustomField.customFieldId
        )
      })
  }

  #launchAndSetupConfirmationDialog(): void {
    this.confirmationDialogRef = this.dialogService.open({
      content: ConfirmationDialogComponent,
      cssClass: 'v-confirmation-dialog v-dialog-delete',
      width: '35rem',
    })

    this.#setDialogInput(this.confirmationDialogRef.content.instance)
    this.#performTaskAfterConfirmation()
  }

  #initializeCodingSvgIcons(): Array<CodingActionIconModel> {
    const actions = [
      CommonActionTypes.DELETE,
      CommonActionTypes.EDIT,
      CommonActionTypes.CLONE,
    ]
    const iconPaths = [
      'assets/svg/icon-tagedit-delete.svg',
      'assets/svg/icon-action-grid-pencil.svg',
      'assets/svg/icon-tagedit-copy.svg',
    ]

    return actions.map((action, index) => ({
      actionType: action,
      iconPath: iconPaths[index],
      isLoading: false,
    }))
  }

  #loadCodingFields(): void {
    const filteredTagGroups = filterBy(this.codingFields, this.filter)
    this.gridView = {
      data: filteredTagGroups.slice(this.skip, this.skip + this.pageSize),
      total: filteredTagGroups.length,
    }
    this.isDataLoaded.set(true)
  }

  #fetchCodingFields(): void {
    this.codingFacade.fetchCodingFields()
  }

  #selectCodingFetchCodingResponses(): void {
    combineLatest([
      this.codingFacade.selectCodingFieldSuccessResponse$,
      this.codingFacade.selectCodingFieldErrorResponse$,
    ])
      .pipe(
        filter(([success, error]) => !!success || !!error),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([success, error]) => {
        this.codingFields = success?.data || []
        this.#loadCodingFields()
      })
  }

  #selectCodingReorderResponses(): void {
    combineLatest([
      this.codingFacade.selectCodingReorderSuccessResponse$,
      this.codingFacade.selectCodingReorderErrorResponse$,
    ])
      .pipe(
        filter(([success, error]) => Boolean(success || error)),
        debounceTime(200),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([success, error]) => {
        this.#showMessage(success ? success.message : error.message, {
          style: success ? 'success' : 'error',
        })

        this.#resetCodingReOrderState()
      })
  }

  public onRowReorder(event: RowReorderEvent): void {
    const { draggedRows, dropPosition, dropTargetRow } = event
    const source = draggedRows[0].dataItem
    const destination = dropTargetRow.dataItem

    const sourceIndex = this.codingFields.findIndex(
      (item) => item?.customFieldId === source.customFieldId
    )

    const destinationIndex = this.codingFields.findIndex(
      (item) => item?.customFieldId === destination.customFieldId
    )

    this.#reorderData(
      source,
      sourceIndex,
      destination,
      destinationIndex,
      dropPosition
    )
  }

  #reorderData(
    source: CustomFieldsModel,
    sourceIndex: number,
    destination: CustomFieldsModel,
    destinationIndex: number,
    dropPosition: string
  ): void {
    const data: CustomFieldsModel[] = cloneDeep(this.codingFields)

    const [removed] = data.splice(sourceIndex, 1)
    const newDestinationIndex =
      dropPosition === 'after' ? destinationIndex + 1 : destinationIndex

    data.splice(newDestinationIndex, 0, removed)
    this.codingFields = data
    this.#loadCodingFields()

    this.#perpareReorderXMLString(data)
  }

  #perpareReorderXMLString(customFields: CustomFieldsModel[]): void {
    const codingWorkerService = new CodingWorkerService()
    codingWorkerService
      .generateCodingReorderData(customFields)
      .then((codingOrderXml: string) => {
        codingWorkerService.terminate()

        const isSaveXML = XMLUtil.isSafeXml(codingOrderXml, 'CodingSortOrder', [
          'ID',
          'Order',
        ])
        if (!isSaveXML) return
        this.#saveCodingReorder(codingOrderXml)
      })
  }

  #saveCodingReorder(codingOrderXml: string): void {
    const codingReorder: CodingReorderModel = {
      codingOrderXml,
    }
    this.codingFacade.saveCodingReorder(this.projectId, codingReorder)
  }
}
