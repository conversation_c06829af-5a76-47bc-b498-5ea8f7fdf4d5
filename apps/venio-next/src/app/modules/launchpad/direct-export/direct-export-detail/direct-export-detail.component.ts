import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  Input,
  SimpleChanges,
  OnInit,
  OnChanges,
  OnDestroy,
  output,
  input,
  computed,
  Injector,
  ViewContainerRef,
  signal,
  effect,
  runInInjectionContext,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms'
import { filter, Subject, take, takeUntil } from 'rxjs'
import {
  CaseDetailModel,
  DirectExportImageType,
  ServiceTypeConstants,
} from '@venio/shared/models/interfaces'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import {
  ExpansionPanelModule,
  LayoutModule,
} from '@progress/kendo-angular-layout'
import { ProjectFacade, DirectExportFacade } from '@venio/data-access/common'
import { toSignal } from '@angular/core/rxjs-interop'
import { RelativityLoginComponent } from '../relativity-login/relativity-login.component'
import { DialogService } from '@progress/kendo-angular-dialog'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'

@Component({
  selector: 'venio-direct-export-detail',
  standalone: true,
  imports: [
    CommonModule,
    LabelModule,
    DropDownListModule,
    InputsModule,
    ExpansionPanelModule,
    FormsModule,
    ReactiveFormsModule,
    RelativityLoginComponent,
    IndicatorsModule,
    LayoutModule,
  ],
  templateUrl: './direct-export-detail.component.html',
  styleUrl: './direct-export-detail.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DirectExportDetailComponent
  implements OnInit, OnChanges, OnDestroy
{
  @Input() public settingsForm!: FormGroup // Form group for settings

  @Input() public timeZones$: any[] = [] // List of time zones

  @Input() public exportTemplates: any[] = [] // List of export templates

  @Input() public serviceTypeList: any[] = [] // List of service types

  @Input() public existingCaseId: number = null // ID of the existing case

  public isExistingCase = input<boolean>() // Whether the case is existing

  public isCaseCreationFlow = input<boolean>() // Whether it's a case creation flow

  public selectedServiceType = input<ServiceTypeConstants | null>() // Selected service type

  public readonly existingCaseValue = output<boolean>() // Emits if the case is existing

  public readonly selectedCaseIdValue = output<number>() // Emits the selected case ID

  public readonly selectedBaseAPIUrl = output<string>() // Emits the selected base API URL

  public readonly selectedServiceTypeName = output<ServiceTypeConstants>() // Emits the selected service type name

  private unsubscribed$ = new Subject<void>() // Subject to manage subscriptions

  public generalSettingsOpen = true // Whether general settings panel is open

  public imageConversionOpen = false // Whether image conversion panel is open

  public overrideSettings = false // Whether to override settings

  public existingCase = false // Whether the case is existing

  public showCaseNameInput = true // Whether to show the case name input

  public caseList: CaseDetailModel[] = [] // List of cases

  public selectedCaseId!: number // Selected case ID

  public currentExpandedPanel = signal('general-setting') // Currently expanded panel

  public defaultItemCaseOption: { projectName: string; projectId: number } = {
    projectName: 'Select Case',
    projectId: -1,
  } // Default case option

  public defaultItemServiceOption: {
    serviceTypeDisplayName: string
    serviceTypeId: number
  } = {
    serviceTypeDisplayName: 'Select Service',
    serviceTypeId: -1,
  } // Default service option

  public defaultItemEnvironmentSelectionOption = {
    environmentName: 'Select Environment',
    id: null,
  } // Default environment option

  public readonly binderSizes = signal<{ value: number; label: string }[]>([
    { value: 0, label: '1' },
    { value: 1, label: '2' },
    { value: 2, label: '3' },
    { value: 3, label: '4' },
    { value: 4, label: '5' },
  ])

  public readonly binderColors = signal<{ value: number; label: string }[]>([
    { value: 0, label: 'Black' },
    { value: 1, label: 'White' },
  ])

  public readonly deduplicationOptions = [
    { displayName: 'None', value: 2 },
    { displayName: 'Custodian Level', value: 1 },
    { displayName: 'Global', value: 0 },
  ] // Deduplication options

  public readonly csvExcelOptions = [
    { displayName: 'Placeholder Only', value: 0 },
    { displayName: 'Cap to 100 Pages, then Placeholder', value: 1 },
    { displayName: 'Process all Pages', value: 2 },
  ] // CSV/Excel handling options

  public readonly exceptionHandlingOptions = [
    { displayName: 'Notify me and allow file repair', value: true },
    {
      displayName: 'Do not notify me, complete the project and report',
      value: false,
    },
  ] // Exception handling options

  public readonly sortOrderOptions = [
    { displayName: 'Original Discovery Order', value: 'RELATIVE_FILE_PATH' },
    { displayName: 'Sort By Date – Oldest to Newest', value: 'GROUP_DATE_ASC' },
    {
      displayName: 'Sort By Date – Newest to Oldest',
      value: 'GROUP_DATE_DESC',
    },
  ] // Sort order options

  public readonly prefixDelimiterOptions = [
    { displayName: 'None', value: 0 },
    { displayName: 'Space', value: 1 },
    { displayName: 'Dash(-)', value: 2 },
    { displayName: 'Underscore(_)', value: 3 },
    { displayName: 'Period(.)', value: 4 },
  ] // Prefix delimiter options

  public readonly controlNumberLocationOptions = [
    { displayName: 'TL', value: 0 },
    { displayName: 'TC', value: 1 },
    { displayName: 'TR', value: 2 },
    { displayName: 'LL', value: 3 },
    { displayName: 'LC', value: 4 },
    { displayName: 'LR', value: 5 },
  ] // Control number location options

  private readonly viewContainerRef = inject(ViewContainerRef) // Reference to the view container

  private readonly directExportFacade = inject(DirectExportFacade) // Facade for direct export

  private readonly cdr = inject(ChangeDetectorRef) // Change detector reference

  private dialog = inject(DialogService) // Dialog service

  private readonly projectFacade = inject(ProjectFacade) // Facade for project

  private injector = inject(Injector) // Injector for dependency injection

  public isWorkSpaceLoading = toSignal(
    this.directExportFacade?.selectIsRelativityWorkspaceLoading$,
    { initialValue: false }
  ) // Signal for workspace loading state

  public isWorkspaceFileShareDataLoading = toSignal(
    this.directExportFacade?.selectIsRelativityWorkspaceFileshareLoading$,
    { initialValue: false }
  ) // Signal for workspace file share loading state

  private readonly connectorEnvironemntList = toSignal(
    this.directExportFacade?.selectConnctorEnvironmentSuccess$
  ) // Signal for connector environment list

  private readonly loadedConnectorEnvioronemntList = computed<any[]>(
    () => this.connectorEnvironemntList() || []
  ) // Computed signal for loaded connector environments

  private readonly relativityTemplatesList = toSignal(
    this.directExportFacade?.selectRelativityFieldTemplatesSuccess$
  ) // Signal for relativity templates list

  private readonly loadedRelativityFieldTemplateList = computed<any[]>(
    () => this.relativityTemplatesList() || []
  ) // Computed signal for loaded relativity templates

  private readonly relativityWorkspaceList = toSignal(
    this.directExportFacade?.selectRelativityWorkspaceSuccess$
  ) // Signal for relativity workspace list

  private readonly loadedRelativityWorkspaceList = computed<any[]>(
    () => this.relativityWorkspaceList() || []
  ) // Computed signal for loaded relativity workspaces

  private readonly relativityWorkspaceFileshareList = toSignal(
    this.directExportFacade?.selectRelativityWorkspaceFileshareSuccess$
  ) // Signal for relativity workspace file share list

  private readonly loadedRelativityWorkspaceFileShareList = computed<any[]>(
    () => this.relativityWorkspaceFileshareList() || []
  ) // Computed signal for loaded relativity workspace file shares

  public pdfServiceVisible = signal(false) // Whether PDF service is visible

  public relativityServiceVisible = signal(false) // Whether Relativity service is visible

  public isConcordanceOrSummationService = signal(false) // Whether it's Concordance or Summation service

  public isPrintService = signal(false) // Whether it's Print service

  private generateImage = signal(false) // Whether to generate image

  public showColorConvertionOptions = computed<boolean>(() => {
    return (
      !this.isConcordanceOrSummationService() ||
      (this.isConcordanceOrSummationService() && this.generateImage())
    )
  })

  public clientId: number // Client ID

  public selectedEnvironment: number // Selected environment

  public selectedUserEnvironment: any // Selected user environment

  public selectedWorkspaceId: number // Selected workspace ID

  public serviceTypeName: ServiceTypeConstants // Service type name

  public isRelativityOne: boolean // Whether it's RelativityOne

  public relativityOneClientId: string // RelativityOne client ID

  public relativityUsername: string // Relativity username

  public selectedEnvironmentData: any // Selected environment data

  public relativityLoginDialogPayload: {
    id: number
    environmentId: number
    relativityOneClientSecret: string
    rememberMe: boolean
  } // Payload for Relativity login dialog

  public connectorEnvironmentData = signal([]) // Connector environment data

  public relativityTemplatesData: any[] // Relativity templates data

  public workSpaceData = signal<any[]>([]) // Workspace data

  public workSpaceFileshareData = signal<any[]>([]) // Workspace file share data

  /** Signal for the case detail loading state */
  public isCaseDetailLoading = toSignal(
    this.projectFacade.selectIsCaseDetailLoading$,
    { initialValue: false }
  )

  /** Signal for the case detail list */
  private readonly caseDetail = toSignal(this.projectFacade.selectCaseDetail$)

  /** Signal for the case detail list */
  private readonly loadedCases = computed<CaseDetailModel[]>(
    () => this.caseDetail()?.caseDetailEntries || []
  )

  // Toggles the expansion panel
  public onPanelToggle(panelId: string, isExpanded: boolean): void {
    if (isExpanded) this.currentExpandedPanel.set(panelId)
    else this.currentExpandedPanel.set('')
  }

  // Initializes the component
  public ngOnInit(): void {
    this.#loadServicesData()
    this.#handleImageTypeChange()
    this.#handleRelativityMappingTemplateChange()
    const formValue = this.settingsForm.value

    if (formValue?.ServiceRequestType !== null) {
      const serviceTypeName = this.#fetchServiceTypeName(
        formValue?.ServiceRequestType
      )

      this.#updateExistingServiceData(
        <ServiceTypeConstants>serviceTypeName,
        this.connectorEnvironemntList()
      )
    }

    this.directExportFacade.selectDefaultData$
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((res) => {
        if (res) {
          this.#updateDefaultValues(res)
        }
      })
  }

  // sets relativity mapping template name when template is selected
  #handleRelativityMappingTemplateChange(): void {
    const relativityMappingControl = this.settingsForm.get(
      'productionSettings.relativityFieldMappingTemplateId'
    )
    if (relativityMappingControl) {
      relativityMappingControl.valueChanges
        .pipe(
          filter((id) => !!id && this.relativityTemplatesData?.length > 0),
          takeUntil(this.unsubscribed$)
        )
        .subscribe((value) => {
          const selectedTemplate = this.relativityTemplatesData.find(
            (template) => template.id === value
          )
          this.settingsForm.patchValue({
            productionSettings: {
              relativityFieldMappingTemplateName: selectedTemplate?.name,
            },
          })
        })
    }
  }

  #handleImageTypeChange(): void {
    const imageTypeCtrl = this.settingsForm.get(
      'imageConversionSettings.imageType'
    )
    if (imageTypeCtrl) {
      imageTypeCtrl.valueChanges
        .pipe(takeUntil(this.unsubscribed$))
        .subscribe((value) => {
          const imageType = value as DirectExportImageType
          this.generateImage.set(imageType !== DirectExportImageType.NONE)
        })
    }
  }

  #loadServicesData(): void {
    // Use Angular's runInInjectionContext to ensure an injection context
    runInInjectionContext(this.injector, async () => {
      effect(
        () => {
          this.caseList = this.loadedCases().filter(
            (item) => item.isExportServiceCase
          )
        },
        { allowSignalWrites: true }
      )

      effect(
        () => {
          this.connectorEnvironmentData.set(
            this.loadedConnectorEnvioronemntList()
          )
        },
        { allowSignalWrites: true }
      )

      effect(
        () => {
          this.relativityTemplatesData =
            this.loadedRelativityFieldTemplateList().filter(
              (item) => !item.isExportSpecificTemplate
            )
          this.workSpaceData.set([
            { id: null, name: 'Select Workspace' },
            ...this.loadedRelativityWorkspaceList(),
          ])
          this.workSpaceFileshareData.set([
            { id: null, name: 'Select Workspace File Share' },
            ...this.loadedRelativityWorkspaceFileShareList(),
          ])
        },
        { allowSignalWrites: true }
      )
    })
  }

  // Handles changes to the component inputs
  public ngOnChanges(changes: SimpleChanges): void {
    if (this.existingCaseId !== null && this.existingCaseId !== undefined) {
      this.existingCase = true
      this.showCaseNameInput = false
      this.fetchExistingCaseData(this.existingCaseId)
    }

    if (this.isCaseCreationFlow() !== undefined) {
      const isCaseCreationFlow = !this.isCaseCreationFlow()
      this.#disableServiceTypeCaseNameInput(isCaseCreationFlow)
    }
  }

  // Disables or enables the service type and case name inputs
  #disableServiceTypeCaseNameInput(isUploadFlow: boolean): void {
    const serviceRequestTypeControl =
      this.settingsForm.get('ServiceRequestType')
    if (serviceRequestTypeControl) {
      isUploadFlow
        ? serviceRequestTypeControl.disable()
        : serviceRequestTypeControl.enable()
    }

    const selectedCaseControl = this.settingsForm.get('selectedCase')
    if (selectedCaseControl) {
      isUploadFlow
        ? selectedCaseControl.disable()
        : selectedCaseControl.enable()
    }
  }

  // Handles workspace selection
  public onWorkspaceSelection(event: number): void {
    this.directExportFacade.clearRelativityWorkspaceFileshareList()
    this.updateSelectedWorkspace(event)
  }

  public updateSelectedWorkspace(event: number): void {
    if (event === null) {
      return
    }
    this.selectedWorkspaceId = event
    const selectedWorkspaceData = this.workSpaceData()?.filter(
      (data) => data.id === event
    )[0]

    if (!this.existingCase) {
      this.settingsForm
        .get('productionSettings.connector.connectorFileSharePath')
        ?.enable()
    } else {
      this.settingsForm
        .get('productionSettings.connector.connectorFileSharePath')
        ?.disable()
    }

    this.settingsForm.patchValue({
      productionSettings: {
        connector: {
          workspaceName: selectedWorkspaceData?.name,
        },
      },
    })

    if (this.isRelativityOne) {
      this.#fetchWorkspaceFileshareList()
    }
  }

  // Handles environment selection
  public onEnvironmentSelection(event: number): void {
    this.directExportFacade.clearRelativityWorkspaceList()
    this.directExportFacade.clearRelativityWorkspaceFileshareList()
    this.updateSelectedEnvironment(event)
  }

  public updateSelectedEnvironment(event: number): void {
    this.selectedEnvironment = event
    this.selectedEnvironmentData = this.connectorEnvironmentData()?.filter(
      (data) => data.id === event
    )[0]
    this.isRelativityOne =
      this.selectedEnvironmentData?.connector?.toUpperCase() === 'RELATIVITYONE'
    if (!this.existingCase) {
      this.settingsForm
        .get('productionSettings.connector.workspaceId')
        ?.enable()
    }
    this.settingsForm
      .get('productionSettings.connector.connectorFileSharePath')
      ?.disable()

    this.settingsForm.patchValue({
      productionSettings: {
        connector: {
          workspaceId: null,
          connectorFileSharePath: 'Select Workspace File Share',
        },
      },
    })
    this.selectedBaseAPIUrl.emit(this.selectedEnvironmentData?.baseAPIURL ?? '')

    this.settingsForm.patchValue({
      productionSettings: {
        connector: {
          baseAPIUrl:
            this.selectedEnvironmentData?.baseAPIURL ?? 'Base API URL',
          connectorPlatform: this.selectedEnvironmentData?.connector,
          userEnvironmentId: this.selectedEnvironmentData?.userEnvironment?.id,
          name: this.selectedEnvironmentData?.environmentName,
        },
      },
    })

    this.selectedUserEnvironment = null

    if (event === null) return

    this.relativityLoginDialogPayload = {
      id: this.selectedEnvironment,
      environmentId:
        this.selectedEnvironmentData?.userEnvironment?.environmentId,
      relativityOneClientSecret: this.selectedEnvironmentData?.apiClientSecret,
      rememberMe:
        this.selectedEnvironmentData?.userEnvironment?.isDefaultAccount,
    }

    if (
      !this.selectedEnvironmentData?.userEnvironment &&
      !this.isExistingCase()
    ) {
      this.openRelativityLoginDialog(this.relativityLoginDialogPayload)
    } else {
      this.relativityUsername =
        this.selectedEnvironmentData?.userEnvironment?.userName
      this.relativityOneClientId =
        this.selectedEnvironmentData?.userEnvironment?.apiClientId
      this.selectedUserEnvironment =
        this.selectedEnvironmentData?.userEnvironment?.id
      this.#fetchWorkspaceList()
      this.cdr.detectChanges()
    }
  }

  // Opens the Relativity login dialog
  public openRelativityLoginDialog(relativityData: {
    id: number
    environmentId: number
    relativityOneClientSecret: string
    rememberMe: boolean
  }): void {
    const dialogRef = this.dialog.open({
      appendTo: this.viewContainerRef,
      content: RelativityLoginComponent,
      maxHeight: '80vh',
      height: '350px',
      minWidth: 250,
      width: '420px',
    })

    dialogRef.result.subscribe(
      (result?: {
        isLoggedInSuccessfully: boolean
        userEnvironmentId: number
      }) => {
        if (result?.isLoggedInSuccessfully) {
          this.selectedUserEnvironment = result.userEnvironmentId
          this.settingsForm.patchValue({
            productionSettings: {
              connector: {
                userEnvironmentId: result.userEnvironmentId,
              },
            },
          })
          this.#fetchWorkspaceList()
        }
      }
    )

    const instance: RelativityLoginComponent = dialogRef.content.instance
    instance.relativityData = {
      id: relativityData?.id,
      environmentId: relativityData?.environmentId,
      isRelativityOne: this.isRelativityOne,
      relativityOneClientId: this.relativityOneClientId,
      relativityOneClientSecret: relativityData?.relativityOneClientSecret,
      rememberMe: relativityData?.rememberMe,
    }
  }

  public selectedCaseChanged(caseId: number): void {
    this.updateOverrideSettingsOption(false)
    this.fetchExistingCaseData(caseId)
  }

  // Fetches data for an existing case
  public fetchExistingCaseData(existingCaseId: number): void {
    this.directExportFacade.resetDirectExportState('caseData')
    this.directExportFacade?.fetchExistingCaseData(existingCaseId)

    this.selectedCaseId = existingCaseId
    this.#selectedCaseIdEmit(existingCaseId)
    this.#existingCaseEmit(this.existingCase)

    this.directExportFacade.selectCaseData$
      .pipe(
        filter((res) => res !== null && res !== undefined),
        take(1),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((res) => {
        const serviceTypeName = this.#fetchServiceTypeName(
          res?.data?.serviceRequestType
        )

        const overrideSettings = this.settingsForm.get('overrideSetting').value
        if (!overrideSettings) {
          this.#updateDefaultValues(res?.data)
          this.settingsForm.patchValue({ selectedCase: existingCaseId })
        } else {
          this.overrideSettings = overrideSettings
          this.#updateExistingServiceData(<ServiceTypeConstants>serviceTypeName)
          this.cdr.detectChanges()
        }
        this.#updateFormControls(overrideSettings)

        this.#removeConnectorsValidations(
          serviceTypeName !==
            ServiceTypeConstants.VODR_IMPORT_TO_RELATIVITY_SERVICE
        )
      })
  }

  #updateExistingServiceData(
    serviceTypeName: ServiceTypeConstants,
    connectorEnvironemntList?: any
  ): void {
    this.relativityServiceVisible.set(
      serviceTypeName === ServiceTypeConstants.VODR_IMPORT_TO_RELATIVITY_SERVICE
    )
    if (this.relativityServiceVisible()) {
      const formValue = this.settingsForm.value
      const connectorId = formValue.productionSettings?.connector?.id
      const connectorData =
        this.connectorEnvironmentData()?.length > 0
          ? this.connectorEnvironmentData()
          : connectorEnvironemntList

      this.selectedEnvironmentData = connectorData?.filter(
        (data) => data.id === connectorId
      )[0]
      this.isRelativityOne =
        this.selectedEnvironmentData?.connector?.toUpperCase() ===
        'RELATIVITYONE'
    }

    this.isConcordanceOrSummationService.set(
      serviceTypeName === ServiceTypeConstants.VODR_CONCORDANCE_SERVICE ||
        serviceTypeName === ServiceTypeConstants.VODR_SUMMATION_SERVICE
    )
    this.isPrintService.set(
      serviceTypeName === ServiceTypeConstants.VODR_PRINT_SERVICE
    )
    this.pdfServiceVisible.set(
      serviceTypeName === ServiceTypeConstants.VODR_PDF_SERVICE
    )
  }

  // Updates the override settings option
  public updateOverrideSettingsOption(event: any): void {
    let isChecked: boolean

    if (typeof event === 'boolean') {
      isChecked = event
    } else {
      const checkbox = event.target as HTMLInputElement
      isChecked = checkbox.checked
    }
    this.overrideSettings = isChecked
    this.settingsForm.patchValue({
      overrideSetting: isChecked,
    })
    this.#updateFormControls(isChecked)
  }

  // Updates form controls based on override settings
  #updateFormControls(overrideSettings: boolean): void {
    const formGroups = [
      'generalSettings',
      'imageConversionSettings',
      'controlNumberAndEndorsementSettings',
      'productionSettings',
      'pdfServiceSettings',
      'printServiceSettings',
    ]

    formGroups.forEach((groupName) => {
      const formGroup = this.settingsForm.get(groupName)
      if (formGroup) {
        overrideSettings ? formGroup.enable() : formGroup.disable()
      }
    })

    // to disable certain control even if overrideSettings is true
    if (overrideSettings) {
      // disable the imageType control to prevent user from changing imageType for existing case
      if (this.existingCase) {
        this.settingsForm.get('imageConversionSettings.imageType')?.disable()
      }

      const controlNumberCtrl = this.settingsForm.get(
        'controlNumberAndEndorsementSettings.ControlNumberSetting'
      )
      // disable control number related fields that should remain disabled unless endorse option is checked
      if (!controlNumberCtrl?.get('endorseControlNumber')?.value) {
        controlNumberCtrl?.get('controlNumberLocation')?.disable()
      }
      if (!controlNumberCtrl?.get('endorseOptionalMessage')?.value) {
        controlNumberCtrl?.get('messageText')?.disable()
        controlNumberCtrl?.get('messageTextLocation')?.disable()
      }
      // always disable the connector one base url field
      this.settingsForm
        .get('productionSettings.connector.baseAPIUrl')
        ?.disable()
    }
  }

  #fetchServiceTypeName(data: any): string | undefined {
    return this.serviceTypeList && this.serviceTypeList?.length > 0
      ? (this.serviceTypeList?.find((val) => val?.serviceTypeId === data)
          ?.serviceTypeName as string | undefined)
      : undefined
  }

  // Updates default values in the form
  #updateDefaultValues(data: any): void {
    this.settingsForm.patchValue({
      exportTemplateName: data?.exportTemplateName,
      approvePreProcessPage_CostEstimate:
        data?.approvePreProcessPage_CostEstimate,
      generalSettings: {
        deduplicationOption: data?.imageConversionOption?.deduplicationOption,
        timeZone: data?.tzTimeZone,
        csvExcelHandling: data?.imageConversionOption?.csV_Excel_option,
        discoveryExceptionHandling: data?.enableDiscoveryExceptionHandling,
        passwords: '',
        autoGenerateImagesAfterIngestion:
          data?.imageConversionOption?.autoGenerateImagesAfterIngestion,
        ignoreAutoTiffJobsForMediaProcessingStatus:
          data?.imageConversionOption
            ?.ignoreAutoTiffJobsForMediaProcessingStatus,
      },
      imageConversionSettings: {
        imageType: data?.imageConversionOption?.imageType,
        imageColorConversion: {
          imageFileType:
            data?.imageConversionOption?.imageColorConversion?.imageFileType,
          pdfFiles: data?.imageConversionOption?.imageColorConversion?.pdfFiles,
          powerpoint:
            data?.imageConversionOption?.imageColorConversion?.powerpoint,
        },
        passwordList: this.#getFormValueFromPasswords(
          data?.imageConversionOption?.passwordList
        ),
      },
      controlNumberAndEndorsementSettings: {
        sortOrder: data?.controlNumber_Endorsement?.sortOrder,
        exportLocation: data?.controlNumber_Endorsement?.exportLocation,
        ControlNumberSetting: {
          controlNumberPrefix:
            data?.controlNumber_Endorsement?.controlNumberSetting?.prefix,
          controlNumberDelimiter:
            data?.controlNumber_Endorsement?.controlNumberSetting
              ?.prefixDelimiter,
          controlNumberStartingNumber:
            data?.controlNumber_Endorsement?.controlNumberSetting?.startNumber,
          endorseControlNumber:
            data?.controlNumber_Endorsement?.controlNumberSetting
              ?.endorseControlNumber,
          controlNumberLocation:
            data?.controlNumber_Endorsement?.controlNumberSetting
              ?.controlNumberLocation,
          endorseOptionalMessage:
            data?.controlNumber_Endorsement?.controlNumberSetting
              ?.endorseOptionalMessage,
          messageText:
            data?.controlNumber_Endorsement?.controlNumberSetting?.messageText,
          messageTextLocation:
            data?.controlNumber_Endorsement?.controlNumberSetting
              ?.optionalMessageLocation,
          volumeId:
            data?.controlNumber_Endorsement?.controlNumberSetting?.volumnId,
          paddingLength:
            data?.controlNumber_Endorsement?.controlNumberSetting
              ?.paddingLength,
          continueFromPreviousControlNumber:
            data?.controlNumber_Endorsement?.controlNumberSetting
              ?.continueFromPreviousControlNumber,
          advancedEndorsementSetting:
            data?.controlNumber_Endorsement?.controlNumberSetting
              ?.advancedEndorsementSetting,
          prefixDelimiterValue:
            data?.controlNumber_Endorsement?.controlNumberSetting
              ?.prefixDelimiterValue,
        },
      },
      productionSettings: {
        fieldTemplateId:
          data?.productionOptions?.fieldTemplateID ||
          data?.productionOptions?.fieldTemplateId,
        filterOptions: {
          excludeProducedDocuments:
            data?.productionOptions?.filterOptions?.excludeProducedDocuments,
          excludeNativeForRedactedDocuments:
            data?.productionOptions?.filterOptions
              ?.excludeNativeForRedactedDocuments,
        },
        savedSearchesForExpressions:
          data?.productionOptions?.savedSearchesForExpressions,
        relativityFieldMappingTemplateId:
          data?.productionOptions?.relativityFieldMappingTemplateId || 1,
        connector: {
          id: data?.productionOptions?.connector?.id ?? null,
          name: data?.productionOptions?.connector?.name ?? '',
          connectorPlatform:
            data?.productionOptions?.connector?.connectorPlatform ?? '',
          userEnvironmentId:
            data?.productionOptions?.connector?.userEnvironmentId ?? null,
          workspaceId: data?.productionOptions?.connector?.workspaceId ?? null,
          workspaceName:
            data?.productionOptions?.connector?.workspaceName ?? '',
          baseAPIUrl: data?.productionOptions?.connector?.baseAPIUrl ?? '',
          connectorFileSharePath:
            data?.productionOptions?.connector?.connectorFileSharePath ?? null,
        },
      },
      pdfServiceSettings: {
        pdfType: data?.pdfServiceOption?.pdfType,
        pdfFamilyFileHandling: data?.pdfServiceOption?.pdfFamilyFileHandling,
        pdfFileNamingConvention:
          data?.pdfServiceOption?.pdfFileNamingConvention,
      },
      printServiceSettings: {
        binding: data?.printServiceOption?.printBinding?.bindingType,
        threeRingBinderSize: data?.printServiceOption?.printBinding?.binderSize,
        threeRingBinderColor:
          data?.printServiceOption?.printBinding?.binderColor,
        printSet: data?.printServiceOption?.printSet?.printSetOption,
        numberOfSets: +data?.printServiceOption?.printSet?.numberOfSetValue,
        paperType: data?.printServiceOption?.paperType,
        printFamilyFileHandling: data?.printServiceOption?.familyFileHandling,
        documentSeparator: data?.printServiceOption?.documentSeparator,
        paperSide: data?.printServiceOption?.paperSide,
      },
      thirdPartyBillingOption: {
        thirdPartyBillingEnabled:
          data?.thirdPartyBillingOption?.thirdPartyBillingEnabled,
        company: data?.thirdPartyBillingOption?.company,
        billingAddress: data?.thirdPartyBillingOption?.billingAddress,
        billingCaseName: data?.thirdPartyBillingOption?.billingCaseName,
        contactPerson: data?.thirdPartyBillingOption?.contactPerson,
        contactPhone: data?.thirdPartyBillingOption?.contactPhone,
        contactEmail: data?.thirdPartyBillingOption?.contactEmail,
      },
      webURL: data?.webURL,
      clientMatterNo: '',
      createImage: data?.createImage,
      dataRetentionRequest: data?.dataRetentionRequest,
      editableCustomFieldList: data?.editableCustomFieldList,
      productionSourceId: data?.productionSourceId,
      enableDiscoveryExceptionHandling: data?.enableDiscoveryExceptionHandling,
      autoQueueForEntityExtraction: data?.autoQueueForEntityExtraction,
    })

    const serviceTypeName = this.#fetchServiceTypeName(data?.serviceRequestType)

    if (this.existingCase && serviceTypeName) {
      this.#existingCaseEmit(this.existingCase)
      this.settingsForm.patchValue({
        caseName: data?.caseName,
        ServiceRequestType: ServiceTypeConstants.VODR_ADD_DATA_EXISTING_PROJECT,
        serviceRequestTypeExisting: data.serviceRequestType,
        serviceTypeName: serviceTypeName,
      })

      this.relativityServiceVisible.set(
        serviceTypeName ===
          ServiceTypeConstants.VODR_IMPORT_TO_RELATIVITY_SERVICE
      )
      if (this.relativityServiceVisible()) {
        if (data?.productionOptions?.connector) {
          this.updateSelectedEnvironment(data?.productionOptions?.connector?.id)
          this.updateSelectedWorkspace(
            data?.productionOptions?.connector?.workspaceId
          )
        }

        this.settingsForm.patchValue({
          productionSettings: {
            relativityFieldMappingTemplateId:
              data?.productionOptions?.relativityFieldMappingTemplateId,
            connector: {
              id: data?.productionOptions?.connector?.id,
              connectorPlatform:
                data?.productionOptions?.connector?.connectorPlatform,
              connectorFileSharePath:
                data?.productionOptions?.connector?.connectorFileSharePath,
              name: data?.productionOptions?.connector?.name,
              userEnvironmentId:
                data?.productionOptions?.connector?.userEnvironmentId,
              workspaceId: data?.productionOptions?.connector?.workspaceId,
              workspaceName: data?.productionOptions?.connector?.workspaceName,
            },
          },
        })
      }

      this.pdfServiceVisible.set(
        serviceTypeName === ServiceTypeConstants.VODR_PDF_SERVICE
      )

      this.isConcordanceOrSummationService.set(
        serviceTypeName === ServiceTypeConstants.VODR_CONCORDANCE_SERVICE ||
          serviceTypeName === ServiceTypeConstants.VODR_SUMMATION_SERVICE
      )
      this.isPrintService.set(
        serviceTypeName === ServiceTypeConstants.VODR_PRINT_SERVICE
      )

      this.isConcordanceOrSummationService.set(
        serviceTypeName === ServiceTypeConstants.VODR_CONCORDANCE_SERVICE ||
          serviceTypeName === ServiceTypeConstants.VODR_SUMMATION_SERVICE
      )
      this.isPrintService.set(
        serviceTypeName === ServiceTypeConstants.VODR_PRINT_SERVICE
      )
    } else {
      this.settingsForm.patchValue({
        ServiceRequestType: data?.serviceRequestType,
      })
    }

    this.cdr.detectChanges()
  }

  // Emits the existing case value
  #existingCaseEmit(value: boolean): void {
    this.existingCaseValue.emit(value)
  }

  // Emits the selected case ID
  #selectedCaseIdEmit(value: number): void {
    this.selectedCaseIdValue.emit(value)
  }

  // Handles service request selection
  public onServiceRequestSelection(event: number): void {
    const serviceTypeName = this.serviceTypeList?.find(
      (val) => val?.serviceTypeId === event
    )?.serviceTypeName

    this.overrideSettings = false
    this.existingCase = false
    this.showCaseNameInput = true
    this.settingsForm.patchValue({ selectedCase: -1, caseName: '' })
    this.serviceTypeName = serviceTypeName

    if (
      serviceTypeName === ServiceTypeConstants.VODR_ADD_DATA_EXISTING_PROJECT
    ) {
      this.projectFacade.fetchCaseDetail()
      this.existingCase = true
      this.showCaseNameInput = false
      this.settingsForm.patchValue({ caseName: '' })
    } else {
      this.directExportFacade.fetchServiceTypeDefaultData(serviceTypeName)
      this.#updateFormControls(true)

      if (
        serviceTypeName ===
        ServiceTypeConstants.VODR_IMPORT_TO_RELATIVITY_SERVICE
      ) {
        this.#disableFormControls([
          'productionSettings.connector.workspaceId',
          'productionSettings.connector.connectorFileSharePath',
          'productionSettings.connector.baseAPIUrl',
        ])
      } else {
        this.#disableFormControls([
          'controlNumberAndEndorsementSettings.ControlNumberSetting.controlNumberLocation',
          'controlNumberAndEndorsementSettings.ControlNumberSetting.messageText',
          'controlNumberAndEndorsementSettings.ControlNumberSetting.messageTextLocation',
        ])
      }
    }

    this.isConcordanceOrSummationService.set(
      [
        ServiceTypeConstants.VODR_CONCORDANCE_SERVICE,
        ServiceTypeConstants.VODR_SUMMATION_SERVICE,
      ].includes(serviceTypeName)
    )

    this.pdfServiceVisible.set(
      serviceTypeName === ServiceTypeConstants.VODR_PDF_SERVICE
    )
    this.relativityServiceVisible.set(
      serviceTypeName === ServiceTypeConstants.VODR_IMPORT_TO_RELATIVITY_SERVICE
    )
    this.isPrintService.set(
      serviceTypeName === ServiceTypeConstants.VODR_PRINT_SERVICE
    )

    this.selectedServiceTypeName.emit(serviceTypeName)
    this.#removeConnectorsValidations(
      serviceTypeName !== ServiceTypeConstants.VODR_IMPORT_TO_RELATIVITY_SERVICE
    )
    this.#existingCaseEmit(this.existingCase)
  }

  #disableFormControls(controlPaths: string[]): void {
    controlPaths.forEach((path) => {
      this.settingsForm.get(path)?.disable()
    })
  }

  // Checks if the endorse optional message is checked
  public get endorseOptionalMessageIsChecked(): boolean {
    const value = this.settingsForm.get(
      'controlNumberAndEndorsementSettings.ControlNumberSetting.endorseOptionalMessage'
    )?.value
    return Boolean(value)
  }

  // Checks if the endorse control number is checked
  public get endorseControlNumberIsChecked(): boolean {
    const value = this.settingsForm.get(
      'controlNumberAndEndorsementSettings.ControlNumberSetting.endorseControlNumber'
    )?.value
    return Boolean(value)
  }

  // Checks if the form is valid and filled
  public isValidFormFilled(): boolean {
    const caseName = this.settingsForm.get('caseName')?.value
    const serviceType = this.settingsForm.get('ServiceRequestType')?.value
    const selectedCase = this.settingsForm.get('selectedCase')?.value
    return (
      (!!caseName &&
        serviceType !== undefined &&
        serviceType !== null &&
        serviceType !== -1 &&
        serviceType !== ServiceTypeConstants.VODR_ADD_DATA_EXISTING_PROJECT) ||
      (this.existingCase && selectedCase && selectedCase !== -1)
    )
  }

  // Cleans up subscriptions on component destruction
  public ngOnDestroy(): void {
    this.unsubscribed$.next()
    this.unsubscribed$.complete()
  }

  // Fetches workspace list
  #fetchWorkspaceList(): void {
    if (this.selectedEnvironment) {
      this.directExportFacade.fetchRelativityWorkspaces(
        this.selectedEnvironment,
        this.selectedUserEnvironment
      )
    }
  }

  // Fetches workspace file share list
  #fetchWorkspaceFileshareList(): void {
    if (this.selectedEnvironment) {
      this.directExportFacade.fetchRelativityWorkspaceFileshares(
        this.selectedEnvironment,
        this.selectedWorkspaceId,
        this.selectedUserEnvironment
      )
    }
  }

  #removeConnectorsValidations(removeValidations: boolean): void {
    const controlPaths = [
      'productionSettings.relativityFieldMappingTemplateId',
      'productionSettings.connector.id',
      'productionSettings.connector.name',
      'productionSettings.connector.connectorPlatform',
      'productionSettings.connector.userEnvironmentId',
      'productionSettings.connector.workspaceId',
      'productionSettings.connector.workspaceName',
      'productionSettings.connector.connectorFileSharePath',
    ]

    controlPaths.forEach((path) => {
      const control = this.settingsForm.get(path)
      if (control) {
        if (removeValidations) {
          control.clearValidators()
        } else {
          control.setValidators([Validators.required])
        }
        control.updateValueAndValidity()
        this.cdr.detectChanges()
      }
    })
  }

  #getFormValueFromPasswords(passwords: string[]): string {
    if (!passwords || passwords.length === 0) {
      return ''
    }
    return passwords.join('\n')
  }
}
