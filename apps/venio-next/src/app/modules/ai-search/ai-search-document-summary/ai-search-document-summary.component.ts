import {
  ChangeDetectionStrategy,
  Component,
  inject,
  OnDestroy,
  OnInit,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { PopoverModule } from '@progress/kendo-angular-tooltip'
import { Subject, takeUntil } from 'rxjs'
import { AiFacade, AiSearchResult } from '@venio/data-access/ai'
import { marked } from 'marked'

@Component({
  selector: 'venio-ai-search-document-summary',
  standalone: true,
  imports: [CommonModule, PopoverModule],
  templateUrl: './ai-search-document-summary.component.html',
  styleUrl: './ai-search-document-summary.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AiSearchDocumentSummaryComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  public readonly aiFacade = inject(AiFacade)

  public readonly selectedSummary = signal<AiSearchResult>(undefined)

  public ngOnInit(): void {
    this.#selectSelectedDocumentSummary()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public formatMarkdownSummary(text: string): string {
    const value = (text || '').trim()
    if (!value) {
      return ''
    }

    return marked(value) as string
  }

  #selectSelectedDocumentSummary(): void {
    this.aiFacade.selectSelectedDocumentSummary$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((selectedDocumentSummary) => {
        this.selectedSummary.set(selectedDocumentSummary)
      })
  }
}
