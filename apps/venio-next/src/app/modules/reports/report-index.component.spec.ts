import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReportIndexComponent } from './report-index.component'
import { provideMockStore } from '@ngrx/store/testing'
import { StoreModule } from '@ngrx/store'
import { EffectsModule } from '@ngrx/effects'
import { provideRouter } from '@angular/router'
import {
  DocumentsFacade,
  FieldFacade,
  SearchFacade,
} from '@venio/data-access/review'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('ReportIndexComponent', () => {
  let component: ReportIndexComponent
  let fixture: ComponentFixture<ReportIndexComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        ReportIndexComponent,
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        DocumentsFacade,
        SearchFacade,
        FieldFacade,
        provideMockStore({}),
        provideRouter([]),
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ReportIndexComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
