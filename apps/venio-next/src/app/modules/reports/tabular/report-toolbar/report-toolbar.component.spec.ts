import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReportToolbarComponent } from './report-toolbar.component'
import { provideMockStore } from '@ngrx/store/testing'
import { provideAnimations } from '@angular/platform-browser/animations'
import {
  AppIdentitiesTypes,
  IframeMessengerModule,
  WINDOW,
  windowFactory,
} from '@venio/data-access/iframe-messenger'
import { PLATFORM_ID } from '@angular/core'
import { EffectsModule } from '@ngrx/effects'
import { StoreModule } from '@ngrx/store'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { StartupsFacade } from '@venio/data-access/review'

describe('ReportToolbarComponent', () => {
  let component: ReportToolbarComponent
  let fixture: ComponentFixture<ReportToolbarComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        ReportToolbarComponent,
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
        IframeMessengerModule.forRoot({
          origin: '*',
          iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
        }),
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        provideMockStore({}),
        provideAnimations(),
        {
          provide: WINDOW,
          useFactory: windowFactory,
          deps: [PLATFORM_ID],
        },
        {
          provide: StartupsFacade,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ReportToolbarComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
