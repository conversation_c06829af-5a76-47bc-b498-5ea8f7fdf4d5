import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  inject,
  On<PERSON><PERSON>roy,
  signal,
  OnInit,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ReportUserDropdownComponent } from '../report-user-dropdown/report-user-dropdown.component'
import { ReportDatePickerComponent } from '../report-date-picker/report-date-picker.component'
import { ReportGenerateInfoComponent } from '../report-generate-info/report-generate-info.component'
import { ReportActionToolbarComponent } from '../report-action-toolbar/report-action-toolbar.component'
import { ReportTypeDropdownComponent } from '../report-type-dropdown/report-type-dropdown.component'
import { ReportGenerateButtonComponent } from '../report-generate-button/report-generate-button.component'
import { Subject, takeUntil } from 'rxjs'
import { ReportsFacade } from '@venio/data-access/reports'
import { ReportTypes } from '@venio/shared/models/constants'
import { ReportProjectDropdownComponent } from '../report-project-dropdown/report-project-dropdown.component'
import { ReportDeleteModeDropdownComponent } from '../report-delete-mode-dropdown/report-delete-mode-dropdown.component'
import { ConfigService } from '@venio/data-access/control-settings'
import { ReportLegalHoldComponent } from '../report-legal-hold/report-legal-hold.component'
import { ActivateDeactivateCustodianReportComponent } from '../activate-deactivate-custodian-report/activate-deactivate-custodian-report.component'

@Component({
  selector: 'venio-report-toolbar',
  standalone: true,
  imports: [
    CommonModule,
    ReportUserDropdownComponent,
    ReportDatePickerComponent,
    ReportGenerateInfoComponent,
    ReportActionToolbarComponent,
    ReportTypeDropdownComponent,
    ReportGenerateButtonComponent,
    ReportProjectDropdownComponent,
    ReportDeleteModeDropdownComponent,
    ReportLegalHoldComponent,
    ActivateDeactivateCustodianReportComponent,
  ],
  templateUrl: './report-toolbar.component.html',
  styleUrl: './report-toolbar.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReportToolbarComponent
  implements OnDestroy, AfterViewInit, OnInit
{
  private readonly toDestroy$ = new Subject<void>()

  private readonly reportFacade = inject(ReportsFacade)

  public selectedReportType = signal<ReportTypes>(undefined)

  public get isDatePickerVisible(): boolean {
    return (
      this.selectedReportType() !== ReportTypes.LOCKED_USERS_REPORTS &&
      this.selectedReportType() !== ReportTypes.PROJECT_ACCESS_REPORT &&
      this.selectedReportType() !==
        ReportTypes.ACTIVATED_DEACTIVATED_CUSTODIAN_REPORT &&
      this.selectedReportType() !== ReportTypes.MATTER_DETAIL_REPORT
    )
  }

  public get isProjectDropdownVisible(): boolean {
    return (
      this.selectedReportType() === ReportTypes.DATA_EXPORT_AND_DOWNLOAD_REPORTS
    )
  }

  public get isDeletedModeDropdownVisible(): boolean {
    return this.selectedReportType() === ReportTypes.DELETED_EXPORTS
  }

  public get isProjectAndUserDropdownVisible(): boolean {
    return this.selectedReportType() === ReportTypes.PROJECT_ACCESS_REPORT
  }

  public get isUserAndProjectDropdownVisible(): boolean {
    return this.selectedReportType() === ReportTypes.ACTIVITY_REPORT
  }

  public get isActivateDeactivateCustodianReport(): boolean {
    return (
      this.selectedReportType() ===
      ReportTypes.ACTIVATED_DEACTIVATED_CUSTODIAN_REPORT
    )
  }

  public get isLegalHoldReport(): boolean {
    return this.selectedReportType() === ReportTypes.MATTER_DETAIL_REPORT
  }

  private readonly configService = inject(ConfigService)

  public legalHoldLicense = false

  public ngOnInit(): void {
    this.configService
      .fetchLicenseStatus$('FEATURE', 'LEGAL HOLD')
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((isEnabled: boolean) => {
        this.legalHoldLicense = Boolean(isEnabled)
      })
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  public ngAfterViewInit(): void {
    this.#selectSelectedReportType()
  }

  #selectSelectedReportType(): void {
    this.reportFacade.selectedSelectedReportType$
      .pipe(takeUntil(this.toDestroy$))
      .subscribe((reportType) => {
        this.selectedReportType.set(reportType)
      })
  }
}
