<kendo-multiselect
  [filterable]="true"
  [clearButton]="false"
  [virtual]="true"
  [listHeight]="300"
  [checkboxes]="true"
  [autoClose]="false"
  [tagMapper]="tagMapper"
  [value]="selectedDeletedModes()"
  #deletedModeSelection
  (filterChange)="filterDeletedModes($event)"
  (valueChange)="deletedModeSelectionChange($event)"
  class="t-min-w-[18rem] t-max-w-[18rem]"
  [data]="filteredDeletedModes()"
  (removeTag)="removeTag($event)"
  [valuePrimitive]="true"
  valueField="modeID"
  textField="modeName">
  <ng-template kendoMultiSelectHeaderTemplate let-dataItem>
    <div
      class="t-flex t-p-2 t-items-center t-h-full t-w-full t-relative t-z-50"
      (click)="handleAllDeletedModesClick($event, allDeletedMode)">
      <input
        [checked]="isAllDeletedModesChecked()"
        (click)="handleAllDeletedModesClick($event, allDeletedMode)"
        (change)="allDeletedModesSelectionChange($event.target['checked'])"
        type="checkbox"
        #allDeletedMode
        id="all-deleted-mode"
        kendoCheckBox />
      <kendo-label
        (click)="handleAllDeletedModesClick($event, allDeletedMode)"
        class="k-checkbox-label t-w-full t-h-full"
        for="all-deleted-mode"
        text="All"></kendo-label>
    </div>
  </ng-template>
  <ng-template kendoSuffixTemplate>
    <kendo-svg-icon
      class="t-cursor-pointer"
      (click)="chevDownIconClick(deletedModeSelection)"
      [icon]="chevronDownIcon"></kendo-svg-icon>
  </ng-template>
  <ng-template kendoMultiSelectGroupTagTemplate let-dataItems>
    {{ dataItems.length }} mode(s) selected
  </ng-template>
</kendo-multiselect>
