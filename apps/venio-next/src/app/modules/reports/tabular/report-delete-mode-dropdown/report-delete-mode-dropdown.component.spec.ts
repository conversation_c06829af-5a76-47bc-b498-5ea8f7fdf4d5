import { TestBed, ComponentFixture } from '@angular/core/testing'
import { ReportDeleteModeDropdownComponent } from './report-delete-mode-dropdown.component'
import { ReportsFacade } from '@venio/data-access/reports'
import { DeletedExportFacade } from '@venio/data-access/common'
import { Subject } from 'rxjs'
import { DeletedExportModeModel } from '@venio/shared/models/interfaces'
import { ReportTypes } from '@venio/shared/models/constants'
import { BrowserAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('ReportDeleteModeDropdownComponent', () => {
  let component: ReportDeleteModeDropdownComponent
  let fixture: ComponentFixture<ReportDeleteModeDropdownComponent>
  let reportsFacadeMock: any
  let deletedExportFacadeMock: any

  beforeEach(async () => {
    // GIVEN the ReportDeleteModeDropdownComponent with necessary dependencies mocked
    reportsFacadeMock = {
      selectedSelectedReportType$: new Subject<ReportTypes>(),
      storeSelectedDeleteMode: jest.fn(),
    }

    deletedExportFacadeMock = {
      selectDeletedExportModeListSuccessResponse$: new Subject<{
        data: DeletedExportModeModel[]
      }>(),
      fetchDeletedExportModeList: jest.fn(),
    }

    await TestBed.configureTestingModule({
      imports: [ReportDeleteModeDropdownComponent, BrowserAnimationsModule],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: ReportsFacade, useValue: reportsFacadeMock },
        { provide: DeletedExportFacade, useValue: deletedExportFacadeMock },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ReportDeleteModeDropdownComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  afterEach(() => {
    // Clean up after each test
    fixture.destroy()
  })

  it('should subscribe to selected report type when the component initializes', () => {
    // GIVEN a spy on the selectedSelectedReportType$ subscription
    const subscribeSpy = jest.spyOn(
      reportsFacadeMock.selectedSelectedReportType$,
      'subscribe'
    )

    // WHEN the component initializes
    component.ngOnInit()

    // THEN it should subscribe to selected report type
    expect(subscribeSpy).toHaveBeenCalled()
  })

  it('should fetch and subscribe to deleted modes when the component initializes', () => {
    // GIVEN spies on the deleted modes data subscription and fetch method
    const subscribeSpy = jest.spyOn(
      deletedExportFacadeMock.selectDeletedExportModeListSuccessResponse$,
      'subscribe'
    )

    // WHEN the component initializes
    component.ngOnInit()

    // THEN it should fetch deleted modes data and set up a subscription
    expect(
      deletedExportFacadeMock.fetchDeletedExportModeList
    ).toHaveBeenCalled()
    expect(subscribeSpy).toHaveBeenCalled()
  })

  it('should update the deleted modes when deleted modes data is received', async () => {
    // GIVEN test data and a setup to listen for deleted modes data updates
    const testData: DeletedExportModeModel[] = [
      { modeID: 1, modeName: 'Mode 1' },
      { modeID: 2, modeName: 'Mode 2' },
    ]

    const promise = new Promise<void>((resolve) => {
      deletedExportFacadeMock.selectDeletedExportModeListSuccessResponse$.subscribe(
        (response) => {
          // THEN the deleted modes should be updated when data is received
          expect(response.data).toEqual(testData)
          resolve()
        }
      )
    })

    // WHEN deleted modes data is emitted
    deletedExportFacadeMock.selectDeletedExportModeListSuccessResponse$.next({
      data: testData,
    })

    // Wait for the promise to resolve
    await promise
  })

  it('should clean up subscriptions when the component is destroyed', () => {
    // GIVEN a spy on the subject's next and complete methods
    const nextSpy = jest.spyOn(component['toDestroy$'], 'next')
    const completeSpy = jest.spyOn(component['toDestroy$'], 'complete')

    // WHEN the component is destroyed
    component.ngOnDestroy()

    // THEN it should complete all subscriptions
    expect(nextSpy).toHaveBeenCalled()
    expect(completeSpy).toHaveBeenCalled()
  })
})
