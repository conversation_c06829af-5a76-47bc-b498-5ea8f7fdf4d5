import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReportProjectDropdownComponent } from './report-project-dropdown.component'
import { provideMockStore } from '@ngrx/store/testing'
import { provideAnimations } from '@angular/platform-browser/animations'

describe('ReportUserDropdownComponent', () => {
  let component: ReportProjectDropdownComponent
  let fixture: ComponentFixture<ReportProjectDropdownComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReportProjectDropdownComponent],
      providers: [provideMockStore({}), provideAnimations()],
    }).compileComponents()

    fixture = TestBed.createComponent(ReportProjectDropdownComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
