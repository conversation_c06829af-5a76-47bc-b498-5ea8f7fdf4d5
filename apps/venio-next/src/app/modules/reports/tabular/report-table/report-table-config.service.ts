import {
  Injectable,
  TemplateRef,
  signal,
  computed,
  OnDestroy,
} from '@angular/core'
import { ReportsFacade } from '@venio/data-access/reports'
import { NotificationService, Type } from '@progress/kendo-angular-notification'
import { Subject, combineLatest } from 'rxjs'
import { filter, takeUntil, debounceTime, tap } from 'rxjs/operators'
import dayjs from 'dayjs'
import { ColumnConfig, CSVGeneratorWorkerService } from '@venio/util/utilities'
import { GridConfig } from '../reusable-grid/grid-config.model'
import {
  LoginLogoutEntry,
  LoginLogoutReport,
  LockedUserEntry,
} from '@venio/shared/models/interfaces'
import { ReusableGridColumnConfig } from '../reusable-grid/column-config.model'
import {
  ExportReportFormatTypes,
  ReportTypes,
} from '@venio/shared/models/constants'
import { GridComponent } from '@progress/kendo-angular-grid'

@Injectable()
export class ReportTableConfigService implements OnDestroy {
  #toDestroy$ = new Subject<void>()

  private _grid: GridComponent

  private _templates: Record<string, TemplateRef<any>>

  public currentDateTime: string

  public set setGridComponent(grid: GridComponent) {
    this._grid = grid
  }

  public set setTemplates(templates: Record<string, TemplateRef<any>>) {
    this._templates = templates
  }

  private get grid(): GridComponent {
    return this._grid
  }

  private get loginDateTemplate(): TemplateRef<any> {
    return this._templates['loginDateTemplate']
  }

  private get logoutDateTemplate(): TemplateRef<any> {
    return this._templates['logoutDateTemplate']
  }

  public get lockedDateTemplate(): TemplateRef<any> {
    return this._templates['lockedDateTemplate']
  }

  public get dataExportStartDateTemplate(): TemplateRef<any> {
    return this._templates['dataExportStartDateTemplate']
  }

  public get dataExportEndDateTemplate(): TemplateRef<any> {
    return this._templates['dataExportEndDateTemplate']
  }

  public get userCreationPerformedDateTemplate(): TemplateRef<any> {
    return this._templates['userCreationPerformedDateTemplate']
  }

  public get roleChangeRoleFromToTemplate(): TemplateRef<any> {
    return this._templates['roleChangeRoleFromToTemplate']
  }

  public get roleChangeRoleUpdatedDateTemplate(): TemplateRef<any> {
    return this._templates['roleChangeRoleUpdatedDateTemplate']
  }

  public get deletedExportsDeletedOnDateTemplate(): TemplateRef<any> {
    return this._templates['deletedExportsDeletedOnDateTemplate']
  }

  public get pdfHeaderFooterTemplate(): TemplateRef<any> {
    return this._templates['pdfHeaderFooterTemplate']
  }

  public get projectAccessDateTemplate(): TemplateRef<any> {
    return this._templates['projectAccessDateTemplate']
  }

  public get activityDateTemplate(): TemplateRef<any> {
    return this._templates['activityDateTemplate']
  }

  public get sentOnDateTemplate(): TemplateRef<any> {
    return this._templates['sentOnDateTemplate']
  }

  public get acceptedOnDateTemplate(): TemplateRef<any> {
    return this._templates['acceptedOnDateTemplate']
  }

  public get legalHoldNoticeTemplate(): TemplateRef<any> {
    return this._templates['legalHoldNoticeTemplate']
  }

  public get legalHoldTemplateType(): TemplateRef<any> {
    return this._templates['legalHoldTemplateType']
  }

  public get legalHoldStatusTemplate(): TemplateRef<any> {
    return this._templates['legalHoldStatusTemplate']
  }

  public get legalHoldHiredDateTemplate(): TemplateRef<any> {
    return this._templates['legalHoldHiredDateTemplate']
  }

  public get legalHoldTerminatedDateTemplate(): TemplateRef<any> {
    return this._templates['legalHoldTerminatedDateTemplate']
  }

  constructor(
    private reportFacade: ReportsFacade,
    private notificationService: NotificationService
  ) {}

  public isReportLoading$ = this.reportFacade.selectIsReportLoading$

  public currentGridConfig = signal<GridConfig>({} as GridConfig)

  public paging = signal<{ pageSize: number; pageNumber: number }>(undefined)

  public selectedReportType = signal<ReportTypes>(undefined)

  public isPrint = signal<boolean>(false)

  public isAllPages = signal<boolean>(true)

  public reportData = signal<
    | LoginLogoutReport
    | { reportEntries: LockedUserEntry[]; totalHitCount: number }
  >({
    reportEntries: [],
    totalHitCount: 0,
  })

  public computedData = computed<LoginLogoutEntry[] | LockedUserEntry[]>(
    () => this.reportData()?.reportEntries || []
  )

  public exportableData = signal<LoginLogoutEntry[] | LockedUserEntry[]>([])

  public formattedCurrentDate = dayjs().format('DD-MM-YYYY_HH:mm.ss.sss')

  public currentDate: string

  public currentTime: string

  // Common configuration map
  private get reportTypeConfigs(): Partial<{
    [key in ReportTypes]: ReusableGridColumnConfig[]
  }> {
    return {
      [ReportTypes.LOG_IN_OUT_REPORTS]: [
        this.createColumnConfig('id', '#', 50, false),
        this.createColumnConfig('userName', 'Username', 200),
        this.createColumnConfig(
          'ipAddress',
          'IP Address',
          undefined,
          true,
          true
        ),
        this.createColumnConfig(
          'loginDate',
          'Login Date & Time',
          200,
          false,
          true,
          this.loginDateTemplate
        ),
        this.createColumnConfig(
          'logoutDate',
          'Logout Date & Time',
          200,
          false,
          true,
          this.logoutDateTemplate
        ),
        this.createColumnConfig('details', 'Details', 150),
      ],
      [ReportTypes.LOCKED_USERS_REPORTS]: [
        this.createColumnConfig('id', '#', 50, false),
        this.createColumnConfig('fullName', 'Full Name', 200),
        this.createColumnConfig('userName', 'Username', 200),
        this.createColumnConfig('globalUserRole', 'Global User Role', 200),
        this.createColumnConfig(
          'lockedDate',
          'Locked Date & Time',
          200,
          false,
          true,
          this.lockedDateTemplate
        ),
      ],
      [ReportTypes.CREATION_AND_DEACTIVATION_REPORTS]: [
        this.createColumnConfig('id', '#', 50),
        this.createColumnConfig('userName', 'Username', 200),
        this.createColumnConfig('action', 'Action', 150),
        this.createColumnConfig('performedBy', 'Performed By', 200),
        this.createColumnConfig(
          'performedDate',
          'Date & Time',
          150,
          false,
          true,
          this.userCreationPerformedDateTemplate
        ),
        this.createColumnConfig('reason', 'Reason', 300),
      ],
      [ReportTypes.DATA_EXPORT_AND_DOWNLOAD_REPORTS]: [
        this.createColumnConfig('id', '#', 50),
        this.createColumnConfig('projectName', 'Project Name', 200),
        this.createColumnConfig('exportName', 'Export Name', 200),
        this.createColumnConfig(
          'startDate',
          'Start Date & Time',
          180,
          false,
          true,
          this.dataExportStartDateTemplate
        ),
        this.createColumnConfig(
          'endDate',
          'End Date & Time',
          180,
          false,
          true,
          this.dataExportEndDateTemplate
        ),
        this.createColumnConfig('exportPath', 'Path', 300),
        this.createColumnConfig('userName', 'Downloaded by', 150),
        this.createColumnConfig('actionStatus', 'Action', 150),
      ],
      [ReportTypes.ROLE_CHANGE_REPORTS]: [
        this.createColumnConfig('id', '#', 50),
        this.createColumnConfig('userName', 'Username', 200),
        this.createColumnConfig(
          'oldRoleName',
          'Role From To',
          250,
          false,
          true,
          this.roleChangeRoleFromToTemplate
        ),
        this.createColumnConfig('roleUpdatedBy', 'Performed By', 200),
        this.createColumnConfig(
          'roleUpdatedDate',
          'Date & Time',
          150,
          false,
          true,
          this.roleChangeRoleUpdatedDateTemplate
        ),
      ],

      [ReportTypes.DELETED_EXPORTS]: [
        this.createColumnConfig('projectId', '#', 50),
        this.createColumnConfig('projectName', 'Project Name', 200),
        this.createColumnConfig('exportName', 'Export Name', 200),
        this.createColumnConfig('deleteMode', 'Deleted Mode', 200),
        this.createColumnConfig('deletedByUsername', 'Deleted By', 200),

        this.createColumnConfig(
          'deleteDate',
          'Deleted On',
          150,
          false,
          true,
          this.deletedExportsDeletedOnDateTemplate
        ),
      ],

      [ReportTypes.PROJECT_ACCESS_REPORT]: [
        this.createColumnConfig('id', '#', 50),
        this.createColumnConfig('projectName', 'Project Name', 200),
        this.createColumnConfig('userName', 'User Name', 200),
        this.createColumnConfig(
          'accessDate',
          'Last Access Date & Time',
          150,
          false,
          true,
          this.projectAccessDateTemplate
        ),
      ],
      [ReportTypes.ACTIVITY_REPORT]: [
        this.createColumnConfig('id', '#', 50),
        this.createColumnConfig('userName', 'User Name', 200),
        this.createColumnConfig('actionPerformed', 'Action Performed', 200),
        this.createColumnConfig(
          'activityDate',
          'Date & Time',
          150,
          false,
          true,
          this.activityDateTemplate
        ),
        this.createColumnConfig('fileId', 'Doc ID', 100),
        this.createColumnConfig('projectName', 'Project', 200),
        this.createColumnConfig('isLoadFile', 'Is Load File', 150),
      ],
      [ReportTypes.ACTIVATED_DEACTIVATED_CUSTODIAN_REPORT]: [
        this.createColumnConfig('id', '#', 30),
        this.createColumnConfig('HoldName', 'Hold Name', 80),
        this.createColumnConfig('FirstName', 'First Name', 80),
        this.createColumnConfig('LastName', 'Last Name', 80),
        this.createColumnConfig('Department', 'Department', 80),
        this.createColumnConfig('EmailAddress', 'Email Address', 140),
        this.createColumnConfig('Designation', 'Role/Title', 70),
        this.createColumnConfig('EmployeeStatus', 'Employment Status', 80),
        this.createColumnConfig('OfficeAddress', 'Physical Location', 100),
        this.createColumnConfig('TimeKeeperNumber', 'Time Keeper No', 100),
        this.createColumnConfig(
          'HiredOn',
          'Hired On',
          90,
          false,
          true,
          this.legalHoldHiredDateTemplate
        ),

        this.createColumnConfig(
          'TerminatedOn',
          'Terminated On',
          90,
          false,
          true,
          this.legalHoldTerminatedDateTemplate
        ),
      ],
      [ReportTypes.MATTER_DETAIL_REPORT]: [
        this.createColumnConfig('id', '#', 30),
        this.createColumnConfig('CustodianName', 'Custodian Name', 80),
        this.createColumnConfig('HoldName', 'Hold Name', 60),
        this.createColumnConfig('Department', 'Department', 60),
        this.createColumnConfig('EmailID', 'Email Address', 80),
        this.createColumnConfig(
          'Notice',
          'Notice',
          60,
          true,
          true,
          this.legalHoldNoticeTemplate
        ),
        this.createColumnConfig(
          'TemplateType',
          'Template Type',
          60,
          true,
          true,
          this.legalHoldTemplateType
        ),
        this.createColumnConfig(
          'Status',
          'Status',
          60,
          true,
          true,
          this.legalHoldStatusTemplate
        ),
        this.createColumnConfig(
          'SentOn',
          'Sent On',
          60,
          false,
          true,
          this.sentOnDateTemplate
        ),
        this.createColumnConfig(
          'AcceptedOn',
          'Accepted On',
          60,
          false,
          true,
          this.acceptedOnDateTemplate
        ),
      ],
    }
  }

  private get csvColumnConfigs(): Partial<{
    [key in ReportTypes]: ColumnConfig[]
  }> {
    return {
      [ReportTypes.LOG_IN_OUT_REPORTS]: [
        { title: '#', field: 'id' },
        'Username',
        'IP Address',
        {
          title: 'Login Date & Time',
          field: 'loginDate',
          mergeFields: ['loginTime'],
        },
        {
          title: 'Logout Date & Time',
          field: 'logoutDate',
          mergeFields: ['logoutTime'],
        },
        'Details',
      ],
      [ReportTypes.LOCKED_USERS_REPORTS]: [
        { title: '#', field: 'id' },
        'Full Name',
        'Username',
        'Global User Role',
        {
          title: 'Locked Date & Time',
          field: 'lockedDate',
          mergeFields: ['lockedTime'],
        },
      ],
      [ReportTypes.CREATION_AND_DEACTIVATION_REPORTS]: [
        { title: '#', field: 'id' },
        { title: 'Username', field: 'userName' },
        { title: 'Action', field: 'action' },
        { title: 'Performed By', field: 'performedBy' },
        {
          title: 'Date & Time',
          field: 'performedDate',
          mergeFields: ['performedTime'],
        },
        { title: 'Reason', field: 'reason' },
      ],
      [ReportTypes.DATA_EXPORT_AND_DOWNLOAD_REPORTS]: [
        { title: '#', field: 'id' },
        { title: 'Project Name', field: 'projectName' },
        { title: 'Export Name', field: 'exportName' },
        {
          title: 'Start Date & Time',
          field: 'startDate',
          mergeFields: ['startTime'],
        },
        {
          title: 'End Date & Time',
          field: 'endDate',
          mergeFields: ['endTime'],
        },
        { title: 'Path', field: 'exportPath' },
        { title: 'Downloaded by', field: 'userName' },
        { title: 'Action', field: 'actionStatus' },
      ],
      [ReportTypes.ROLE_CHANGE_REPORTS]: [
        { title: '#', field: 'id' },
        { title: 'Username', field: 'userName' },
        { title: 'Old Role', field: 'oldRoleName' },
        { title: 'New Role', field: 'newRoleName' },
        { title: 'Performed By', field: 'roleUpdatedBy' },
        { title: 'Date', field: 'roleUpdatedDate' },
        { title: 'Time', field: 'roleUpdatedTime' },
      ],
      [ReportTypes.DELETED_EXPORTS]: [
        { title: '#', field: 'id' },
        { title: 'Project Name', field: 'projectName' },
        { title: 'Export Name', field: 'exportName' },
        { title: 'Deleted Mode', field: 'deleteMode' },
        { title: 'Deleted By', field: 'deletedByUsername' },
        {
          title: 'Deleted Date',
          field: 'deleteDate',
          mergeFields: ['deleteTime'],
        },
      ],
      [ReportTypes.PROJECT_ACCESS_REPORT]: [
        { title: '#', field: 'id' },
        { title: 'Project Name', field: 'projectName' },
        { title: 'User Name', field: 'userName' },
        { title: 'Last Access Date', field: 'accessDate' },
        { title: 'Last Access Time', field: 'accessTime' },
      ],
      [ReportTypes.ACTIVITY_REPORT]: [
        { title: '#', field: 'id' },
        { title: 'User Name', field: 'userName' },
        { title: 'Action Performed', field: 'actionPerformed' },
        {
          title: 'Date & Time',
          field: 'actionPerformedDate',
          mergeFields: ['actionPerformedTime'],
        },
        { title: 'Doc ID', field: 'fileId' },
        { title: 'Project', field: 'projectName' },
        { title: 'Is Load File', field: 'isLoadFile' },
      ],
      [ReportTypes.MATTER_DETAIL_REPORT]: [
        { title: '#', field: 'id' },
        { title: 'Custodian Name', field: 'CustodianName' },
        { title: 'Hold Name', field: 'HoldName' },
        { title: 'Department', field: 'Department' },
        { title: 'Email Address', field: 'EmailID' },
        { title: 'Notice', field: 'Notice' },
        { title: 'Template Type', field: 'TemplateType' },
        { title: 'Status', field: 'Status' },
        { title: 'sentOn', field: 'SentOn' },
        { title: 'acceptedOn', field: 'AcceptedOn' },
      ],
      [ReportTypes.ACTIVATED_DEACTIVATED_CUSTODIAN_REPORT]: [
        { title: '#', field: 'id' },
        { title: 'First Name', field: 'FirstName' },
        { title: 'Last Name', field: 'LastName' },
        { title: 'Hold Name', field: 'holdName' },
        { title: 'Department', field: 'Department' },
        { title: 'Email Address', field: 'EmailAddress' },
        { title: 'Role/Title', field: 'Designation' },
        { title: 'Employment Status', field: 'EmployeeStatus ' },
        { title: 'Physical Location', field: 'OfficeAddress' },
        { title: 'TimeKeeper Number', field: 'TimeKeeperNumber' },
        { title: 'Hired On', field: 'HiredOn' },
        { title: 'Terminated On', field: 'TerminatedOn' },
      ],
    }
  }

  private get excelExportColumnConfigs(): Partial<{
    [key in ReportTypes]: ColumnConfig[]
  }> {
    return {
      [ReportTypes.LOG_IN_OUT_REPORTS]: [
        { title: '#', field: 'id' },
        { title: 'User Name', field: 'userName' },
        { title: 'IP Address', field: 'ipAddress' },
        { title: 'Login Date', field: 'loginDate' },
        { title: 'Login Time', field: 'loginTime' },
        { title: 'Logout Date', field: 'logoutDate' },
        { title: 'Logout Time', field: 'logoutTime' },
        { title: 'Details', field: 'details' },
      ],
      [ReportTypes.LOCKED_USERS_REPORTS]: [
        { title: '#', field: 'id' },
        { title: 'Full Name', field: 'fullName' },
        { title: 'User Name', field: 'userName' },
        { title: 'Global User Role', field: 'globalUserRole' },
        { title: 'Locked Date', field: 'lockedDate' },
        { title: 'Locked Time', field: 'lockedTime' },
      ],
      [ReportTypes.CREATION_AND_DEACTIVATION_REPORTS]: [
        { title: '#', field: 'id' },
        { title: 'Username', field: 'userName' },
        { title: 'Action', field: 'action' },
        { title: 'Performed By', field: 'performedBy' },
        { title: 'Date', field: 'performedDate' },
        { title: 'Time', field: 'performedTime' },
        { title: 'Reason', field: 'reason' },
      ],
      [ReportTypes.DATA_EXPORT_AND_DOWNLOAD_REPORTS]: [
        { title: '#', field: 'id' },
        { title: 'Project Name', field: 'projectName' },
        { title: 'Export Name', field: 'exportName' },
        { title: 'Start Date', field: 'startDate' },
        { title: 'Start Time', field: 'startTime' },
        { title: 'End Date', field: 'endTime' },
        { title: 'End Time', field: 'endTime' },
        { title: 'Path', field: 'exportPath' },
        { title: 'Downloaded by', field: 'userName' },
        { title: 'Action', field: 'actionStatus' },
      ],
      [ReportTypes.ROLE_CHANGE_REPORTS]: [
        { title: '#', field: 'id' },
        { title: 'Username', field: 'userName' },
        { title: 'Old Role', field: 'oldRoleName' },
        { title: 'New Role', field: 'newRoleName' },
        { title: 'Performed By', field: 'roleUpdatedBy' },
        { title: 'Date', field: 'roleUpdatedDate' },
        { title: 'Time', field: 'roleUpdatedTime' },
      ],
      [ReportTypes.DELETED_EXPORTS]: [
        { title: '#', field: 'id' },
        { title: 'Project Name', field: 'projectName' },
        { title: 'Export Name', field: 'exportName' },
        { title: 'Deleted Mode', field: 'deleteMode' },
        { title: 'Deleted By', field: 'deletedByUsername' },
        { title: 'Deleted Date', field: 'deleteDate' },
        { title: 'Deleted Time', field: 'deleteTime' },
      ],
      [ReportTypes.PROJECT_ACCESS_REPORT]: [
        { title: '#', field: 'id' },
        { title: 'Project Name', field: 'projectName' },
        { title: 'User Name', field: 'userName' },
        { title: 'Last Access Date', field: 'accessDate' },
        { title: 'Last Access Time', field: 'accessTime' },
      ],
      [ReportTypes.ACTIVITY_REPORT]: [
        { title: '#', field: 'id' },
        { title: 'User Name', field: 'userName' },
        { title: 'Action Performed', field: 'actionPerformed' },
        { title: 'Action Performed Date', field: 'actionPerformedDate' },
        { title: 'Action Performed Time', field: 'actionPerformedTime' },
        { title: 'Doc ID', field: 'fileId' },
        { title: 'Project', field: 'projectName' },
        { title: 'Is Load File', field: 'isLoadFile' },
      ],

      [ReportTypes.MATTER_DETAIL_REPORT]: [
        { title: '#', field: 'id' },
        { title: 'Custodian Name', field: 'CustodianName' },
        { title: 'Hold Name', field: 'HoldName' },
        { title: 'Department', field: 'Department' },
        { title: 'Email Address', field: 'EmailID' },
        { title: 'Notice', field: 'Notice' },
        { title: 'Template Type', field: 'TemplateType' },
        { title: 'Status', field: 'Status' },
        { title: 'sentOn', field: 'SentOn' },
        { title: 'acceptedOn', field: 'AcceptedOn' },
      ],

      [ReportTypes.ACTIVATED_DEACTIVATED_CUSTODIAN_REPORT]: [
        { title: '#', field: 'id' },
        { title: 'First Name', field: 'FirstName' },
        { title: 'Last Name', field: 'LastName' },
        { title: 'Hold Name', field: 'HoldName' },
        { title: 'Department', field: 'Department' },
        { title: 'Email Address', field: 'EmailAddress' },
        { title: 'Role/Title', field: 'Designation' },
        { title: 'Employment Status', field: 'EmployeeStatus' },
        { title: 'Physical Location', field: 'OfficeAddress' },
        { title: 'TimeKeeper Number', field: 'TimeKeeperNumber' },
        { title: 'Hired On', field: 'HiredOn' },
        { title: 'Terminated On', field: 'TerminatedOn' },
      ],
    }
  }

  public initGridConfig(): void {
    const reportColumnConfigs = this.getColumnConfigs()

    const pdfGridOptions = {
      creator: 'Venio Systems',
      margin: { top: '2cm', left: '1cm', right: '1cm', bottom: '1cm' },
      fileName: `${this.selectedReportType()}_${this.formattedCurrentDate}.pdf`,
      scale: 0.6,
      delay: 1000,
      allPages: this.isAllPages(),
      autoPrint: this.isPrint(),
      paperSize: 'A4',
      landscape: true,
      repeatHeaders: true,
      onExport: this.onPDFExport.bind(this),
      pdfTemplate: this.pdfHeaderFooterTemplate,
    }

    const excelGridOptions = {
      fileName: `${this.selectedReportType()}_${
        this.formattedCurrentDate
      }.xlsx`,
      onExport: this.onExcelExport.bind(this),
      excelColumns: this.excelExportColumnConfigs[this.selectedReportType()],
    }

    this.currentGridConfig.set({
      isLoading: this.isReportLoading$,
      containerWidth: 0,
      containerHeight: 0,
      columnConfigs: reportColumnConfigs,
      pdfGridOptions: pdfGridOptions,
      excelGridOptions: excelGridOptions,
      noRecordsMessage: 'No records found',
    })
  }

  private createColumnConfig(
    field: string,
    title: string,
    width: number,
    autoSize = true,
    breakWords = false,
    template: TemplateRef<any> = null
  ): ReusableGridColumnConfig {
    return {
      field,
      title,
      width,
      filterable: autoSize,
      autoSize,
      class: `!t-whitespace-normal${breakWords ? ' t-break-words' : ''}`,
      headerClass: `t-text-primary !t-whitespace-normal${
        breakWords ? ' t-break-words' : ''
      }`,
      template,
    }
  }

  private getColumnConfigs(): ReusableGridColumnConfig[] {
    const reportType = this.selectedReportType()
    if (!reportType) return []
    return this.reportTypeConfigs[reportType]
  }

  #setCurrentDate(): void {
    this.reportFacade.fetchDBServerDateTimeInfo()
    this.reportFacade.selectFetchDBServerDateTimeInfoSuccess$
      .pipe(takeUntil(this.#toDestroy$))
      .subscribe((response) => {
        if (response && response.data) {
          this.currentDateTime = response?.data?.dbServerDateTime
        }
      })
  }

  #showMessage(content = '', type: Type): void {
    if (!content?.trim()) return

    const notificationRef = this.notificationService.show({
      content,
      type,
      animation: { type: 'fade', duration: 300 },
      hideAfter: 10000,
      width: 300,
    })

    // When the user clicks on the notification, it should hide
    notificationRef.notification.location.nativeElement.onclick = (): void => {
      notificationRef.hide()
    }
  }

  #resetExportState(): void {
    this.reportFacade.resetReportState([
      'isReportExporting',
      'exportEvent',
      'fetchExportDataSuccess',
      'fetchExportDataError',
    ])
  }

  public selectPaging(): void {
    this.reportFacade.selectPaging$
      .pipe(takeUntil(this.#toDestroy$))
      .subscribe((paging) => this.paging.set(paging))
  }

  public calculateId(
    pageNumber: number,
    pageSize: number,
    index: number
  ): number {
    return (pageNumber - 1) * pageSize + (index + 1)
  }

  public selectReportResponses(): void {
    combineLatest([
      this.reportFacade.selectFetchReportSuccessResponse$,
      this.reportFacade.selectFetchReportErrorResponse$,
      this.reportFacade.selectPaging$,
    ])
      .pipe(takeUntil(this.#toDestroy$))
      .subscribe(([success, error, paging]) => {
        const isError = Boolean(error?.status)
        const style = success?.message ? 'success' : 'error'
        const message = success?.message || error?.message

        if (isError) {
          this.#showMessage(message, { style })
          return
        }

        const { reportEntries, totalHitCount } = success?.data || {}
        const { pageNumber, pageSize } = paging

        this.reportData.set({
          reportEntries: ((reportEntries as LoginLogoutEntry[]) || []).map(
            (report, index) =>
              ({
                ...report,
                id: this.calculateId(pageNumber, pageSize, index),
              } as LoginLogoutEntry)
          ),
          totalHitCount,
        })

        this.#setCurrentDate()
      })
  }

  public exportCSV(data: LoginLogoutEntry[]): void {
    const advancedColumns = this.csvColumnConfigs[this.selectedReportType()]
    const csvWorkerService = new CSVGeneratorWorkerService()

    csvWorkerService
      .generateCSV(
        `${this.selectedReportType()}_${this.formattedCurrentDate}`,
        advancedColumns,
        data
      )
      .then((isSuccess) => {
        // Once the export is done, reset the export event state for the next export
        this.#resetExportState()
        this.reportFacade.setIsReportLoading(false)
      })
      .catch(() => {
        this.#showMessage('Failed to export CSV file', { style: 'error' })
        // Once the export is done, reset the export event state for the next export
        this.#resetExportState()
        this.reportFacade.setIsReportLoading(false)
      })
  }

  public exportGridData(data: any): void {
    // actionType is types of print or export
    // exportType is types of pdf or excel, or any file format
    const { actionType, exportType, page } = data
    const isOpenPrint = actionType === ExportReportFormatTypes.PRINT
    // for print as well, we need to toggle the print flag and call the saveAsPDF method
    const type: ExportReportFormatTypes = isOpenPrint
      ? ExportReportFormatTypes.PDF
      : exportType

    // additional parameter to control kendo pdf export settings
    this.isPrint.set(isOpenPrint)
    this.isAllPages.set(page === 'all')

    switch (type) {
      case ExportReportFormatTypes.PRINT:
      case ExportReportFormatTypes.PDF:
        this.#showMessage('Exporting PDF file...', { style: 'success' })
        this.grid.saveAsPDF()
        break
      case ExportReportFormatTypes.EXCEL:
        this.#showMessage('Exporting Excel file...', { style: 'success' })
        this.reportFacade.setIsReportLoading(true)
        this.grid.saveAsExcel()
        break
    }

    this.#resetExportState()
  }

  public selectExportReportToFormat(): void {
    combineLatest([
      this.reportFacade.selectExportReportFormatType$.pipe(
        tap(() => this.reportFacade.fetchExportableReports())
      ),
      this.reportFacade.selectFetchExportableReportSuccess$,
    ])
      .pipe(
        filter(([event, _]) => Boolean(event)),
        filter(
          ([event, response]) =>
            Boolean(event) && Boolean(response?.data?.totalHitCount)
        ),
        debounceTime(300),
        takeUntil(this.#toDestroy$)
      )
      .subscribe(([event, success]) => {
        // first, we need to make a copy of the original
        const copyPagedData = { ...this.reportData() }
        const { totalHitCount } = success.data
        let { reportEntries } = success.data
        if (
          this.selectedReportType() === ReportTypes.MATTER_DETAIL_REPORT &&
          event['exportType'] !== ExportReportFormatTypes.PDF
        ) {
          reportEntries = this.#flattenCustodianData(reportEntries)
        }
        const exportData = (reportEntries as LoginLogoutEntry[]).map(
          (report, index) =>
            ({
              ...report,
              id: this.calculateId(1, totalHitCount, index),
            } as LoginLogoutEntry)
        )

        const isCSV = event['exportType'] === ExportReportFormatTypes.CSV
        // Here, we use our own CSV export engine to export the data
        if (isCSV) {
          this.#showMessage('Exporting CSV file...', { style: 'success' })
          this.reportFacade.setIsReportLoading(true)
          this.exportCSV(exportData)
          return
        }

        this.exportableData.set(exportData)

        this.reportData.set({
          reportEntries: exportData,
          totalHitCount,
        })

        setTimeout(() => this.exportGridData(event), 100)

        // Once we pass the exportable data to reusableGrid and export is in progress, we need to reset the data back to original
        setTimeout(() => {
          this.reportData.set(copyPagedData)
          this.exportableData.set([])
        }, 3000)
      })
  }

  public selectSelectedReportType(): void {
    this.reportFacade.selectedSelectedReportType$
      .pipe(
        filter((type) => Boolean(type)),
        takeUntil(this.#toDestroy$)
      )
      .subscribe((type) => {
        this.reportFacade.resetReportState([
          'fetchReportSuccessResponse',
          'fetchReportErrorResponse',
          'selectedUsers',
        ])
        this.selectedReportType.set(type)
        this.initGridConfig()
      })
  }

  public ngOnDestroy(): void {
    this.#toDestroy$.next()
    this.#toDestroy$.complete()
  }

  public onExcelExport(event: any): void {
    this.reportFacade.setIsReportLoading(false)
    // TODO
  }

  public onPDFExport(event: any): void {
    // TODO
  }

  #flattenCustodianData(custodians: any[]): {
    CustodianId: number
    CustodianName: string
    HoldName: string
    EmailID: string
    Notice: string
    TemplateType: string | null
    Status: string
    SentOn: string | null
    AcceptedOn: string | null
  }[] {
    return custodians.flatMap((custodian) =>
      custodian.Notices.map((notice) => ({
        CustodianId: custodian.CustodianId,
        CustodianName: custodian.CustodianName,
        HoldName: custodian.HoldName,
        Department: custodian.Department,
        EmailID: custodian.EmailID,
        Notice: notice.Notice,
        TemplateType: notice.TemplateType,
        Status: notice.Status,
        SentOn: notice.SentOn,
        AcceptedOn: notice.AcceptedOn,
      }))
    )
  }
}
