<div
  class="t-relative t-flex t-flex-1 t-justify-stretch t-items-stretch t-w-full t-h-full t-min-h-[10rem] t-min-w-full report-grid"
  #container>
  <venio-reusable-grid
    [data]="reportTableConfigService.computedData()"
    [container]="container"
    [config]="reportTableConfigService.currentGridConfig()" />
</div>

<ng-template #loginDateTemplate let-dataItem="dataItem">
  <span
    >{{ dataItem?.loginDate }}
    <small class="t-text-[#A7A9AA]">{{ dataItem.loginTime }}</small></span
  >
</ng-template>

<ng-template #logoutDateTemplate let-dataItem="dataItem">
  <span
    >{{ dataItem?.logoutDate }}
    <small class="t-text-[#A7A9AA]">{{ dataItem.logoutTime }}</small></span
  >
</ng-template>

<ng-template #lockedDateTemplate let-dataItem="dataItem">
  <span
    >{{ dataItem?.lockedDate }}
    <small class="t-text-[#A7A9AA]">{{ dataItem.lockedTime }}</small></span
  >
</ng-template>

<ng-template #dataExportStartDateTemplate let-dataItem="dataItem">
  <span
    >{{ dataItem?.startDate }}
    <small class="t-text-[#A7A9AA]">{{ dataItem.startTime }}</small></span
  >
</ng-template>

<ng-template #dataExportEndDateTemplate let-dataItem="dataItem">
  <span
    >{{ dataItem?.endDate }}
    <small class="t-text-[#A7A9AA]">{{ dataItem.endTime }}</small></span
  >
</ng-template>

<ng-template #userCreationPerformedDateTemplate let-dataItem="dataItem">
  <span
    >{{ dataItem?.performedDate }}
    <small class="t-text-[#A7A9AA]">{{ dataItem.performedTime }}</small></span
  >
</ng-template>

<ng-template #roleChangeRoleFromToTemplate let-dataItem="dataItem">
  <span>{{ dataItem?.oldRoleName }} to {{ dataItem?.newRoleName }}</span>
</ng-template>

<ng-template #roleChangeRoleUpdatedDateTemplate let-dataItem="dataItem">
  <span
    >{{ dataItem?.roleUpdatedDate }}
    <small class="t-text-[#A7A9AA]">{{ dataItem.roleUpdatedTime }}</small></span
  >
</ng-template>

<ng-template #deletedExportsDeletedOnDateTemplate let-dataItem="dataItem">
  <span
    >{{ dataItem?.deleteDate | date : 'MM dd YYYY' }}
    <small class="t-text-[#A7A9AA]">{{ dataItem?.deleteTime }}</small></span
  >
</ng-template>

<ng-template #projectAccessDateTemplate let-dataItem="dataItem">
  <span
    >{{ dataItem?.accessDate }}
    <small class="t-text-[#A7A9AA]">{{ dataItem.accessTime }}</small></span
  >
</ng-template>

<ng-template #activityDateTemplate let-dataItem="dataItem">
  <span
    >{{ dataItem?.actionPerformedDate }}
    <small class="t-text-[#A7A9AA]">{{
      dataItem.actionPerformedTime
    }}</small></span
  >
</ng-template>

<ng-template #dateDisplayTemplate let-date="date">
  @if (date !== null) {
  <div class="t-flex t-flex-col t-my-1">
    {{ date | date : 'MM dd yyyy' }}
    <small class="t-text-[#A7A9AA]">
      {{ date | date : 'h:mm:ss a' }}
    </small>
  </div>
  } @else {
  <div class="t-flex t-flex-col t-my-1 t-h-[36px]"></div>
  }
</ng-template>

<!-- Sent On Template -->
<ng-template #sentOnDateTemplate let-dataItem="dataItem">
  @for (notice of dataItem.Notices; track notice) {
  <ng-container
    [ngTemplateOutlet]="dateDisplayTemplate"
    [ngTemplateOutletContext]="{ date: notice?.SentOn }">
  </ng-container>
  }
</ng-template>

<!-- Accepted On Template -->
<ng-template #acceptedOnDateTemplate let-dataItem="dataItem">
  @for (notice of dataItem.Notices; track notice) {
  <ng-container
    [ngTemplateOutlet]="dateDisplayTemplate"
    [ngTemplateOutletContext]="{ date: notice?.AcceptedOn }">
  </ng-container>
  }
</ng-template>

<ng-template #legalHoldNoticeTemplate let-dataItem="dataItem">
  <ng-container *ngFor="let notice of dataItem.Notices">
    <p class="t-py-1 t-h-[36px]">{{ notice.Notice }}</p>
  </ng-container>
</ng-template>

<ng-template #legalHoldTemplateType let-dataItem="dataItem">
  <ng-container *ngFor="let notice of dataItem.Notices">
    <p class="t-py-1 t-h-[36px]">{{ notice.TemplateType }}</p>
  </ng-container>
</ng-template>

<ng-template #legalHoldStatusTemplate let-dataItem="dataItem">
  <ng-container *ngFor="let notice of dataItem.Notices">
    <div class="t-py-1 t-h-[36px]">{{ notice.Status }}</div>
  </ng-container>
</ng-template>
<ng-template #legalHoldHiredDateTemplate let-dataItem="dataItem">
  <p class="t-flex t-flex-col t-my-1">
    {{ dataItem?.HiredOn | date : 'MM dd yyyy' }}
  </p>
</ng-template>

<ng-template #legalHoldTerminatedDateTemplate let-dataItem="dataItem">
  <p class="t-flex t-flex-col t-my-1">
    {{ dataItem?.TerminatedOn | date : 'MM dd yyyy' }}
  </p>
</ng-template>

<ng-template
  #pdfHeaderFooterTemplate
  let-pageNum="pageNum"
  let-totalPages="totalPages">
  <div
    class="t-absolute t-top-0 t-right-0 t-left-0 t-px-[43px] t-flex t-justify-between t-items-center t-pt-4 t-text-primary">
    <div class="t-text-left t-uppercase">
      {{ reportTableConfigService.selectedReportType() }}
    </div>
    <div>
      <div class="t-text-right">
        <span class="t-text-[#000000BC] t-text-[12px]">Created By</span>
        <span class="t-text-[#000000]"> {{ userName$ | async }} </span>
      </div>
      <div class="t-text-right">
        <span class="t-text-[#000000BC] t-text-[12px]">Generated On:</span>
        <span class="t-text-[#000000]">
          {{ reportTableConfigService?.currentDateTime | date : 'MM dd YYYY' }}
          <small>{{
            reportTableConfigService?.currentDateTime | date : 'hh:mm a'
          }}</small></span
        >
      </div>
    </div>
  </div>
  <div class="t-absolute t-bottom-0 t-right-0 t-px-[43px] t-py-4">
    Page {{ pageNum }} of {{ totalPages }}
  </div>
</ng-template>
