import { ChangeDetectionStrategy, Component } from '@angular/core'
import { CommonModule } from '@angular/common'
import { ReportToolbarComponent } from '../report-toolbar/report-toolbar.component'
import { ReportTableComponent } from '../report-table/report-table.component'

@Component({
  selector: 'venio-report-container',
  standalone: true,
  imports: [CommonModule, ReportToolbarComponent, ReportTableComponent],
  templateUrl: './report-container.component.html',
  styleUrl: './report-container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReportContainerComponent {}
