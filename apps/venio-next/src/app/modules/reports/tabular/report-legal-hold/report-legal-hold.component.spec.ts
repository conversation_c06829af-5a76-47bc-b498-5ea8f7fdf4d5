import {
  TestBed,
  ComponentFixture,
  fakeAsync,
  tick,
} from '@angular/core/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { ReportLegalHoldComponent } from './report-legal-hold.component'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient } from '@angular/common/http'
import { Observable, of } from 'rxjs'
import { ReportTypes } from '@venio/shared/models/constants'
import { LegalHoldReportFacade } from '@venio/data-access/common'
import { ReportsFacade } from '@venio/data-access/reports'

// Define mock interfaces
interface MockReportsFacade {
  storeSelectedLegalHold: jest.Mock
  storeSelectedCustodians: jest.Mock
  storeSelectedStatuses: jest.Mock
  selectedSelectedReportType$: Observable<ReportTypes>
}

interface MockLegalHoldReportFacade {
  fetchLegalHoldList: jest.Mock
  fetchCustodianList: jest.Mock
  fetchStatus: jest.Mock
  selectLegalHoldList$: Observable<any[]>
  selectCustodianList$: Observable<any>
  selectCustodianListSuccessResponse$: Observable<any>
  selectStatusList$: Observable<any[]>
}

// Create mock objects
const mockReportsFacade: MockReportsFacade = {
  storeSelectedLegalHold: jest.fn(),
  storeSelectedCustodians: jest.fn(),
  storeSelectedStatuses: jest.fn(),
  selectedSelectedReportType$: of(ReportTypes.MATTER_DETAIL_REPORT),
}

const mockLegalHoldReportFacade: MockLegalHoldReportFacade = {
  fetchLegalHoldList: jest.fn(),
  fetchCustodianList: jest.fn(),
  fetchStatus: jest.fn(),
  selectLegalHoldList$: of([]),
  selectCustodianList$: of(false),
  selectCustodianListSuccessResponse$: of({ data: [] }),
  selectStatusList$: of([]),
}

describe('ReportLegalHoldComponent', () => {
  let component: ReportLegalHoldComponent
  let fixture: ComponentFixture<ReportLegalHoldComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReportLegalHoldComponent, NoopAnimationsModule],
      providers: [
        provideMockStore({}),
        provideHttpClient(),
        { provide: ReportsFacade, useValue: mockReportsFacade },
        { provide: LegalHoldReportFacade, useValue: mockLegalHoldReportFacade },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ReportLegalHoldComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  afterEach(() => {
    if (fixture) {
      fixture.destroy()
    }
  })

  it('should create the component', () => {
    expect(component).toBeTruthy()
  })

  it('should fetch legal hold list on ngOnInit', () => {
    component.ngOnInit()
    expect(mockLegalHoldReportFacade.fetchLegalHoldList).toHaveBeenCalled()
  })

  it('should fetch status list on ngOnInit', () => {
    component.ngOnInit()
    expect(mockLegalHoldReportFacade.fetchStatus).toHaveBeenCalled()
  })

  it('should handle legal hold selection change', () => {
    const userIds = [1, 2, 3]
    component.legalHoldSelectionChange(userIds)
    expect(component.selectedLegalHold()).toEqual(userIds)
    expect(component.isAllLegalHoldChecked()).toBe(false)
  })

  it('should handle custodian selection change', () => {
    const userIds = [1, 2, 3]
    component.custodianSelectionChange(userIds)
    expect(component.selectedCustodians()).toEqual(userIds)
    expect(component.isAllCustodiansChecked()).toBe(false)
  })

  it('should handle status selection change', () => {
    const statusIds = [1, 2, 3]
    component.statusSelectionChange(statusIds)
    expect(component.selectedStatuses()).toEqual(statusIds)
    expect(component.isAllStatusesChecked()).toBe(false)
  })

  it('should remove tag and update signals', () => {
    const event = {
      dataItem: { holdId: null, statusID: null, custodianId: null },
    } as any
    component.removeTag(event)
    expect(component.isAllLegalHoldChecked()).toBe(false)
    expect(component.isAllStatusesChecked()).toBe(false)
    expect(component.isAllCustodiansChecked()).toBe(false)
  })

  it('should store selected legal hold', fakeAsync(() => {
    const userIds = [1, 2, 3]
    component.selectedLegalHold.set(userIds)
    tick()
    expect(mockReportsFacade.storeSelectedLegalHold).toHaveBeenCalledWith(
      userIds
    )
  }))

  it('should store selected custodians', fakeAsync(() => {
    const userIds = [1, 2, 3]
    component.selectedCustodians.set(userIds)
    tick()
    expect(mockReportsFacade.storeSelectedCustodians).toHaveBeenCalledWith(
      userIds
    )
  }))

  it('should store selected statuses', fakeAsync(() => {
    const statusIds = [1, 2, 3]
    component.selectedStatuses.set(statusIds)
    tick()
    expect(mockReportsFacade.storeSelectedStatuses).toHaveBeenCalledWith(
      statusIds
    )
  }))

  it('should clean up subscriptions when the component is destroyed', () => {
    const nextSpy = jest.spyOn(component['toDestroy$'], 'next')
    const completeSpy = jest.spyOn(component['toDestroy$'], 'complete')
    component.ngOnDestroy()
    expect(nextSpy).toHaveBeenCalled()
    expect(completeSpy).toHaveBeenCalled()
  })
})
