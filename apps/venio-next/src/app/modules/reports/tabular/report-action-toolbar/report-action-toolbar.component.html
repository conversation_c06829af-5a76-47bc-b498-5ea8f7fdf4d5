@for(button of actionButtons; track button.actionType){ @if(button.actionType
=== 'PRINT'){
<kendo-dropdownbutton
  [disabled]="(totalCount$ | async) === 0"
  kendoTooltip
  [title]="(totalCount$ | async) !== 0 ? 'Print Reporrt' : 'No data to print'"
  [data]="printOptions"
  themeColor="dark"
  fillMode="outline"
  [arrowIcon]="true"
  buttonClass="hover:t-bg-[#263238] hover:t-text-white"
  [svgIcon]="button.svgIcon"
  (itemClick)="exportReportTo($event)">
  <ng-template kendoDropDownButtonItemTemplate let-dataItem>
    <kendo-svgicon [icon]="dataItem.svgIcon"></kendo-svgicon>
    <span>{{ dataItem.text }}</span>
  </ng-template>
</kendo-dropdownbutton>
} @else{
<kendo-dropdownbutton
  [disabled]="(totalCount$ | async) === 0"
  kendoTooltip
  [title]="(totalCount$ | async) !== 0 ? 'Export Report' : 'No data to export'"
  [data]="actions"
  themeColor="dark"
  fillMode="outline"
  [arrowIcon]="true"
  buttonClass="hover:t-bg-[#263238] hover:t-text-white"
  [svgIcon]="button.svgIcon"
  (itemClick)="exportReportTo($event)">
  <ng-template kendoDropDownButtonItemTemplate let-dataItem>
    <kendo-svgicon
      [ngClass]="
        largeDataWarningClass(dataItem, selectTotalReportCount$ | async)
      "
      [icon]="dataItem.svgIcon"></kendo-svgicon>
    <span
      kendoTooltip
      [title]="
        largeDataWarningClass(dataItem, selectTotalReportCount$ | async)
          ? 'PDF export might be slow for large datasets'
          : ''
      "
      [ngClass]="
        largeDataWarningClass(dataItem, selectTotalReportCount$ | async)
      "
      >{{ dataItem.text }}</span
    >
  </ng-template>
</kendo-dropdownbutton>
} }

<venio-pagination
  [pageSize]="(selectPaging$ | async)?.pageSize"
  [showPageSize]="true"
  [currentPage]="(selectPaging$ | async)?.pageNumber"
  [showPageJumper]="false"
  [totalRecords]="selectTotalReportCount$ | async"
  (pageSizeChanged)="pagingChange($event)"
  (pageChanged)="pagingChange($event)" />
