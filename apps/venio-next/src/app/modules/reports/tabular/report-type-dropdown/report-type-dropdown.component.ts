import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  computed,
  inject,
  On<PERSON><PERSON>roy,
  OnInit,
  signal,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { AdornmentsModule } from '@progress/kendo-angular-common'
import {
  DropDownsModule,
  MultiSelectModule,
} from '@progress/kendo-angular-dropdowns'
import { SVGIconModule } from '@progress/kendo-angular-icons'
import { caretAltDownIcon } from '@progress/kendo-svg-icons'
import { FormsModule } from '@angular/forms'
import { ReportTypes } from '@venio/shared/models/constants'
import { ReportsFacade } from '@venio/data-access/reports'
import {
  combineLatest,
  distinctUntilChanged,
  filter,
  map,
  Subject,
  take,
  takeUntil,
} from 'rxjs'
import {
  AppIdentitiesTypes,
  IframeMessengerFacade,
  IframeMessengerService,
  MessageContent,
  MessageType,
} from '@venio/data-access/iframe-messenger'
import {
  ProgressBarAnimation,
  ProgressBarModule,
} from '@progress/kendo-angular-progressbar'
import {
  ConfigService,
  ControlSettingService,
} from '@venio/data-access/control-settings'
import { StartupsFacade, UserRights } from '@venio/data-access/review'
import { toSignal } from '@angular/core/rxjs-interop'

interface ReportItem {
  type: ReportTypes
  label: string
  description: string
  requiredRight: UserRights
}

@Component({
  selector: 'venio-report-type-dropdown',
  standalone: true,
  imports: [
    CommonModule,
    AdornmentsModule,
    MultiSelectModule,
    SVGIconModule,
    DropDownsModule,
    FormsModule,
    ProgressBarModule,
  ],
  templateUrl: './report-type-dropdown.component.html',
  styleUrl: './report-type-dropdown.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReportTypeDropdownComponent implements OnInit, OnDestroy {
  private readonly toDestroy$ = new Subject<void>()

  private readonly reportFacade = inject(ReportsFacade)

  private readonly permissionFacade = inject(StartupsFacade)

  private readonly iframeMessengerFacade = inject(IframeMessengerFacade)

  private readonly iframeMessengerService = inject(IframeMessengerService)

  private readonly controlSettingService = inject(ControlSettingService)

  public readonly isExporting = signal(false)

  public isMicroApp = this.iframeMessengerFacade.selectLoadedAsMicroApp$.pipe(
    map((loadedAsMicroApp) => Boolean(loadedAsMicroApp))
  )

  public readonly progressBarAnimation: ProgressBarAnimation = {
    duration: 2000,
  }

  public progressBarValue = signal<number>(0)

  public computedProgressBarValue = computed(() =>
    this.progressBarValue()?.toFixed(2)
  )

  private intervalId: any

  public listItems: ReportItem[] = []

  public selectedReportType: ReportItem | null = null

  public readonly chevronDownIcon = caretAltDownIcon

  private readonly rights = toSignal(this.permissionFacade.getUserRights$)

  // Define the two separate report sets
  private readonly nonLegalHoldReports: ReportItem[] = [
    {
      type: ReportTypes.LOG_IN_OUT_REPORTS,
      label: 'Login & Logout',
      description: 'Login & Logout Report',
      requiredRight: UserRights.ALLOW_TO_VIEW_SYSTEM_LOGIN_LOGOUT_REPORT,
    },
    {
      type: ReportTypes.LOCKED_USERS_REPORTS,
      label: 'Locked Users',
      description: 'Locked Users Report',
      requiredRight: UserRights.ALLOW_TO_VIEW_LOCKED_USERS_REPORT,
    },
    {
      type: ReportTypes.DELETED_EXPORTS,
      label: 'Deleted Exports',
      description: 'Deleted Exports Report',
      requiredRight: UserRights.ALLOW_TO_GENERATE_PRODUCTION_DELETE_REPORT,
    },
    // TODO: future implementation
    // {
    //   type: ReportTypes.UNLOCKED_USERS_REPORTS,
    //   label: 'Unlocked Users',
    //   description: 'Unlocked Users Report',
    // },
    {
      type: ReportTypes.CREATION_AND_DEACTIVATION_REPORTS,
      label: 'Creation & Deactivation',
      description: 'Creation & Deactivation Report',
      requiredRight:
        UserRights.ALLOW_TO_VIEW_USER_CREATION_AND_DEACTIVATION_REPORTS,
    },
    {
      type: ReportTypes.DATA_EXPORT_AND_DOWNLOAD_REPORTS,
      label: 'Data Export & Download',
      description: 'Data Export & Download Report',
      requiredRight: UserRights.ALLOW_TO_VIEW_EXPORT_AND_DOWNLOAD_REPORT,
    },
    {
      type: ReportTypes.ROLE_CHANGE_REPORTS,
      label: 'Role Change Report',
      description: 'Role Change Report',
      requiredRight: UserRights.ALLOW_TO_VIEW_ROLE_CHANGE_REPORT,
    },
    {
      type: ReportTypes.PROJECT_ACCESS_REPORT,
      label: 'Project Access Report',
      description: 'Project Access Report',
      requiredRight: UserRights.ALLOW_TO_VIEW_PROJECT_ACCESS_REPORT,
    },
    {
      type: ReportTypes.ACTIVITY_REPORT,
      label: 'Activity Report',
      description: 'Activity Report',
      requiredRight: UserRights.ALLOW_TO_VIEW_USER_ACTIVITY_REPORT,
    },
  ]

  private readonly legalHoldReports: ReportItem[] = [
    {
      type: ReportTypes.MATTER_DETAIL_REPORT,
      label: 'Matter Detail Report',
      description: 'Matter Detail Report',
      requiredRight:
        UserRights.ALLOW_TO_VIEW_LEGAL_HOLD_CUSTODIAN_DETAIL_REPORT,
    },
    {
      type: ReportTypes.ACTIVATED_DEACTIVATED_CUSTODIAN_REPORT,
      label: 'Activated & Deactivated Custodian Report',
      description: 'Activated & Deactivated Custodian Report',
      requiredRight:
        UserRights.ALLOW_TO_VIEW_LEGAL_HOLD_ACTIVE_TERMINATED_CUSTODIAN_DETAIL_REPORT,
    },
  ]

  private readonly configService = inject(ConfigService)

  public legalHoldLicense = false

  constructor(private cdr: ChangeDetectorRef) {}

  public ngOnInit(): void {
    this.#fetchRights()

    this.#initializeReportList()
    this.#selectIsExporting()
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
    if (this.intervalId) {
      clearInterval(this.intervalId)
    }
  }

  public reportTypeChange(event: { type: ReportTypes }): void {
    this.reportFacade.storeReportType(event.type)
    this.reportFacade.storeDateRange(undefined)
    this.#notifySelectedReportType(event.type)
  }

  #selectSelectedReportType(): void {
    this.reportFacade.selectedSelectedReportType$
      .pipe(
        distinctUntilChanged(),
        // Uncomment and adjust filter as needed
        // filter(
        //   (type) => Boolean(type) && this.selectedReportType?.type !== type
        // ),
        takeUntil(this.toDestroy$)
      )
      .subscribe((type) => {
        // If any report type is selected, then set it as the selected report type
        const foundReport = this.listItems.find((item) => item.type === type)
        if (foundReport) {
          this.selectedReportType = foundReport
          this.reportTypeChange({ type: this.selectedReportType?.type })
          this.cdr.detectChanges()
        }
      })
  }

  // Method to filter list items and set default report type
  #initializeReportList(): void {
    // Determine legalHoldLicense from configService
    combineLatest([
      this.configService.fetchLicenseStatus$('FEATURE', 'LEGAL HOLD'),
      this.configService.fetchLicenseStatus$('FEATURE', 'FULL FEATURE'),
    ])
      .pipe(
        map(([legalHoldEnabled, fullFeatureEnabled]) => {
          let mergedList = []

          if (legalHoldEnabled) {
            mergedList = [...this.legalHoldReports]
          }
          if (fullFeatureEnabled) {
            mergedList = [...mergedList, ...this.nonLegalHoldReports]
          }

          // Remove duplicates based on 'type' property
          const uniqueList = mergedList.filter(
            (item, index, self) =>
              index === self.findIndex((t) => t.type === item.type)
          )

          // Apply additional filtering if ENABLE_REVIEW_2 is enabled
          if (this.controlSettingService.getControlSetting?.ENABLE_REVIEW_2) {
            return uniqueList.filter(
              (item) => item.type !== ReportTypes.LOG_IN_OUT_REPORTS
            )
          }

          return uniqueList
        }),
        map((uniqueList) => {
          const rights = this.rights()

          // Handle permissions for available actions
          const userGlobalRights = rights?.Invalid_Global_Right_List
          const filteredList = uniqueList.filter(
            (item) => !userGlobalRights[item?.requiredRight]
          )

          return filteredList
        }),
        takeUntil(this.toDestroy$)
      )
      .subscribe((finalList) => {
        this.listItems = finalList
        this.#selectIframeSelectedReportType()
        this.#selectSelectedReportType()
      })
  }

  #selectIframeSelectedReportType(): void {
    this.iframeMessengerFacade
      .selectIframeMessengerContent$(MessageType.REPORTS)
      .pipe(
        distinctUntilChanged(),
        filter(
          (mc) => (mc as MessageContent)?.content?.['reportData']?.['type']
        ),
        take(1),
        takeUntil(this.toDestroy$)
      )
      .subscribe((mc) => {
        const type = (mc as MessageContent).content['reportData']['type']
        this.selectedReportType = this.listItems.find(
          (item) => item.type === type
        )
        this.reportFacade.storeReportType(type)
      })
  }

  public startProgress(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId)
    }

    this.intervalId = setInterval(() => {
      // Increment the value more slowly as it approaches 90%
      if (this.progressBarValue() < 90) {
        this.progressBarValue.set(this.progressBarValue() + Math.random() * 2)
      }

      // Check if the value is 90 or more and stop the interval
      if (this.progressBarValue() >= 90) {
        clearInterval(this.intervalId)
        if (!this.isExporting()) {
          this.#simulateTaskCompletion()
        }
      }
    }, 1000)
  }

  #simulateTaskCompletion(): void {
    setTimeout(() => {
      this.progressBarValue.set(100)
    }, 500)
  }

  #selectIsExporting(): void {
    this.reportFacade.selectIsReportExporting$
      .pipe(
        filter((isExporting) => typeof isExporting === 'boolean'),
        takeUntil(this.toDestroy$)
      )
      .subscribe((isExporting) => {
        // TODO: comprehensive workflow is necessary
        // this.isExporting.set(isExporting)
        if (isExporting) {
          this.startProgress()
        }
      })
  }

  #fetchRights(): void {
    this.permissionFacade.fetchUserRights(0)
  }

  #notifySelectedReportType(selectedReportType: ReportTypes): void {
    this.iframeMessengerService.sendMessage({
      type: 'MICRO_APP_DATA_CHANGE',
      eventTriggeredFor: 'PARENT_WINDOW',
      eventTriggeredBy: AppIdentitiesTypes.VENIO_NEXT,
      iframeIdentity: AppIdentitiesTypes.VOD,
      payload: {
        type: MessageType.REPORTS,
        content: {
          selectedReportType,
          isNotifiedFromChild: true,
        },
      },
    })
  }
}
