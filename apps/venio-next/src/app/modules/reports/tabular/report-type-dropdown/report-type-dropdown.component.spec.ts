import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ReportTypeDropdownComponent } from './report-type-dropdown.component'
import { provideMockStore } from '@ngrx/store/testing'
import { provideAnimations } from '@angular/platform-browser/animations'
import {
  AppIdentitiesTypes,
  IframeMessengerModule,
  WINDOW,
  windowFactory,
} from '@venio/data-access/iframe-messenger'
import { PLATFORM_ID } from '@angular/core'
import { EffectsModule } from '@ngrx/effects'
import { StoreModule } from '@ngrx/store'
import { provideHttpClient } from '@angular/common/http'
import { StartupsFacade } from '@venio/data-access/review'
import { of } from 'rxjs'

describe('ReportTypeDropdownComponent', () => {
  let component: ReportTypeDropdownComponent
  let fixture: ComponentFixture<ReportTypeDropdownComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        ReportTypeDropdownComponent,
        StoreModule.forRoot({}),
        EffectsModule.forRoot([]),
        IframeMessengerModule.forRoot({
          origin: '*',
          iframeIdentity: AppIdentitiesTypes.VENIO_NEXT,
        }),
        EffectsModule.forRoot([]),
      ],
      providers: [
        provideHttpClient(),
        provideMockStore({}),
        provideAnimations(),
        {
          provide: WINDOW,
          useFactory: windowFactory,
          deps: [PLATFORM_ID],
        },
        {
          provide: StartupsFacade,
          useValue: {
            getUserRights$: of({ Invalid_Global_Right_List: {} }),
            fetchUserRights: jest.fn(),
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(ReportTypeDropdownComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
