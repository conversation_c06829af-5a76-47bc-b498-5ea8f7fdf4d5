import { ChangeDetectionStrategy, Component, inject } from '@angular/core'
import { CommonModule } from '@angular/common'
import { caretAltDownIcon } from '@progress/kendo-svg-icons'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { ReportsFacade } from '@venio/data-access/reports'

@Component({
  selector: 'venio-report-generate-button',
  standalone: true,
  imports: [CommonModule, ButtonsModule],
  templateUrl: './report-generate-button.component.html',
  styleUrl: './report-generate-button.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReportGenerateButtonComponent {
  public readonly chevDown = caretAltDownIcon

  private readonly reportFacade = inject(ReportsFacade)

  public isReportLoading$ = this.reportFacade.selectIsReportLoading$

  public generateReport(): void {
    this.reportFacade.fetchReports()
  }
}
