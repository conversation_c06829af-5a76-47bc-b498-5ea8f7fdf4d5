import { ChangeDetectionStrategy, Component, inject } from '@angular/core'
import { CommonModule } from '@angular/common'
import { UserFacade } from '@venio/data-access/common'
import { ReportsFacade } from '@venio/data-access/reports'
import { map, switchMap } from 'rxjs'

@Component({
  selector: 'venio-report-generate-info',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './report-generate-info.component.html',
  styleUrl: './report-generate-info.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReportGenerateInfoComponent {
  private readonly userFacade = inject(UserFacade)

  private readonly reportFacade = inject(ReportsFacade)

  public userName$ = this.userFacade.selectCurrentUsername$

  public totalCount$ = this.reportFacade.selectTotalReportCount$

  public isReportLoading$ = this.reportFacade.selectIsReportLoading$.pipe(
    switchMap(() => this.reportFacade.selectTotalReportCount$),
    map((count) => {
      return typeof count === 'number'
    })
  )

  public currentDateTime: string

  constructor() {
    this.reportFacade.selectFetchDBServerDateTimeInfoSuccess$.subscribe(
      (response) => {
        if (response && response.data) {
          this.currentDateTime = response?.data?.dbServerDateTime
        }
      }
    )
  }
}
