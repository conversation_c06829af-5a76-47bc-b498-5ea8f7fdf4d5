import { TestBed, ComponentFixture } from '@angular/core/testing'
import { ActivateDeactivateCustodianReportComponent } from './activate-deactivate-custodian-report.component'
import { ReportsFacade } from '@venio/data-access/reports'
import { provideMockStore } from '@ngrx/store/testing'
import { provideHttpClient } from '@angular/common/http'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { of } from 'rxjs'
import { ReportTypes } from '@venio/shared/models/constants'
import { LegalHoldReportFacade } from '@venio/data-access/common'
import { CommonModule } from '@angular/common'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { IconsModule } from '@progress/kendo-angular-icons'
import { CheckBoxModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'

// Mock ReportsFacade
const mockReportsFacade = {
  storeSelectedActiveTerminatedLegalHold: jest.fn(),
  storeSelectedActiveTerminatedStatuses: jest.fn(),
  selectedSelectedReportType$: of(
    ReportTypes.ACTIVATED_DEACTIVATED_CUSTODIAN_REPORT
  ),
}

// Mock LegalHoldReportFacade
const mockLegalHoldReportFacade = {
  fetchActiveTerminatedLegalHoldList: jest.fn(),
  selectActiveTerminatedLegalHoldListSuccessResponse$: of([]),
  fetchActiveTerminatedStatus: jest.fn(),
  selectActiveTerminatedStatusSuccessResponse$: of([]),
}

describe('ActivateDeactivateCustodianReportComponent', () => {
  let component: ActivateDeactivateCustodianReportComponent
  let fixture: ComponentFixture<ActivateDeactivateCustodianReportComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        DropDownsModule,
        IconsModule,
        CheckBoxModule,
        LabelModule,
        NoopAnimationsModule,
        ActivateDeactivateCustodianReportComponent,
      ],
      providers: [
        provideMockStore({}),
        provideHttpClient(),
        { provide: ReportsFacade, useValue: mockReportsFacade },
        { provide: LegalHoldReportFacade, useValue: mockLegalHoldReportFacade },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(
      ActivateDeactivateCustodianReportComponent
    )
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  afterEach(() => {
    fixture.destroy()
  })

  it('should create the component', () => {
    expect(component).toBeTruthy()
  })

  it('should call fetch methods on ngOnInit', () => {
    component.ngOnInit()
    expect(
      mockLegalHoldReportFacade.fetchActiveTerminatedLegalHoldList
    ).toHaveBeenCalled()
    expect(
      mockLegalHoldReportFacade.fetchActiveTerminatedStatus
    ).toHaveBeenCalled()
  })
})
