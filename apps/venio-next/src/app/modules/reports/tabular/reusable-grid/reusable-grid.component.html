<kendo-grid
  class="t-relative t-min-h-[10rem] t-min-w-[100%]"
  [ngStyle]="{ width: container.clientWidth + 'px' }"
  [kendoGridBinding]="data"
  [loading]="config?.isLoading | async"
  kendoGridSelectBy="id"
  [sortable]="true"
  [selectable]="false"
  [resizable]="true"
  scrollable="virtual"
  (excelExport)="config?.excelGridOptions?.onExport($event)"
  (pdfExport)="config?.pdfGridOptions?.onExport($event)"
  [height]="container?.clientHeight - 8 || 400"
  [rowHeight]="36"
  [pageSize]="data?.length || 20"
  [skip]="0">
  <ng-template kendoGridNoRecordsTemplate>
    <div class="t-flex t-justify-center t-items-center t-h-min t-w-full">
      <span class="t-text-[#000000BC] t-text-[16px]">{{
        config?.noRecordsMessage
      }}</span>
    </div>
  </ng-template>

  @for (column of config?.columnConfigs; track column.field + column.title){
  <kendo-grid-column
    [resizable]="true"
    [field]="column.field"
    [title]="column.title"
    [width]="column.width"
    [headerClass]="column.headerClass"
    [class]="column.class"
    [minResizableWidth]="50">
    <ng-container *ngIf="column.template">
      <ng-template kendoGridCellTemplate let-dataItem>
        <ng-container
          *ngTemplateOutlet="
            column.template;
            context: { dataItem: dataItem }
          "></ng-container>
      </ng-template>
    </ng-container>
  </kendo-grid-column>
  }
  <kendo-grid-pdf
    [creator]="config?.pdfGridOptions?.creator"
    [margin]="config?.pdfGridOptions?.margin"
    [fileName]="config?.pdfGridOptions?.fileName"
    [scale]="config?.pdfGridOptions?.scale"
    [delay]="config?.pdfGridOptions?.delay"
    [allPages]="config?.pdfGridOptions?.allPages"
    [autoPrint]="config?.pdfGridOptions?.autoPrint"
    [paperSize]="config?.pdfGridOptions?.paperSize"
    [landscape]="config?.pdfGridOptions?.landscape"
    [repeatHeaders]="config?.pdfGridOptions?.repeatHeaders">
    @for (column of config?.columnConfigs; track column.field + column.title){
    <kendo-grid-column
      [field]="column.field"
      [title]="column.title"
      [width]="column.width"
      [headerClass]="column.headerClass"
      [class]="column.class"
      [minResizableWidth]="column.minResizableWidth">
    </kendo-grid-column>
    } @if(config?.pdfGridOptions?.pdfTemplate){
    <ng-template
      kendoGridPDFTemplate
      let-pageNum="pageNum"
      let-totalPages="totalPages">
      <ng-container
        *ngTemplateOutlet="
          config?.pdfGridOptions.pdfTemplate;
          context: { pageNum: pageNum, totalPages: totalPages }
        " />
    </ng-template>

    }
  </kendo-grid-pdf>

  <kendo-grid-excel [fileName]="config?.excelGridOptions?.fileName">
    @for (column of config?.excelGridOptions?.excelColumns; track column.field +
    column.title){
    <kendo-excelexport-column [field]="column.field" [title]="column.title">
    </kendo-excelexport-column>
    }
  </kendo-grid-excel>
</kendo-grid>
