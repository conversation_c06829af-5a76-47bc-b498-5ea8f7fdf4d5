import {
  ChangeDetectionStrategy,
  Component,
  inject,
  OnDestroy,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { RouterOutlet } from '@angular/router'
import { ReportsFacade } from '@venio/data-access/reports'
import { IframeMessengerFacade } from '@venio/data-access/iframe-messenger'
import { map } from 'rxjs'
import { DataAccessCommonModule } from '@venio/data-access/common'

@Component({
  selector: 'venio-report-index',
  standalone: true,
  imports: [CommonModule, RouterOutlet, DataAccessCommonModule],
  templateUrl: './report-index.component.html',
  styleUrl: './report-index.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReportIndexComponent implements OnDestroy {
  private readonly reportFacade = inject(ReportsFacade)

  private readonly iframeMessengerFacade = inject(IframeMessengerFacade)

  public isMicroApp = this.iframeMessengerFacade.selectLoadedAsMicroApp$.pipe(
    map((loadedAsMicroApp) => Boolean(loadedAsMicroApp))
  )

  #resetReportState(): void {
    this.reportFacade.resetReportState([
      'selectedReportType',
      'selectedUsers',
      'fetchExportDataSuccess',
      'fetchExportDataError',
      'exportEvent',
      'fetchReportSuccessResponse',
      'fetchReportErrorResponse',
      'dateRange',
      'isReportExporting',
      'isReportLoading',
    ])
  }

  public ngOnDestroy(): void {
    this.#resetReportState()
  }
}
