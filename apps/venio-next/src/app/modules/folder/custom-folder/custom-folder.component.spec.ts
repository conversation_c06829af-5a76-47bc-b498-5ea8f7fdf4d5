import { ComponentFixture, TestBed } from '@angular/core/testing'
import { CustomFolderComponent } from './custom-folder.component'
import { FolderFacade, StartupsFacade } from '@venio/data-access/review'
import { provideMockStore } from '@ngrx/store/testing'
import { ActivatedRoute } from '@angular/router'
import { AnimationBuilder } from '@angular/animations'
import { NotificationService } from '@progress/kendo-angular-notification'

describe('CustomFolderComponent', () => {
  let component: CustomFolderComponent
  let fixture: ComponentFixture<CustomFolderComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CustomFolderComponent],
      providers: [
        FolderFacade,
        StartupsFacade,
        AnimationBuilder,
        NotificationService,
        provideMockStore({}),
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParams: {
                projectId: 2,
              },
            },
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(CustomFolderComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
