import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  Optional,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { LabelModule } from '@progress/kendo-angular-label'
import { GridModule } from '@progress/kendo-angular-grid'
import { InputsModule } from '@progress/kendo-angular-inputs'
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { IconsModule } from '@progress/kendo-angular-icons'
import {
  LayoutModule,
  ExpansionPanelModule,
} from '@progress/kendo-angular-layout'
import {
  FolderFacade,
  FolderModel,
  ProjectGroups,
  StartupsFacade,
} from '@venio/data-access/review'
import { Subject, filter, map, takeUntil } from 'rxjs'
import { ActivatedRoute } from '@angular/router'
import { validateBeforeSubmit } from '@venio/feature/generic-validator'
import { DialogRef } from '@progress/kendo-angular-dialog'
import { NotificationService } from '@progress/kendo-angular-notification'

@Component({
  selector: 'venio-custom-folder',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    LabelModule,
    GridModule,
    InputsModule,
    FormsModule,
    LayoutModule,
    DropDownListModule,
    IconsModule,
    ExpansionPanelModule,
  ],
  templateUrl: './custom-folder.component.html',
  styleUrls: ['./custom-folder.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CustomFolderComponent implements OnInit, OnDestroy {
  public customFolderFormGroup: FormGroup

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public customFolderErrorMessage: string

  public customFolders: FolderModel[]

  public selectedCustomFolder: FolderModel

  public securityGroups: ProjectGroups[] = []

  public securityPermissionData = [
    { text: 'Read/Write', value: 'READ_WRITE' },
    { text: 'Read Only', value: 'READ_ONLY' },
    { text: 'None', value: 'NONE' },
  ]

  private unsubscribed$: Subject<void> = new Subject<void>()

  constructor(
    private formBuilder: FormBuilder,
    private activatedRoute: ActivatedRoute,
    private folderFacade: FolderFacade,
    private startupsFacade: StartupsFacade,
    private cdr: ChangeDetectorRef,
    private notificationService: NotificationService,
    @Optional()
    public dialogRef: DialogRef
  ) {}

  public ngOnInit(): void {
    this.#initCustomFolderFormGroup()
    this.fetchCustomFoldersAndGroupDetails()
    this.#selectStaticFolders()
    this.#selectSecurityGroupData()
    this.#selectSuccessMessage()
    this.#selectStaticFolderErrorMessage()
  }

  public ngOnDestroy(): void {
    this.unsubscribed$.next()
    this.unsubscribed$.complete()
  }

  private fetchCustomFoldersAndGroupDetails(): void {
    this.folderFacade.fetchStaticFolders(this.projectId)
    this.startupsFacade.fetchDefaultGroups(this.projectId)
    this.folderFacade.fetchFolderSecurityGroupAction()
  }

  #initCustomFolderFormGroup(): void {
    this.customFolderFormGroup = this.formBuilder.group({
      folderName: ['', Validators.required],
      folderDescription: '',
    })
  }

  #selectStaticFolders(): void {
    this.folderFacade.getStaticFolders$
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((staticFolders) => {
        this.cdr.markForCheck()
        if (staticFolders.length > 0) {
          this.customFolders = [
            ...staticFolders,
            ...[
              {
                folderId: -1,
                folderName: '-----Root Folder------',
                parentFolderId: -2,
                accessType: 'PUBLIC',
                folderLineage: '',
                folderIdlineage: '\\-1',
                fileCount: 0,
                isSystemFolder: false,
                isRelativePath: null,
                customFieldInfoId: null,
                folderProjectGroupAssociations: null,
                groupAccess: null,
                description: '',
                folderOrder: 0,
              },
            ],
          ]

          this.selectedCustomFolder =
            this.customFolders[this.customFolders.length - 1]
        }
      })
  }

  #selectSecurityGroupData(): void {
    this.folderFacade.getSecurityGroupData$
      .pipe(
        map((securityGroupData) =>
          securityGroupData.map((item) => ({ ...item }))
        ),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((securityGroupData) => {
        this.cdr.markForCheck()
        this.securityGroups = securityGroupData
      })
  }

  #selectSuccessMessage(): void {
    this.folderFacade.getSuccessMessage$
      .pipe(
        filter((successMessage) => !!successMessage),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((successMessage) => {
        this.notificationService.show({
          content: successMessage,
          type: { style: 'success', icon: true },
          animation: { type: 'fade', duration: 300 },
          closable: false,
          hideAfter: 3000,
        })
        this.folderFacade.fetchStaticFolders(this.projectId)
        this.dialogRef.close()
      })
  }

  #selectStaticFolderErrorMessage(): void {
    this.folderFacade.getErrorMessage$
      .pipe(
        filter((errorMessage) => !!errorMessage),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((errorMessage) => {
        this.cdr.markForCheck()
        this.customFolderErrorMessage = errorMessage
      })
  }

  private getCustomFolderData(): FolderModel {
    const folder: FolderModel = {
      folderId: 0,
      folderName: this.customFolderFormGroup.get('folderName').value,
      parentFolderId: this.selectedCustomFolder
        ? this.selectedCustomFolder.folderId
        : -1,
      accessType: 'PUBLIC',
      folderLineage: '',
      folderIdlineage: '',
      fileCount: 0,
      isSystemFolder: false,
      isRelativePath: null,
      customFieldInfoId: null,
      folderProjectGroupAssociations: null,
      groupAccess: this.securityGroups.map((g) => ({
        groupId: g.groupId,
        permission: g.permission,
      })),
      userGroupPermissionFolder: null,
      parentFileCount: 0,
      description: this.customFolderFormGroup.get('folderDescription').value,
      folderOrder: null,
      belowFolderId: 0,
    }
    return folder
  }

  private setErrorMessage(message: string): void {
    this.cdr.markForCheck()
    this.customFolderErrorMessage = message
  }

  public createCustomFolder(): void {
    const errorMessage = validateBeforeSubmit(this.customFolderFormGroup)
    if (errorMessage) {
      this.setErrorMessage(errorMessage)
      return
    }
    this.folderFacade.clearMessage()
    const folderModel = this.getCustomFolderData()

    this.folderFacade.createStaticFolder(this.projectId, folderModel)
  }
}
