<div class="content">
  <div class="t-flex t-mt-4 t-w-full t-flex-col t-p-2 v-custom-grey-bg">
    <div
      *ngIf="autoFolderErrorMessage"
      class="t-flex t-justify-between t-items-center t-bg-[#f8d7da] t-p-3">
      <span>{{ autoFolderErrorMessage }}</span>
      <button
        type="button"
        (click)="autoFolderErrorMessage = null"
        class="t-border-0 t-cursor-pointer t-text-error">
        close
      </button>
    </div>
    <div
      class="t-flex t-flex-col t-mt-3 t-gap-3"
      [formGroup]="autoFolderFormGroup">
      <div class="t-flex t-flex-1 t-flex-row-reverse t-justify-end t-gap-2">
        <kendo-label
          class="k-radio-label"
          [for]="relativePath"
          text="Relative File Path"></kendo-label>

        <input
          type="radio"
          [value]="autoFolderOption.Relative_Path"
          name="autoFolderOption"
          #relativePath
          kendoRadioButton
          formControlName="autoFolderOption" />
      </div>

      <div class="t-flex t-flex-1 t-flex-row-reverse t-justify-end t-gap-2">
        <div showHints="always" class="t-flex t-gap-1">
          <input
            type="radio"
            [value]="autoFolderOption.CustomField"
            name="autoFolderOption"
            #customField
            kendoRadioButton
            formControlName="autoFolderOption" />

          <kendo-label
            class="k-radio-label t-w-[25rem]"
            [for]="customField"
            text="Custom Field">
            <span *ngIf="showCustomFieldOption" class="t-text-error">*</span>
          </kendo-label>

          <kendo-dropdownlist
            #customField
            *ngIf="showCustomFieldOption"
            [data]="customFields"
            textField="displayFieldName"
            valueField="venioFieldId"
            formControlName="customFieldId"
            placeholder="Select Field"
            [valuePrimitive]="true">
          </kendo-dropdownlist>

          <kendo-label
            *ngIf="showCustomFieldOption"
            for="Separator"
            class="t-text-sm t-w-1/2 t-flex t-items-center">
            Separator: <span class="t-text-error">*</span>
          </kendo-label>
          <kendo-multicolumncombobox
            #Separator
            *ngIf="showCustomFieldOption"
            [data]="separators"
            textField="displayText"
            valueField="text"
            [filterable]="true"
            (filterChange)="handleFilterChange($event)"
            (selectionChange)="onSelectionChanged($event)"
            formControlName="customFieldSeparator"
            [valuePrimitive]="true">
            <kendo-combobox-column
              field="displayText"
              title="DisplayText"
              [width]="200">
            </kendo-combobox-column>
            <kendo-combobox-column field="value" title="Value" [width]="80">
            </kendo-combobox-column>
            <kendo-combobox-column field="text" title="Text" [width]="80">
            </kendo-combobox-column>
          </kendo-multicolumncombobox>
        </div>
      </div>
      <div
        class="t-flex t-flex-row-reverse t-gap-2 t-justify-end t-mt-3 t-items-center">
        <label class="k-checkbox-label" for="custodian"
          >Create Folder for Each Custodian</label
        >
        <input
          type="checkbox"
          id="custodian"
          [size]="'small'"
          kendoCheckBox
          formControlName="isCreateFolderForEachCustodian" />
      </div>

      <div
        class="t-flex t-flex-row-reverse t-gap-2 t-justify-end t-items-center">
        <label class="k-checkbox-label" for="media"
          >Create Folder for Each Media</label
        >
        <input
          type="checkbox"
          id="media"
          [size]="'small'"
          kendoCheckBox
          formControlName="isCreateFolderForEachMedia" />
      </div>
    </div>
  </div>
  <div class="t-flex t-flex-col t-w-full t-gap-2">
    <div class="t-flex t-mt-4 t-pb-3 t-text-primary t-font-semibold">
      Security
    </div>

    <div class="t-flex t-flex-col t-w-full">
      <kendo-grid
        class="!t-max-h-60 t-w-full"
        [data]="securityGroups"
        [resizable]="true">
        <kendo-grid-column
          field="groupName"
          title="Role"
          headerClass="t-text-primary">
        </kendo-grid-column>
        <kendo-grid-column
          field="permission"
          title="Permission"
          [width]="200"
          headerClass="t-text-primary">
          <ng-template kendoGridCellTemplate let-dataItem>
            <kendo-dropdownlist
              [data]="securityPermissionData"
              [valuePrimitive]="true"
              textField="text"
              valueField="value"
              [(ngModel)]="dataItem.permission">
            </kendo-dropdownlist>
          </ng-template>
        </kendo-grid-column>
      </kendo-grid>
    </div>
  </div>
</div>
