import { CommonModule } from '@angular/common'
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
  Optional,
} from '@angular/core'
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms'
import { ActivatedRoute } from '@angular/router'
import { DialogRef } from '@progress/kendo-angular-dialog'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { GridModule } from '@progress/kendo-angular-grid'
import { IconsModule } from '@progress/kendo-angular-icons'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import {
  LayoutModule,
  ExpansionPanelModule,
} from '@progress/kendo-angular-layout'
import { NotificationService } from '@progress/kendo-angular-notification'
import {
  DynamicFolderFacade,
  DynamicFolderModel,
  SearchDupOption,
  SearchSettings,
  StartupsFacade,
  UserRights,
  UserRolePermission,
} from '@venio/data-access/review'
import { validateBeforeSubmit } from '@venio/feature/generic-validator'
import { UserGroupRightCheckDirective } from '@venio/feature/shared/directives'
import { Subject, combineLatest, filter, map, takeUntil } from 'rxjs'

@Component({
  selector: 'venio-dynamic-folder',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    LabelModule,
    GridModule,
    InputsModule,
    FormsModule,
    LayoutModule,
    DropDownListModule,
    IconsModule,
    ExpansionPanelModule,
    UserGroupRightCheckDirective,
  ],
  templateUrl: './dynamic-folder.component.html',
  styleUrls: ['./dynamic-folder.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DynamicFolderComponent implements OnInit, OnDestroy {
  public dynamicFolderFormGroup: FormGroup

  private unsubscribed$: Subject<void> = new Subject<void>()

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  public dynamicFolderErrorMessage: string

  public containers: DynamicFolderModel[]

  public selectedContainer: DynamicFolderModel

  public userRolePermissions: UserRolePermission[]

  public rolePermissionData = [
    { text: 'Search and Edit', value: 'Search and Edit' },
    { text: 'Search', value: 'Search' },
    { text: 'None', value: 'None' },
  ]

  private searchSettings: SearchSettings

  public UserRights = UserRights

  constructor(
    private formBuilder: FormBuilder,
    private activatedRoute: ActivatedRoute,
    private dynamicFolderFacade: DynamicFolderFacade,
    private startupsFacade: StartupsFacade,
    private cdr: ChangeDetectorRef,
    private notificationService: NotificationService,
    @Optional()
    public dialogRef: DialogRef
  ) {}

  public ngOnInit(): void {
    this.#initDynamicFolderFormGroup()
    this.fetchDynamicFoldersAndUserRole()
    this.#selectDynamicFoldersForContainer()
    this.#selectUserRolePermissionForSecurity()
    this.#selectCreateDynamicFolderSuccessMessage()
    this.#selectDynamicFolderErrorMessage()
    this.#selectSearchParamForSearchSetting()
  }

  public ngOnDestroy(): void {
    this.unsubscribed$.next()
    this.unsubscribed$.complete()
  }

  private fetchDynamicFoldersAndUserRole(): void {
    this.dynamicFolderFacade.fetchDynamicFolders(this.projectId)
    this.dynamicFolderFacade.fetchDefaultUserRolePermission()
  }

  #initDynamicFolderFormGroup(): void {
    this.dynamicFolderFormGroup = this.formBuilder.group({
      dynamicFolderName: ['', Validators.required],
      dynamicFolderNote: '',
    })
  }

  #selectDynamicFoldersForContainer(): void {
    combineLatest([
      this.startupsFacade.hasGroupRight$(
        UserRights.ALLOW_TO_ADD_GLOBAL_DYNAMIC_FOLDER
      ),
      this.startupsFacade.hasGroupRight$(
        UserRights.ALLOW_TO_ADD_LOCAL_DYNAMIC_FOLDER
      ),
      this.dynamicFolderFacade.getDynamicFolders$,
    ])
      .pipe(
        filter(([allowGlobal, allowLocal, folders]) => !!folders),
        map(([allowGlobal, allowLocal, folders]) => {
          return folders.filter((folder) =>
            this.projectId < 1 || (allowGlobal && allowLocal)
              ? folder.isPlaceholder
              : allowGlobal
              ? folder.isGlobal && folder.isPlaceholder
              : allowLocal
              ? !folder.isGlobal && folder.isPlaceholder
              : []
          )
        }),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((containers) => {
        this.cdr.markForCheck()
        this.containers = containers
        if (this.containers) this.selectedContainer = this.containers[0]
      })
  }

  #selectUserRolePermissionForSecurity(): void {
    this.dynamicFolderFacade.getDefaultUserRolePermission$
      .pipe(
        filter((defaultUserRolePermission) => !!defaultUserRolePermission),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((defaultUserRolePermission) => {
        this.cdr.markForCheck()

        this.userRolePermissions =
          defaultUserRolePermission
            ?.map((role) => ({ ...role }))
            .filter((a) => !a?.globalRoleName?.match(/legal admin/gi)) || []
      })
  }

  #selectCreateDynamicFolderSuccessMessage(): void {
    //show success message
    this.dynamicFolderFacade.getCreateDynamicFolderSuccessMessage$
      .pipe(
        filter((successMessage) => !!successMessage),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((successMessage) => {
        this.notificationService.show({
          content: successMessage,
          type: { style: 'success', icon: true },
          animation: { type: 'fade', duration: 300 },
          closable: false,
          hideAfter: 3000,
        })
        this.dynamicFolderFacade.fetchDynamicFolders(this.projectId)
        this.dialogRef.close()
      })
  }

  #selectDynamicFolderErrorMessage(): void {
    this.dynamicFolderFacade.getCreateDynamicFolderErrorMessage$
      .pipe(
        filter((errorMessage) => !!errorMessage),
        takeUntil(this.unsubscribed$)
      )
      .subscribe((errorMessage) => {
        this.cdr.markForCheck()
        this.dynamicFolderErrorMessage = errorMessage
      })
  }

  #selectSearchParamForSearchSetting(): void {
    this.startupsFacade.getSearchParams$
      .pipe(takeUntil(this.unsubscribed$))
      .subscribe((searchParams: any) => {
        this.#setSearchSettings(searchParams)
      })
  }

  #setSearchSettings(searchParams: any): void {
    this.searchSettings = {
      includeParentChild: searchParams.includePC,
      isCombinedSearch: false,
      searchDuplicateOption:
        searchParams.searchDuplicateOption === SearchDupOption.DEFAULT
          ? SearchDupOption.HIDE_ALL_DUPS_DYNAMIC
          : searchParams.searchDuplicateOption,
      isLoadFileSearch: searchParams.isLoadFile,
      searchExpression: {
        expression: searchParams.searchExpression,
        isSqlMode: false,
      },
      searchLoadFile: {
        loadFileType: 'SEARCH TERM',
        text: '',
      },
    }
  }

  private getDynamicFolderData(): DynamicFolderModel {
    //Edit is not consider
    const dynamicFolder: DynamicFolderModel = {
      folderId: 0,
      folderName: this.dynamicFolderFormGroup.get('dynamicFolderName').value,
      folderNote: this.dynamicFolderFormGroup.get('dynamicFolderNote').value,
      isPlaceholder: false,
      parentFolderId: this.selectedContainer.folderId,
      groupPermissions: this.userRolePermissions?.map((rolePermission) => ({
        dynamicFolderId: 0,
        globalRoleId: rolePermission.globalRoleId,
        permission: rolePermission.permission,
      })),
      isGlobal: this.selectedContainer.isGlobal,
      searchSettings: this.searchSettings,
    }
    return dynamicFolder
  }

  private setErrorMessage(message: string): void {
    this.cdr.markForCheck()
    this.dynamicFolderErrorMessage = message
  }

  public saveDynamicFolder(): void {
    const errorMessage = validateBeforeSubmit(this.dynamicFolderFormGroup)
    if (errorMessage) {
      this.setErrorMessage(errorMessage)
      return
    }
    this.dynamicFolderFacade.clearDynamicFolderMessage()
    const dynamicFolderModel = this.getDynamicFolderData()
    //create
    this.dynamicFolderFacade.createDynamicFolder(
      this.projectId,
      dynamicFolderModel.isGlobal,
      dynamicFolderModel
    )
  }
}
