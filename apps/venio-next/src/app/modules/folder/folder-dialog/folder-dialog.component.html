<kendo-dialog-titlebar>
  <div class="t-font-semibold t-text-lg">Folder</div>
</kendo-dialog-titlebar>

<div
  class="t-flex t-flex-col t-border t-border-t-[#ebebeb] t-border-x-0 t-border-b-0">
  <div class="t-flex t-gap-5">
    <div class="t-flex t-flex-1 t-flex-col t-w-full">
      <kendo-tabstrip
        #mainTabStrip
        class="v-tabstrip-custom t-w-full t-h-full"
        (tabSelect)="onTabSelect($event)">
        <kendo-tabstrip-tab
          title="Custom Folder"
          *venioHasUserGroupRights="UserRights.ALLOW_TO_CREATE_NEW_FOLDER">
          <ng-template kendoTabContent>
            <venio-custom-folder></venio-custom-folder>
          </ng-template>
        </kendo-tabstrip-tab>
        <kendo-tabstrip-tab
          title="Save a Dynamic Folder"
          *venioHasUserGroupRights="
            [
              UserRights.ALLOW_TO_ADD_GLOBAL_DYNAMIC_FOLDER,
              UserRights.ALLOW_TO_ADD_LOCAL_DYNAMIC_FOLDER
            ];
            anyOfTheGivenPermission: true
          ">
          <ng-template kendoTabContent>
            <venio-dynamic-folder></venio-dynamic-folder>
          </ng-template>
        </kendo-tabstrip-tab>

        <kendo-tabstrip-tab
          title="Auto Folder"
          *venioHasUserGroupRights="UserRights.ALLOW_TO_AUTO_FOLDER">
          <ng-template kendoTabContent>
            <venio-auto-folder></venio-auto-folder>
          </ng-template>
        </kendo-tabstrip-tab>
      </kendo-tabstrip>
    </div>
  </div>
</div>

<kendo-dialog-actions>
  <div class="t-flex t-gap-4 t-justify-end">
    <button
      kendoButton
      (click)="save()"
      [disabled]="
        (isCreateFolderLoading | async) && folderTabType === FolderTabType.AUTO
      "
      class="v-custom-secondary-button"
      fillMode="outline"
      themeColor="secondary">
      <kendo-loader
        *ngIf="
          (isCreateFolderLoading | async) &&
          folderTabType === FolderTabType.AUTO
        "
        size="small">
      </kendo-loader>
      {{ saveTitle }}
    </button>
    <button kendoButton (click)="close()" themeColor="dark" fillMode="outline">
      CANCEL
    </button>
  </div>
</kendo-dialog-actions>
