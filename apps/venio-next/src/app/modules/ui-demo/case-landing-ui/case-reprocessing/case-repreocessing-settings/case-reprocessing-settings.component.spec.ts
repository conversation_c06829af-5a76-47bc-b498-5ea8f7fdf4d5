import { ComponentFixture, TestBed } from '@angular/core/testing'
import { CaseReprocessingSettingsComponent } from './case-reprocessing-settings.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('CaseReprocessingSettingsComponent', () => {
  let component: CaseReprocessingSettingsComponent
  let fixture: ComponentFixture<CaseReprocessingSettingsComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CaseReprocessingSettingsComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(CaseReprocessingSettingsComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
