<div class="t-flex t-gap-2 t-mt-5">
  <div class="t-flex t-gap-2">
    <kendo-textbox
      class="!t-border-[#ccc] !t-w-[25rem]"
      placeholder="Search Custodian Or Media Scope"
      [clearButton]="true">
      <ng-template kendoTextBoxSuffixTemplate>
        <button
          kendoButton
          fillMode="clear"
          class="t-text-[#1EBADC]"
          imageUrl="assets/svg/icon-updated-search.svg"></button>
      </ng-template>
    </kendo-textbox>
  </div>

  <button
    kendoButton
    themeColor="secondary"
    class="t-p-[0.4rem] t-w-9 t-h-9 hover:t-text-[#FFFFFF] hover:t-bg-[#9BD2A7]"
    fillMode="solid">
    <img src="assets/svg/icon-refresh-twoway.svg" alt="icon" />
  </button>
</div>

<div class="t-mt-4 t-flex t-gap-2">
  <div class="t-w-2/4">
    <span class="t-inline-flex t-items-center t-gap-2">
      <input type="checkbox" #allFiles kendoCheckBox [checked]="true" />
      <kendo-label
        class="k-radio-label"
        [for]="allFiles"
        text="Select All"></kendo-label>
    </span>
    <kendo-treeview
      [nodes]="treeViewData"
      textField="text"
      kendoTreeViewExpandable
      kendoTreeViewCheckable
      [(checkedKeys)]="checkedKeys"
      [hasChildren]="hasChildren"
      class="v-hide-scrollbar"
      [children]="getChildren"
      [expandedKeys]="expandedKeys"
      [extraSpacing]="70"
      venioDynamicHeight>
      <ng-template kendoTreeViewNodeTemplate let-dataItem let-id="id">
        <span>
          {{ dataItem.text }}
        </span>
      </ng-template>
    </kendo-treeview>
  </div>

  <div class="t-w-2/4">
    <kendo-treeview
      [nodes]="treeViewData2"
      textField="text"
      kendoTreeViewExpandable
      kendoTreeViewCheckable
      [(checkedKeys)]="checkedKeys"
      [hasChildren]="hasChildren"
      [children]="getChildren"
      class="v-hide-scrollbar"
      [expandedKeys]="expandedKeys"
      [extraSpacing]="70"
      venioDynamicHeight>
      <ng-template kendoTreeViewNodeTemplate let-dataItem let-id="id">
        <span>
          {{ dataItem.text }}
        </span>
      </ng-template>
    </kendo-treeview>
  </div>
</div>

<!-- footer-->
<div class="t-flex t-w-full t-gap-4 t-justify-end t-pt-4">
  <button
    kendoButton
    class="v-custom-secondary-button"
    themeColor="secondary"
    fillMode="outline"
    data-qa="save">
    SAVE
  </button>
  <button data-qa="cancel" kendoButton themeColor="dark" fillMode="outline">
    CANCEL
  </button>
</div>
