import { ComponentFixture, TestBed } from '@angular/core/testing'
import { CaseReprocessingComponent } from './case-reprocessing.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { ActivatedRoute } from '@angular/router'
import { of } from 'rxjs'

describe('CaseReprocessingComponent', () => {
  let component: CaseReprocessingComponent
  let fixture: ComponentFixture<CaseReprocessingComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CaseReprocessingComponent, NoopAnimationsModule],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        {
          provide: ActivatedRoute,
          useValue: {
            queryParams: of({ ind: 'settings' }), // Mock query parameters
          },
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(CaseReprocessingComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
