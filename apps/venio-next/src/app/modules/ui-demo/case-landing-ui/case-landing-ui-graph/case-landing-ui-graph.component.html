<div class="t-flex t-items-center t-flex-col t-justify-start">
  <kendo-chart class="t-border-0 !t-h-[210px]">
    <kendo-chart-tooltip>
      <ng-template
        kendoChartSeriesTooltipTemplate
        let-value="value"
        let-category="category"
        let-series="series">
        {{ category }}: {{ value }}%
      </ng-template>
    </kendo-chart-tooltip>
    <kendo-chart-series>
      <kendo-chart-series-item
        type="pie"
        [data]="data"
        categoryField="kind"
        field="share"
        [colorField]="'color'"
        [autoFit]="true">
      </kendo-chart-series-item>
    </kendo-chart-series>
    <kendo-chart-legend [visible]="false"></kendo-chart-legend>
  </kendo-chart>

  <div class="t-flex t-gap-[15px] t-justify-between t-w-full t-flex-wrap">
    <ng-container *ngFor="let item of data">
      <div class="t-flex t-flex-col t-gap-[3px]">
        <div class="t-text-xs t-font-medium t-text-[#393939]">
          {{ item.kind }}
        </div>
        <div class="t-flex t-items-center t-gap-[9px]">
          <span
            class="t-inline-block t-rounded t-w-[22px] t-h-[19px]"
            [ngStyle]="{ 'background-color': item.color }"></span
          ><span class="t-text-[#004F11]">{{ item.share }}</span>
        </div>
      </div>
    </ng-container>
  </div>
</div>
