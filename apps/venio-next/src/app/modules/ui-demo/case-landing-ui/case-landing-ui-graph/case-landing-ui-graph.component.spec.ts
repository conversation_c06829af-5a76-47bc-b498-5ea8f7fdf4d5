import { ComponentFixture, TestBed } from '@angular/core/testing'
import { CaseLandingUiGraphComponent } from './case-landing-ui-graph.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('CaseLandingUiGraphComponent', () => {
  let component: CaseLandingUiGraphComponent
  let fixture: ComponentFixture<CaseLandingUiGraphComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CaseLandingUiGraphComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(CaseLandingUiGraphComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
