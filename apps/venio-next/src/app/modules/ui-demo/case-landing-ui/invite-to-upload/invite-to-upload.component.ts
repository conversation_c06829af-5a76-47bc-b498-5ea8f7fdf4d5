import { ChangeDetectionStrategy, Component } from '@angular/core'
import { CommonModule } from '@angular/common'
import { IconsModule } from '@progress/kendo-angular-icons'
import { DialogRef, DialogsModule } from '@progress/kendo-angular-dialog'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { ButtonsModule } from '@progress/kendo-angular-buttons'

import { userOutlineIcon } from '@progress/kendo-svg-icons'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { GridModule } from '@progress/kendo-angular-grid'
import { EditorModule } from '@progress/kendo-angular-editor'
import { LayoutModule } from '@progress/kendo-angular-layout'
import { LabelModule } from '@progress/kendo-angular-label'

@Component({
  selector: 'venio-invite-to-upload',
  standalone: true,
  imports: [
    CommonModule,
    DialogsModule,
    IconsModule,
    ButtonsModule,
    GridModule,
    DropDownsModule,
    InputsModule,
    EditorModule,
    LayoutModule,
    LabelModule,
  ],
  templateUrl: './invite-to-upload.component.html',
  styleUrl: './invite-to-upload.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InviteToUploadComponent {
  public dialogTitle = 'Invite Users to Upload'

  public icons = { UserOutlineIcon: userOutlineIcon }

  public internalUsers = [
    { id: 1, name: 'John Doe', role: 'Admin' },
    { id: 2, name: 'Melissa', role: 'User' },
    { id: 1, name: 'Rebecca son', role: 'Admin' },
    { id: 2, name: 'Aston Martin', role: 'User' },
  ]

  public defaultItemExternal: { text: string; value: number } = {
    text: 'Add/Select External User',
    value: null,
  }

  public externalUsers: Array<{ text: string; value: number }> = [
    { text: 'John Doe', value: 1 },
    { text: 'Melissa', value: 2 },
    { text: 'Rebecca son', value: 3 },
    { text: 'Aston Martin', value: 4 },
  ]

  public validityOptions = ['1 Day', '7 Days', '30 Days']

  public defaultValidity = '7 Days'

  constructor(private dialogRef: DialogRef) {}

  public close(status: string): void {
    this.dialogRef.close()
  }
}
