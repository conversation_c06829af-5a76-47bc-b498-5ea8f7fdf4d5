import { ComponentFixture, TestBed } from '@angular/core/testing'
import { InviteToUploadComponent } from './invite-to-upload.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { DialogRef } from '@progress/kendo-angular-dialog'

class MockDialogRef {
  public close(): void {}
}

describe('InviteToUploadComponent', () => {
  let component: InviteToUploadComponent
  let fixture: ComponentFixture<InviteToUploadComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [InviteToUploadComponent, NoopAnimationsModule],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: DialogRef, useClass: MockDialogRef },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(InviteToUploadComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
