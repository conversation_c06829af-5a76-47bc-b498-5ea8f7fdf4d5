<kendo-dialog-titlebar (close)="close('cancel')">
  <div class="t-flex t-justify-between t-w-full t-items-center">
    <div class="t-block">
      <span
        class="t-w-10 t-h-10 t-p-3 t-mr-3 t-bg-[#F2F2F2] t-rounded-full t-inline-flex t-justify-center">
        <img src="assets/svg/icon-person-outline.svg" alt="upload icon" />
      </span>

      {{ dialogTitle }}
    </div>
  </div>
</kendo-dialog-titlebar>

<div class="t-flex t-flex-col t-gap-2">
  <div class="t-flex t-gap-3 t-mt-1">
    <!-- Internal Users Section -->
    <div class="t-w-1/2">
      <kendo-label
        class="t-flex t-items-center t-h-[41px] t-font-semibold t-text-[#263238] t-mb-0.5">
        Share To Internal Users
      </kendo-label>

      <div class="t-flex t-items-center t-mb-4">
        <kendo-textbox
          class="!t-border-[#ccc] !t-w-full"
          placeholder="Search For Internal Users"
          [clearButton]="true">
          <ng-template kendoTextBoxSuffixTemplate>
            <button
              kendoButton
              fillMode="clear"
              class="t-text-[#1EBADC]"
              imageUrl="assets/svg/icon-updated-search.svg"></button>
          </ng-template>
        </kendo-textbox>
      </div>

      <kendo-grid
        [kendoGridBinding]="internalUsers"
        venioDynamicHeight
        [resizable]="true"
        kendoGridSelectBy="id"
        [height]="140">
        <kendo-grid-column
          headerClass="t-text-primary"
          field="id"
          title="#"
          [width]="50"></kendo-grid-column>
        <kendo-grid-checkbox-column
          [showSelectAll]="true"
          [width]="40"></kendo-grid-checkbox-column>
        <kendo-grid-column
          headerClass="t-text-primary"
          field="name"
          [width]="150"
          title="User Name"></kendo-grid-column>
        <kendo-grid-column
          headerClass="t-text-primary"
          field="role"
          [width]="150"
          title="Role"></kendo-grid-column>
      </kendo-grid>
    </div>

    <div class="t-flex">
      <div
        class="t-h-full t-bg-green-200 t-w-10 t-h-full custom-svg-line t-bg-no-repeat t-bg-center"></div>
    </div>

    <!-- External Users Section -->
    <div class="t-w-1/2">
      <div class="t-flex t-justify-between t-gap-3 t-mb-2.5">
        <kendo-label class="t-flex t-items-center">
          <input type="checkbox" kendoCheckBox class="t-mr-2" />
          Share To External Users
        </kendo-label>

        <kendo-dropdownlist
          [data]="externalUsers"
          [defaultItem]="defaultItemExternal"
          textField="text"
          valueField="value"
          class="t-ml-4 t-w-52">
        </kendo-dropdownlist>
      </div>

      <div class="t-flex t-items-center t-mb-4">
        <kendo-textbox
          class="!t-border-[#ccc] !t-w-full"
          placeholder="Search For External Users"
          [clearButton]="true">
          <ng-template kendoTextBoxSuffixTemplate>
            <button
              kendoButton
              fillMode="clear"
              class="t-text-[#1EBADC]"
              imageUrl="assets/svg/icon-updated-search.svg"></button>
          </ng-template>
        </kendo-textbox>
      </div>

      <kendo-grid
        [kendoGridBinding]="internalUsers"
        venioDynamicHeight
        [resizable]="true"
        kendoGridSelectBy="id"
        [height]="140">
        <kendo-grid-column
          headerClass="t-text-primary"
          field="id"
          title="#"
          [width]="50"></kendo-grid-column>
        <kendo-grid-checkbox-column
          [showSelectAll]="true"
          [width]="40"></kendo-grid-checkbox-column>
        <kendo-grid-column
          headerClass="t-text-primary"
          field="name"
          [width]="300"
          title="User Name"></kendo-grid-column>
      </kendo-grid>
    </div>
  </div>

  <div class="t-flex t-flex-col t-w-full t-justify-start">
    <!-- Instruction Section -->
    <div class="t-flex t-flex-col t-gap-2">
      <kendo-label class="t-block t-text-[#707070]">Instruction</kendo-label>
      <kendo-editor
        class="t-border t-border-[#BEBEBE] t-h-[170px] t-rounded-md t-p-2">
        <kendo-toolbar>
          <kendo-toolbar-dropdownlist
            kendoEditorFontFamily></kendo-toolbar-dropdownlist>
          <kendo-toolbar-dropdownlist
            kendoEditorFontSize></kendo-toolbar-dropdownlist>

          <kendo-toolbar-buttongroup>
            <kendo-toolbar-button kendoEditorBoldButton></kendo-toolbar-button>
            <kendo-toolbar-button
              kendoEditorItalicButton></kendo-toolbar-button>
            <kendo-toolbar-button
              kendoEditorUnderlineButton></kendo-toolbar-button>
            <kendo-toolbar-button
              kendoEditorStrikethroughButton></kendo-toolbar-button>
          </kendo-toolbar-buttongroup>
          <kendo-toolbar-buttongroup>
            <kendo-toolbar-button
              kendoEditorAlignLeftButton></kendo-toolbar-button>
            <kendo-toolbar-button
              kendoEditorAlignCenterButton></kendo-toolbar-button>
            <kendo-toolbar-button
              kendoEditorAlignRightButton></kendo-toolbar-button>
            <kendo-toolbar-button
              kendoEditorAlignJustifyButton></kendo-toolbar-button>
          </kendo-toolbar-buttongroup>

          <kendo-toolbar-buttongroup>
            <kendo-toolbar-button
              kendoEditorInsertUnorderedListButton></kendo-toolbar-button>
            <kendo-toolbar-button
              kendoEditorInsertOrderedListButton></kendo-toolbar-button>
            <kendo-toolbar-button
              kendoEditorIndentButton></kendo-toolbar-button>
            <kendo-toolbar-button
              kendoEditorOutdentButton></kendo-toolbar-button>
          </kendo-toolbar-buttongroup>
        </kendo-toolbar>
      </kendo-editor>
    </div>

    <div class="t-mt-1 t-flex t-flex-col t-items-start t-gap-2">
      <kendo-label class="t-mr-4 t-w-36 t-text-[#707070]"
        >Valid up-to</kendo-label
      >
      <kendo-dropdownlist
        [data]="validityOptions"
        [value]="defaultValidity"
        class="t-w-36"></kendo-dropdownlist>
    </div>
  </div>
</div>

<kendo-dialog-actions>
  <div class="t-flex t-gap-4 t-justify-end">
    <button
      kendoButton
      (click)="close('no')"
      class="v-custom-secondary-button"
      themeColor="secondary"
      fillMode="outline"
      data-qa="save-button">
      SEND INVITE
    </button>
    <button
      kendoButton
      (click)="close('yes')"
      themeColor="dark"
      fillMode="outline"
      data-qa="cancel-button">
      CANCEL
    </button>
  </div>
</kendo-dialog-actions>
