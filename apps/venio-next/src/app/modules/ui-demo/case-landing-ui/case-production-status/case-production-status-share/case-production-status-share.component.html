<div class="t-flex t-flex-col t-gap-2">
  <div class="t-flex t-mt-2 t-mb-1 t-w-full t-items-center">
    <button
      kendoButton
      fillMode="outline"
      class="t-w-[74px] t-p-0 t-text-[16px] hover:t-text-[#000000] t-flex t-items-center">
      <kendo-svg-icon
        class="t-text-[var(--v-custom-sky-blue)] hover:t-text-[var(--v-custom-sky-blue)]"
        [icon]="icons.chevronLeftIcon"
        size="large"></kendo-svg-icon>
      Back</button
    ><span class="t-ml-2 t-font-semibold t-text-[16px]">Share Production</span>
  </div>
  <div class="t-flex t-gap-3 t-mt-1">
    <!-- Internal Users Section -->
    <div class="t-w-1/2">
      <kendo-label
        class="t-flex t-items-center t-h-[41px] t-font-semibold t-text-[#263238] t-mb-0.5">
        Share To Internal Users
      </kendo-label>

      <div class="t-flex t-items-center t-mb-4">
        <kendo-textbox
          class="!t-border-[#ccc] !t-w-full"
          placeholder="Search For Internal Users"
          [clearButton]="true">
          <ng-template kendoTextBoxSuffixTemplate>
            <button
              kendoButton
              fillMode="clear"
              class="t-text-[#1EBADC]"
              imageUrl="assets/svg/icon-updated-search.svg"></button>
          </ng-template>
        </kendo-textbox>
      </div>

      <kendo-grid
        [kendoGridBinding]="internalUsers"
        venioDynamicHeight
        [resizable]="true"
        kendoGridSelectBy="id"
        [height]="140">
        <kendo-grid-column
          headerClass="t-text-primary"
          field="id"
          title="#"
          [width]="50"></kendo-grid-column>
        <kendo-grid-checkbox-column
          [showSelectAll]="true"
          [width]="40"></kendo-grid-checkbox-column>
        <kendo-grid-column
          headerClass="t-text-primary"
          field="name"
          [width]="150"
          title="User Name"></kendo-grid-column>
        <kendo-grid-column
          headerClass="t-text-primary"
          field="role"
          [width]="150"
          title="Role"></kendo-grid-column>
      </kendo-grid>
    </div>

    <div class="t-flex">
      <div
        class="t-h-full t-bg-green-200 t-w-10 t-h-full custom-svg-line t-bg-no-repeat t-bg-center"></div>
    </div>

    <!-- External Users Section -->
    <div class="t-w-1/2">
      <div class="t-flex t-justify-between t-gap-3 t-mb-2.5">
        <kendo-label class="t-flex t-items-center">
          <input type="checkbox" kendoCheckBox class="t-mr-2" />
          Share To External Users
        </kendo-label>

        <kendo-dropdownlist
          [data]="externalUsers"
          [defaultItem]="defaultItemExternal"
          textField="text"
          valueField="value"
          class="t-ml-4 t-w-52">
        </kendo-dropdownlist>
      </div>

      <div class="t-flex t-items-center t-mb-4">
        <kendo-textbox
          class="!t-border-[#ccc] !t-w-full"
          placeholder="Search For External Users"
          [clearButton]="true">
          <ng-template kendoTextBoxSuffixTemplate>
            <button
              kendoButton
              fillMode="clear"
              class="t-text-[#1EBADC]"
              imageUrl="assets/svg/icon-updated-search.svg"></button>
          </ng-template>
        </kendo-textbox>
      </div>

      <kendo-grid
        [kendoGridBinding]="internalUsers"
        venioDynamicHeight
        [resizable]="true"
        kendoGridSelectBy="id"
        [height]="140">
        <kendo-grid-column
          headerClass="t-text-primary"
          field="id"
          title="#"
          [width]="50"></kendo-grid-column>
        <kendo-grid-checkbox-column
          [showSelectAll]="true"
          [width]="40"></kendo-grid-checkbox-column>
        <kendo-grid-column
          headerClass="t-text-primary"
          field="name"
          [width]="300"
          title="User Name"></kendo-grid-column>
      </kendo-grid>
    </div>
  </div>

  <div class="t-flex t-flex-col t-w-full t-justify-start">
    <!-- Instruction Section -->
    <div class="t-flex t-flex-col t-gap-2">
      <kendo-label class="t-block t-text-[#707070]">Instruction</kendo-label>
      <kendo-editor
        class="t-border t-border-[#BEBEBE] t-h-[170px] t-rounded-md t-p-2">
        <kendo-toolbar>
          <kendo-toolbar-dropdownlist
            kendoEditorFontFamily></kendo-toolbar-dropdownlist>
          <kendo-toolbar-dropdownlist
            kendoEditorFontSize></kendo-toolbar-dropdownlist>

          <kendo-toolbar-buttongroup>
            <kendo-toolbar-button kendoEditorBoldButton></kendo-toolbar-button>
            <kendo-toolbar-button
              kendoEditorItalicButton></kendo-toolbar-button>
            <kendo-toolbar-button
              kendoEditorUnderlineButton></kendo-toolbar-button>
            <kendo-toolbar-button
              kendoEditorStrikethroughButton></kendo-toolbar-button>
          </kendo-toolbar-buttongroup>
          <kendo-toolbar-buttongroup>
            <kendo-toolbar-button
              kendoEditorAlignLeftButton></kendo-toolbar-button>
            <kendo-toolbar-button
              kendoEditorAlignCenterButton></kendo-toolbar-button>
            <kendo-toolbar-button
              kendoEditorAlignRightButton></kendo-toolbar-button>
            <kendo-toolbar-button
              kendoEditorAlignJustifyButton></kendo-toolbar-button>
          </kendo-toolbar-buttongroup>

          <kendo-toolbar-buttongroup>
            <kendo-toolbar-button
              kendoEditorInsertUnorderedListButton></kendo-toolbar-button>
            <kendo-toolbar-button
              kendoEditorInsertOrderedListButton></kendo-toolbar-button>
            <kendo-toolbar-button
              kendoEditorIndentButton></kendo-toolbar-button>
            <kendo-toolbar-button
              kendoEditorOutdentButton></kendo-toolbar-button>
          </kendo-toolbar-buttongroup>
        </kendo-toolbar>
      </kendo-editor>
    </div>

    <div class="t-mt-1 t-flex t-flex-col t-items-start t-gap-2">
      <kendo-label class="t-mr-4 t-w-36 t-text-[#707070]"
        >Valid up-to</kendo-label
      >
      <kendo-dropdownlist
        [data]="validityOptions"
        [value]="defaultValidity"
        class="t-w-36"></kendo-dropdownlist>
    </div>
  </div>
</div>
