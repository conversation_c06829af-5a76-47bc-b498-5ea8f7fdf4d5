<div class="t-flex t-w-full t-flex-col t-gap-3">
  <div>
    <kendo-textbox
      class="k-textbox"
      (input)="onInput($event)"
      placeholder="Input Value">
    </kendo-textbox>
  </div>

  <div
    class="t-flex t-flex-col t-gap-2 v-hide-scrollbar t-max-h-[175px] t-border-t-[2px] t-pt-3 t-mt-1 t-border-[#cccccc] t-border-dashed">
    <div
      class="t-flex t-items-center t-gap-1"
      *ngFor="let item of currentData; let i = index"
      (click)="onSelectionChange(item)"
      [ngClass]="{ 'k-selected': isItemSelected(item) }">
      <input
        type="checkbox"
        kendoCheckBox
        id="chk-{{ item }}-{{ i }}"
        [checked]="isItemSelected(item)" />
      <label
        class="k-multiselect-checkbox k-checkbox-label"
        for="chk-{{ item }}-{{ i }}">
        {{ item }}
      </label>
    </div>
  </div>
</div>
