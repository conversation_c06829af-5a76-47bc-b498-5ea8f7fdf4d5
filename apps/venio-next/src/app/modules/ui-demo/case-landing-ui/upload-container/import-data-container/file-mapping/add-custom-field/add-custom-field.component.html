<div class="t-flex t-flex-col t-gap-3 t-my-3 t-flex-1">
  <kendo-grid
    class="v-custom-grid-upload"
    [kendoGridBinding]="repoGridData"
    venioDynamicHeight
    [resizable]="true"
    kendoGridSelectBy="id">
    <kendo-grid-checkbox-column
      [showSelectAll]="true"
      [width]="40"></kendo-grid-checkbox-column>
    <kendo-grid-column
      headerClass="t-text-primary"
      field="name"
      title="Select All"></kendo-grid-column>

    <kendo-grid-column headerClass="t-text-primary" field="name" title="">
      <ng-template kendoGridHeaderTemplate let-column>
        <kendo-textbox
          class="!t-border-[#ccc]"
          placeholder="Search"
          [clearButton]="true">
          <ng-template kendoTextBoxSuffixTemplate>
            <button
              kendoButton
              fillMode="clear"
              class="t-text-[#1EBADC]"
              imageUrl="assets/svg/icon-updated-search.svg"></button>
          </ng-template>
        </kendo-textbox>
      </ng-template>
      <ng-template kendoGridCellTemplate let-dataItem let-column="column">
        <kendo-textbox class="t-h-[24px]" [clearButton]="true"></kendo-textbox>
      </ng-template>
    </kendo-grid-column>
  </kendo-grid>
</div>
<div class="t-flex t-gap-4 t-justify-end">
  <button
    kendoButton
    class="v-custom-secondary-button !t-rounded-none t-self-end"
    themeColor="secondary"
    fillMode="outline"
    data-qa="upload-button">
    SAVE
  </button>

  <button
    kendoButton
    themeColor="dark"
    fillMode="outline"
    data-qa="cancel-button">
    CANCEL
  </button>
</div>
