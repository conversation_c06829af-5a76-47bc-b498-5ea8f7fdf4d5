import { ChangeDetectionStrategy, Component, output } from '@angular/core'
import { CommonModule } from '@angular/common'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'

enum MediaSourceType {
  SOURCE_FILE = 'SOURCE_FILE',
  SOURCE_FOLDER = 'SOURCE_FOLDER',
  AWSS3_FILE = 'AWSS3_FILE',
  AWSS3_FOLDER = 'AWSS3_FOLDER',
  IMAGE_DD = 'IMAGE_DD',
  IMAGE_ENCASE = 'IMAGE_ENCASE',
  IMAGE_ENCASE_LOGICAL = 'IMAGE_ENCASE_LOGICAL',
  IMAGE_001 = 'IMAGE_001',
  IMAGE_ISO = 'IMAGE_ISO',
  IMAGE_AD1 = 'IMAGE_AD1',
  SOCIAL_MEDIA_FACEBOOK = 'SOCIAL_MEDIA_FACEBOOK',
  SOCIAL_MEDIA_SLACK = 'SOCIAL_MEDIA_SLACK',
  SOCIAL_MEDIA_CELLEBRITE = 'SOCIAL_MEDIA_CELLEBRITE',
  SOCIAL_MEDIA_BLOOMBERG = 'SOCIAL_MEDIA_BLOOMBERG',
  SOCIAL_MEDIA_TWITTER = 'SOCIAL_MEDIA_TWITTER',
  SOCIAL_MEDIA_MSTEAM = 'SOCIAL_MEDIA_MSTEAM',
  RSMF = 'RSMF',
}

enum SourceType {
  /**
   * no source type
   */
  None,

  /**
   * Unprocessed RAW data
   */
  Unstructured,

  /**
   * Third party production data
   */
  Structured,

  /**
   * Transcript
   */
  Transcript,
}

@Component({
  selector: 'venio-upload-source-selection',
  standalone: true,
  imports: [CommonModule, SvgLoaderDirective],
  templateUrl: './upload-source-selection.component.html',
  styleUrl: './upload-source-selection.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UploadSourceSelectionComponent {
  public selectedSource = output<{
    sourceType: SourceType
    mediaSourceType: MediaSourceType
  }>()

  public sourceType = SourceType

  public mediaSourceType = MediaSourceType

  public selectSource(
    sourceType: SourceType,
    mediaSourceType: MediaSourceType
  ): void {
    this.selectedSource.emit({
      sourceType: sourceType,
      mediaSourceType: mediaSourceType,
    })
  }
}
