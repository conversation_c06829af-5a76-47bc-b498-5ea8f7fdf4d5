import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { UploadDetailComponent } from './upload-detail.component'
import { ComponentFixture, TestBed } from '@angular/core/testing'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('UploadDetailComponent', () => {
  let component: UploadDetailComponent
  let fixture: ComponentFixture<UploadDetailComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [UploadDetailComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(UploadDetailComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
