import { ComponentFixture, TestBed } from '@angular/core/testing'
import { UploadStatusComponent } from './upload-status.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('UploadStatusComponent', () => {
  let component: UploadStatusComponent
  let fixture: ComponentFixture<UploadStatusComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [UploadStatusComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(UploadStatusComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
