import { ComponentFixture, TestBed } from '@angular/core/testing'
import { AddCustomFieldComponent } from './add-custom-field.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('AddCustomFieldComponent', () => {
  let component: AddCustomFieldComponent
  let fixture: ComponentFixture<AddCustomFieldComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AddCustomFieldComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(AddCustomFieldComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
