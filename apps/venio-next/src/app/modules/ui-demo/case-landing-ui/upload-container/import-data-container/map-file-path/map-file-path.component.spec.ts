import { ComponentFixture, TestBed } from '@angular/core/testing'
import { MapFilePathComponent } from './map-file-path.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('MapFilePathComponent', () => {
  let component: MapFilePathComponent
  let fixture: ComponentFixture<MapFilePathComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [MapFilePathComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(MapFilePathComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
