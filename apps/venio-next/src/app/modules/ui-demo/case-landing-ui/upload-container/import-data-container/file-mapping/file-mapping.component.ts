import { ChangeDetectionStrategy, Component } from '@angular/core'
import { CommonModule } from '@angular/common'
import { FormsModule } from '@angular/forms'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { GridModule } from '@progress/kendo-angular-grid'
import { IconsModule } from '@progress/kendo-angular-icons'
import {
  InputsModule,
  TextBoxModule,
  RadioButtonModule,
  CheckBoxModule,
} from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { DialogAnimation } from '@progress/kendo-angular-dialog'
import { AddCustomFieldComponent } from './add-custom-field/add-custom-field.component'
import { SaveImportTemplateComponent } from './save-import-template/save-import-template.component'
import { xIcon } from '@progress/kendo-svg-icons'

@Component({
  selector: 'venio-file-mapping',
  standalone: true,
  imports: [
    CommonModule,
    ButtonsModule,
    InputsModule,
    TextBoxModule,
    DropDownListModule,
    SvgLoaderDirective,
    IconsModule,
    LabelModule,
    RadioButtonModule,
    CheckBoxModule,
    GridModule,
    FormsModule,
    SaveImportTemplateComponent,
    AddCustomFieldComponent,
  ],
  templateUrl: './file-mapping.component.html',
  styleUrl: './file-mapping.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FileMappingComponent {
  public isOverlayActive = false

  public animation: boolean | DialogAnimation = {
    type: 'slide',
    direction: 'left',
    duration: 300,
  }

  public activeComponent: string | null = null

  public overlayTitle = ''

  public overlayIconUrl = ''

  public icons = {
    closeIcon: xIcon,
    saveIcon: 'assets/svg/icon-save-grey.svg',
    updateIcon: 'assets/svg/icon-reprocessing-update.svg',
  }

  public selectedOption = 0

  public repoGridData = [
    {
      id: 1,
      load_file: 'Attachment Bates Range',
      venio_field: 'Select a value',
      preview_sample: 'No Sample',
    },
    {
      id: 2,
      load_file: 'Attachment Bates Range All',
      venio_field: 'Select a value',
      preview_sample: 'No Sample',
    },
    {
      id: 3,
      load_file: 'Attachment Control Number',
      venio_field: 'Select a value',
      preview_sample: 'No Sample',
    },
  ]

  public venioFieldOptions: string[] = ['Option 1', 'Option 2', 'Option 3']

  public openOverlay(component: string): void {
    this.isOverlayActive = true
    this.activeComponent = component

    // Set the overlay title based on the active component
    if (component === 'add-fields') {
      this.overlayTitle = 'Add Custom Fields'
      this.overlayIconUrl = this.icons.updateIcon
    } else if (component === 'save-import') {
      this.overlayTitle = 'Save Import Template'
      this.overlayIconUrl = this.icons.saveIcon
    }
  }

  /**
   * Closes the overlay and resets the active component.
   */
  public closeOverlay(): void {
    this.isOverlayActive = false
    this.activeComponent = null
    this.overlayTitle = ''
    this.overlayIconUrl = ''
  }
}
