import { ComponentFixture, TestBed } from '@angular/core/testing'
import { UploadSourceSelectionComponent } from './upload-source-selection.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('UploadSourceSelectionComponent', () => {
  let component: UploadSourceSelectionComponent
  let fixture: ComponentFixture<UploadSourceSelectionComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [UploadSourceSelectionComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(UploadSourceSelectionComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
