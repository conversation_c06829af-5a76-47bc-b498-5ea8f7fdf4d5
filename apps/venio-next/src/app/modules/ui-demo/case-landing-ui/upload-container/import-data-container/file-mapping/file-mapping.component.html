<div class="t-flex t-flex-col t-flex-1">
  <div class="t-flex t-flex-col t-gap-5 t-flex-1">
    <div
      class="t-flex t-flex-row t-justify-between t-gap-5 t-border t-border-b-1 t-border-t-1 t-border-l-0 t-border-r-0 t-border-[#0000001A] t-border-solid t-py-[10px]">
      <div class="t-flex t-flex-row t-gap-4">
        <p class="t-font-medium t-text-[16px] t-text-[#000000DE] t-self-center">
          Field Mapping
        </p>
      </div>
    </div>

    <div
      class="t-flex t-flex-row t-gap-5 t-justify-between t-border t-border-b-1 t-border-t-0 t-border-l-0 t-border-r-0 t-border-[#0000001A] t-border-solid t-pb-[10px]">
      <div class="t-flex t-flex-row t-gap-5 t-flex-1">
        <div class="t-flex t-flex-row t-gap-1">
          <p
            class="t-font-medium t-text-[12px] t-text-[#000000DE] t-self-center">
            Load File Field
          </p>
          <span
            class="!t-bg-[#9BD2A7] t-w-[27px] t-h-[27px] t-p-[2px] t-m-1"
            venioSvgLoader
            applyEffectsTo="fill"
            color="#FFFFFF"
            svgUrl="assets/svg/icon-swap.svg"
            height="1.5rem"
            width="1.5rem"></span>
          <p
            class="t-font-medium t-text-[12px] t-text-[#000000DE] t-self-center">
            Venio Field
          </p>
        </div>

        <kendo-textbox
          class="!t-border-[#ccc] !t-w-[310px]"
          placeholder="Search Field For Mapping"
          [clearButton]="true">
          <ng-template kendoTextBoxSuffixTemplate>
            <button
              kendoButton
              fillMode="clear"
              class="t-text-[#1EBADC]"
              imageUrl="assets/svg/icon-updated-search.svg"></button>
          </ng-template>
        </kendo-textbox>
      </div>

      <div class="t-flex t-flex-row t-gap-3">
        <button
          kendoButton
          class="t-p-0 t-px-2 t-m-0"
          fillMode="outline"
          (click)="openOverlay('add-fields')">
          <span class="t-text-[#1DBADC] t-font-medium t-text-[23px]">+</span>
        </button>

        <button
          kendoButton
          class="t-p-0 t-px-2 t-m-0"
          fillMode="outline"
          (click)="openOverlay('save-import')">
          <span
            venioSvgLoader
            applyEffectsTo="fill"
            svgUrl="assets/svg/icon-save.svg"
            height="1rem"
            width="1rem"></span>
        </button>
      </div>
    </div>

    <kendo-grid
      class="t-pb-5"
      [kendoGridBinding]="repoGridData"
      venioDynamicHeight
      [filterable]="'menu'"
      [resizable]="true">
      <kendo-grid-column
        [filterable]="true"
        headerClass="t-text-primary"
        field="load_file"
        title="Load File">
        <ng-template
          kendoGridFilterMenuTemplate
          kendoGridFilterMenuTemplate
          let-column="column"
          let-filter="filter"
          let-filterService="filterService">
        </ng-template
      ></kendo-grid-column>
      <kendo-grid-column
        headerClass="t-text-primary"
        [filterable]="false"
        field="venio_field"
        title="Venio Fields">
        <ng-template kendoGridCellTemplate let-dataItem>
          <kendo-dropdownlist
            class="!focus:t-border-none !hover:t-border-none t-border-none t-bg-transparent"
            [data]="venioFieldOptions"
            [(ngModel)]="dataItem.venio_field"
            [defaultItem]="'Select a value'"
            [valuePrimitive]="true">
          </kendo-dropdownlist>
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column
        headerClass="t-text-primary"
        [filterable]="false"
        field="preview_sample"
        title="Preview Samples From Load File"></kendo-grid-column>
    </kendo-grid>

    <div class="t-flex t-flex-row t-gap-5 t-pt-1">
      <div class="t-flex t-flex-col t-gap-1 t-flex-1">
        <p class="t-font-black t-font-bold t-text-[14px]">Add Custodian</p>
        <div class="t-flex t-items-center">
          <div>
            <input
              id="default-limit"
              type="radio"
              size="small"
              [(ngModel)]="selectedOption"
              [value]="0"
              kendoRadioButton />
          </div>
          <div class="t-w-auto t-flex t-flex-wrap">
            <kendo-label
              class="t-px-[5px] t-py-2 t-relative t-top-[2px]"
              [ngStyle]="{ color: selectedOption === 0 ? '#2F3080' : '' }"
              text="Import data to custodians mapped with selected load file"></kendo-label>
          </div>
        </div>
        <kendo-dropdownlist
          textField="text"
          valueField="value"
          title="Select load file field"
          class="t-w-[260px]"
          [popupSettings]="{ popupClass: 'v-hide-placeholder' }"
          [valuePrimitive]="true">
        </kendo-dropdownlist>
        <div class="t-flex t-items-center">
          <div>
            <input
              id="default-limit"
              type="radio"
              value="0"
              size="small"
              kendoRadioButton />
          </div>
          <div class="t-w-auto t-flex t-flex-wrap">
            <kendo-label
              class="t-px-[5px] t-py-2 t-relative t-top-[2px]"
              text="Import all data to custodians"></kendo-label>
          </div>
        </div>
        <kendo-textbox
          class="!t-border-[#ccc] !t-w-[260px]"
          [disabled]="true"
          placeholder="Select or input custodian name">
        </kendo-textbox>
      </div>
      <div class="v-dashed-sperator t-opacity-100 t-w-[1px] t-h-auto"></div>

      <div class="t-flex t-flex-col t-gap-1 t-flex-1">
        <p class="t-font-black t-font-bold t-text-[14px]">Add Media</p>
        <div class="t-flex t-items-center">
          <div>
            <input
              id="default-limit"
              type="radio"
              size="small"
              [(ngModel)]="selectedOption"
              [value]="0"
              kendoRadioButton />
          </div>
          <div class="t-w-auto t-flex t-flex-wrap">
            <kendo-label
              class="t-px-[5px] t-py-2 t-relative t-top-[2px]"
              [ngStyle]="{ color: selectedOption === 0 ? '#2F3080' : '' }"
              text="Import data to media mapped with selected load file">
            </kendo-label>
          </div>
        </div>

        <kendo-dropdownlist
          textField="text"
          valueField="value"
          title="Select load file field"
          class="t-w-[260px]"
          [popupSettings]="{ popupClass: 'v-hide-placeholder' }"
          [valuePrimitive]="true">
        </kendo-dropdownlist>
        <div class="t-flex t-items-center">
          <div>
            <input
              id="default-limit"
              type="radio"
              value="0"
              size="small"
              kendoRadioButton />
          </div>
          <div class="t-w-auto t-flex t-flex-wrap">
            <kendo-label
              class="t-px-[5px] t-py-2 t-relative t-top-[2px]"
              text="Import all data to media"></kendo-label>
          </div>
        </div>
        <kendo-textbox
          class="!t-border-[#ccc] !t-w-[260px]"
          [disabled]="true"
          placeholder="Enter media name">
        </kendo-textbox>
      </div>
    </div>
  </div>

  <div class="t-flex t-gap-4 t-justify-end">
    <button
      kendoButton
      class="v-custom-secondary-button !t-rounded-none t-self-end"
      themeColor="secondary"
      fillMode="outline"
      data-qa="upload-button">
      PREVIOUS
    </button>

    <button
      kendoButton
      class="v-custom-secondary-button !t-rounded-none t-self-end"
      themeColor="secondary"
      fillMode="outline"
      data-qa="upload-button">
      VALIDATE
    </button>
    <button
      kendoButton
      class="v-custom-secondary-button !t-rounded-none t-self-end"
      themeColor="secondary"
      fillMode="outline"
      data-qa="upload-button">
      IMPORT
    </button>
  </div>
</div>

<div
  class="t-fixed t-top-[1px] t-left-0 t-w-full t-h-full t-bg-[#212121] t-opacity-10 t-z-[1999]"
  *ngIf="isOverlayActive"
  (click)="isOverlayActive = !isOverlayActive"></div>
<div
  class="t-fixed t-top-[1px] t-w-[56%] t-h-full t-bg-white t-overflow-hidden t-shadow-[0px_20px_16px_6px_rgba(0,0,0,0.212)] t-z-[2000] t-transition-all t-duration-400 t-p-5"
  [ngClass]="{
    't-right-0': isOverlayActive,
    't-right-[-56%]': !isOverlayActive
  }">
  <div
    class="t-flex t-justify-between t-items-center t-w-full t-border t-border-b-1 t-border-t-0 t-border-l-0 t-border-r-0 t-border-[#0000001A] t-border-solid">
    <span
      class="t-inline-flex t-items-center t-gap-3 t-text-primary t-text-lg t-font-semibold">
      <button
        class="t-w-[35px] t-p-0 t-bg-[#F3F3F3] t-rounded-full t-h-[35px] t-flex t-items-center"
        fillMode="clear"
        kendoButton
        [imageUrl]="overlayIconUrl"></button>
      <span class="t-text-[16px] t-font-medium">{{ overlayTitle }}</span>
    </span>
    <button
      (click)="closeOverlay()"
      class="v-dialog-action-button v-dialog-action-button-cancel v-dialog-close t-rounded-full t-text-white t-w-6 t-h-6 t-p-0 t-bg-[#ED7425] t-leading-none">
      <kendo-svg-icon [icon]="icons.closeIcon"></kendo-svg-icon>
    </button>
  </div>

  <venio-save-import-template
    class="t-flex t-flex-col t-w-full t-h-[calc(100%_-_2rem)]"
    *ngIf="activeComponent === 'save-import'"></venio-save-import-template>

  <venio-add-custom-field
    class="t-flex t-flex-col t-w-full t-h-[calc(100%_-_2rem)]"
    *ngIf="activeComponent === 'add-fields'"></venio-add-custom-field>
</div>
