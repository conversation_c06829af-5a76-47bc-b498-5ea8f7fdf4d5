import { ChangeDetectionStrategy, Component } from '@angular/core'
import { CommonModule } from '@angular/common'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { GridModule } from '@progress/kendo-angular-grid'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { ButtonsModule } from '@progress/kendo-angular-buttons'

@Component({
  selector: 'venio-add-custom-field',
  standalone: true,
  imports: [
    CommonModule,
    InputsModule,
    GridModule,
    SvgLoaderDirective,
    ButtonsModule,
  ],
  templateUrl: './add-custom-field.component.html',
  styleUrl: './add-custom-field.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AddCustomFieldComponent {
  public repoGridData: any[] = [
    {
      id: 1,
      name: 'Source One',
    },
    {
      id: 2,
      name: 'Venio',
    },
  ]
}
