import { ComponentFixture, TestBed } from '@angular/core/testing'
import { SelectLoadFileComponent } from './select-load-file.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('SelectLoadFileComponent', () => {
  let component: SelectLoadFileComponent
  let fixture: ComponentFixture<SelectLoadFileComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SelectLoadFileComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(SelectLoadFileComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
