<div class="t-flex t-flex-col t-gap-5 t-flex-1">
  <div
    class="t-flex t-flex-row t-justify-between t-gap-5 t-border t-border-b-1 t-border-t-1 t-border-l-0 t-border-r-0 t-border-[#0000001A] t-border-solid t-py-[10px]">
    <div class="t-flex t-flex-row t-gap-4">
      <p class="t-font-medium t-text-[16px] t-text-[#000000DE] t-self-center">
        Unstructured Data
      </p>

      <kendo-dropdownlist
        [(ngModel)]="selectedSource"
        [data]="sourceList"
        textField="text"
        valueField="value"
        title="Select Document"
        class="t-w-48"
        [defaultItem]="placeholderItem"
        [popupSettings]="{ popupClass: 'v-hide-placeholder' }"
        [valuePrimitive]="true">
        <ng-template kendoDropDownListHeaderTemplate>
          <div
            class="t-border-b-[#979797] t-border-b-[2px] t-border-dashed t-w-full t-pb-2">
            Source
          </div>
        </ng-template>
      </kendo-dropdownlist>

      @if(selectedSource === 2){
      <div class="t-flex t-flex-row t-gap-2 t-self-center">
        <p class="t-text-[12px] t-text-[#999999]">Download sample csv</p>
        <button
          kendoButton
          class="t-bg-[#ffffff] t-p-0 t-w-[12px] t-h-[12px] t-cursor-pointer t-self-center"
          rounded="full"
          fillMode="clear"
          title="Delete">
          <span
            venioSvgLoader
            class="t-w-[15px] t-h-[18px] t-rounded-full t-bg-[#ffffff]"
            svgUrl="assets/svg/icon-material-round-sim-card-download.svg"></span>
        </button>
      </div>
      }
    </div>
    @if(selectedSource === 2){
    <p class="t-text-[10px] t-text-[#232323] t-self-center">
      <span class="t-font-bold t-text-[#ED7428]">NOTE: </span>Click on grid cell
      to edit custodian name, Media, Source
    </p>
    }
  </div>

  <!----------------- LOCAL COMPUTER BLOCK----------------->
  @if(selectedSource === 1){

  <div class="t-flex t-flex-row t-justify-between">
    <div
      class="v-custom-upload-container t-mt-3 t-flex t-flex-1 t-h-2/5 t-mr-[20px] t-relative"
      id="uploader">
      <div
        #dropZone
        class="v-upload-area t-flex t-flex-col t-border-2 t-border-dashed t-border-[#1DBADC] t-p-3 t-min-h-[214px] t-flex-1 t-items-center t-justify-center t-text-center t-cursor-pointer relative"
        (dragover)="onDragOver($event)"
        (dragleave)="onDragLeave($event)"
        (drop)="onDrop($event)"
        (click)="triggerFileInput()">
        <input type="file" #fileInput hidden />
        <!-- Add this hidden input -->

        <p class="t-text-[14px] t-font-normal t-text-[#5E6366]">
          Drag & Drop files or Click
          <span class="t-font-bold t-text-[#1EBADC] t-cursor-pointer"
            >here</span
          >
          to upload files
        </p>

        <p
          class="t-flex t-flex-row t-text-[#FEB43C] t-font-normal t-text-[12px] t-mt-3"
          (click)="openFileInfoDialog($event)">
          <span>
            <img
              src="assets/svg/icon-warning.svg"
              height="14"
              width="15"
              alt="Warning"
              class="t-mx-1" />
          </span>
          Only container files are supported!
        </p>

        <!-- File Preview -->
        <div>
          <div class="t-p-[10px] t-flex t-items-center t-w-full">
            <div class="t-flex t-flex-wrap t-gap-5 t-items-center t-w-full">
              <div class="t-flex t-flex-col">
                <div class="t-flex t-flex-row t-gap-1">
                  <img
                    src="assets/svg/icon-zip-file.svg"
                    height="16"
                    width="19"
                    alt="Source.zip" />
                  <div class="t-flex t-flex-col">
                    <p
                      class="t-text-[12px] t-text-[#263238] t-font-bold t-my-1">
                      Source.zip
                    </p>
                    <p class="t-text-[10px] t-text-[#A9A9A9] t-font-medium">
                      180 GB
                    </p>
                  </div>
                </div>
              </div>

              <div class="t-flex t-flex-col">
                <div class="t-flex t-flex-row t-gap-1">
                  <img
                    src="assets/svg/icon-zip-file.svg"
                    height="16"
                    width="19"
                    alt="Source.zip" />
                  <div class="t-flex t-flex-col">
                    <p
                      class="t-text-[12px] t-text-[#263238] t-font-bold t-my-1">
                      Source.zip
                    </p>
                    <p class="t-text-[10px] t-text-[#A9A9A9] t-font-medium">
                      180 GB
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="t-flex t-flex-col t-gap-5 t-bg-[#9BD2A70D] t-p-3 t-w-[304px] t-h-[78vh] t-overflow-hidden">
      <p class="t-text-[#263238] t-text-[14px] t-font-medium">
        Selected Files & Folders
      </p>
      <div class="t-flex t-flex-col t-justify-between t-flex-1">
        <div
          class="t-flex t-flex-col t-max-h-[75%] t-overflow-y-auto t-p-[10px] t-gap-5 flex-1">
          <div
            class="t-flex t-flex-col t-gap-2 t-shadow-[0px_6px_10px_#00000029] t-p-[20px] t-rounded-[2px]">
            <div class="t-flex t-flex-row t-justify-between">
              <div class="t-flex t-items-center">
                <span class="t-text-[#000000] t-text-[14px]">Source One</span>
              </div>

              <button
                kendoButton
                class="t-bg-[#ffffff] t-p-0 t-w-[12px] t-h-[12px]"
                rounded="full"
                fillMode="clear"
                title="Delete">
                <span
                  venioSvgLoader
                  class="t-w-[20px] t-h-[20px] t-rounded-full t-bg-[#ffffff]"
                  svgUrl="assets/svg/Icon-material-delete-grey.svg"></span>
              </button>
            </div>

            <kendo-dropdownlist
              [valuePrimitive]="true"
              textField="CustodianName"
              valueField="CustodianName"
              placeholder="Assign your custodian value">
            </kendo-dropdownlist>

            <kendo-textbox
              placeholder="Enter media name"
              type="text"
              class="t-flex t-flex-1">
            </kendo-textbox>

            <kendo-textbox
              placeholder="Enter password"
              type="text"
              class="t-flex t-flex-1">
            </kendo-textbox>
          </div>

          <div
            class="t-flex t-flex-col t-gap-2 t-shadow-[0px_6px_10px_#00000029] t-p-[20px] t-rounded-[2px]">
            <div class="t-flex t-flex-row t-justify-between">
              <div class="t-flex t-items-center">
                <span class="t-text-[#000000] t-text-[14px]">Source Two</span>
              </div>

              <button
                kendoButton
                class="t-bg-[#ffffff] t-p-0 t-w-[12px] t-h-[12px]"
                rounded="full"
                fillMode="clear"
                title="Delete">
                <span
                  venioSvgLoader
                  class="t-w-[20px] t-h-[20px] t-rounded-full t-bg-[#ffffff]"
                  svgUrl="assets/svg/Icon-material-delete-grey.svg"></span>
              </button>
            </div>

            <kendo-dropdownlist
              [valuePrimitive]="true"
              textField="CustodianName"
              valueField="CustodianName"
              placeholder="Assign your custodian value">
            </kendo-dropdownlist>

            <kendo-textbox
              placeholder="Enter media name"
              type="text"
              class="t-flex t-flex-1">
            </kendo-textbox>

            <kendo-textbox
              placeholder="Enter password"
              type="text"
              class="t-flex t-flex-1">
            </kendo-textbox>
          </div>
        </div>

        <button
          kendoButton
          class="v-custom-secondary-button !t-rounded-none t-self-end"
          themeColor="secondary"
          fillMode="outline"
          data-qa="upload-button">
          PROCESS
        </button>
      </div>
    </div>
  </div>
  }

  <!----------------- LOCAL COMPUTER BLOCK END----------------->

  <!----------------- BATCH MEDIA BLOCK----------------->

  @if(selectedSource === 2){
  <div class="t-flex t-gap-[30px] t-flex-row">
    <div class="t-flex t-flex-col t-gap-2 t-w-[304px]">
      <p
        class="t-text-[#2F3080] t-font-medium t-text-[16px] t-captalize t-tracking-[0.48px]">
        Upload From Local Computer
      </p>
      <div
        class="v-custom-upload-container t-flex t-h-[64px] t-relative"
        id="uploader">
        <div
          #dropZone
          class="v-upload-area t-flex t-flex-col t-border-2 t-border-dashed t-border-[#1DBADC] t-p-3 t-flex-1 t-items-center t-justify-center t-text-center t-cursor-pointer relative"
          (dragover)="onDragOver($event)"
          (dragleave)="onDragLeave($event)"
          (drop)="onDrop($event)"
          (click)="triggerFileInput()">
          <input type="file" #fileInput hidden />
          <!-- Add this hidden input -->

          <p class="t-text-[14px] t-font-normal t-text-[#5E6366]">
            Drag & Drop files or Click
            <span class="t-font-bold t-text-[#1EBADC] t-cursor-pointer"
              >here</span
            >
            to upload files
          </p>
        </div>
      </div>
      <hr class="t-text-[#0000001A] t-my-2" />
      <p
        class="t-text-[#2F3080] t-font-medium t-text-[16px] t-captalize t-tracking-[0.48px]">
        Repository
      </p>

      <kendo-textbox
        class="!t-border-[#ccc] t-w-full t-h-[40px] !t-rounded-none"
        [(ngModel)]="searchTerm"
        (input)="filterTree()"
        placeholder="Search"
        [clearButton]="true">
        <ng-template kendoTextBoxSuffixTemplate>
          <button
            kendoButton
            fillMode="clear"
            class="t-text-[#1EBADC]"
            imageUrl="assets/svg/icon-updated-search.svg"></button>
        </ng-template>
      </kendo-textbox>

      <hr class="t-text-[#0000001A] t-my-2" />

      <kendo-treeview
        [nodes]="filteredTreeData"
        textField="name"
        kendoTreeViewExpandable
        kendoTreeViewHierarchyBinding
        childrenField="children">
        <ng-template kendoTreeViewNodeTemplate let-dataItem>
          <span class="t-flex t-align-center t-gap-2">
            <!-- Folder Icon -->
            <span
              *ngIf="dataItem.type === 'folder'"
              class="t-flex t-align-center">
              <span
                venioSvgLoader
                class="t-w-[16px] t-h-[16px]"
                svgUrl="assets/svg/icon-folder.svg">
              </span>
            </span>

            <!-- File Icon -->
            <span
              *ngIf="dataItem.type === 'file'"
              class="t-flex t-align-center">
              <span
                venioSvgLoader
                class="t-w-[16px] t-h-[16px]"
                svgUrl="assets/svg/Icon-file-text.svg">
              </span>
            </span>

            <!-- File/Folder Name -->
            <span class="tree-text">{{ dataItem.name }}</span>

            <!-- Upload Button (Only for Uploadable Files) -->
            <span *ngIf="dataItem.type === 'file' && dataItem.uploadable">
              <button
                (click)="onUpload(dataItem)"
                kendoButton
                class="t-bg-[#ffffff] t-p-0 t-w-[16px] t-h-[16px]"
                rounded="full"
                fillMode="clear"
                title="Upload">
                <span
                  venioSvgLoader
                  class="t-w-[16px] t-h-[16px]"
                  svgUrl="assets/svg/download-icon.svg">
                </span>
              </button>
            </span>
          </span>
        </ng-template>
      </kendo-treeview>
    </div>
    <div class="t-flex t-flex-1">
      <kendo-grid
        class="t-grid t-border-b-1 t-relative t-overflow-y-auto t-w-full"
        [kendoGridBinding]="batchMediaData"
        [sortable]="true"
        [scrollable]="'scrollable'"
        [groupable]="false"
        [reorderable]="true"
        [resizable]="true"
        kendoGridSelectBy="id"
        (cellClick)="onCellClick($event)">
        <!-- Non-editable Column: ID -->
        <kendo-grid-column
          headerClass="t-text-primary"
          field="id"
          title="#"
          [width]="50"></kendo-grid-column>

        <!-- Editable Column: Custodian Name -->
        <kendo-grid-column
          headerClass="t-text-primary"
          field="custodian_name"
          title="Custodian Name">
          <ng-template kendoGridCellTemplate let-dataItem let-column="column">
            <span
              *ngIf="!isEditing(dataItem, column.field); else editCustodian">
              {{ dataItem[column.field] }}
            </span>
            <ng-template #editCustodian>
              <input
                kendoTextBox
                [(ngModel)]="dataItem[column.field]"
                (blur)="onCellEditComplete(dataItem, column.field)"
                (keydown.enter)="onCellEditComplete(dataItem, column.field)" />
            </ng-template>
          </ng-template>
        </kendo-grid-column>

        <!-- Editable Column: Media Name -->
        <kendo-grid-column
          headerClass="t-text-primary"
          field="media_name"
          title="Media Name">
          <ng-template kendoGridCellTemplate let-dataItem let-column="column">
            <span *ngIf="!isEditing(dataItem, column.field); else editMedia">
              {{ dataItem[column.field] }}
            </span>
            <ng-template #editMedia>
              <input
                kendoTextBox
                [(ngModel)]="dataItem[column.field]"
                (blur)="onCellEditComplete(dataItem, column.field)"
                (keydown.enter)="onCellEditComplete(dataItem, column.field)" />
            </ng-template>
          </ng-template>
        </kendo-grid-column>

        <!-- Editable Column: Source Path -->
        <kendo-grid-column
          headerClass="t-text-primary"
          field="source_path"
          title="Source Path">
          <ng-template kendoGridCellTemplate let-dataItem let-column="column">
            <span
              *ngIf="!isEditing(dataItem, column.field); else editSourcePath">
              {{ dataItem[column.field] }}
            </span>
            <ng-template #editSourcePath>
              <input
                kendoTextBox
                [(ngModel)]="dataItem[column.field]"
                (blur)="onCellEditComplete(dataItem, column.field)"
                (keydown.enter)="onCellEditComplete(dataItem, column.field)" />
            </ng-template>
          </ng-template>
        </kendo-grid-column>

        <!-- Non-editable Column: Validity -->
        <kendo-grid-column
          headerClass="t-text-primary"
          field="validity"
          title="Validity"
          [width]="100">
          <ng-template kendoGridCellTemplate let-dataItem>
            @if (dataItem?.validity) {
            <kendo-svg-icon
              [icon]="icons?.checkIcon"
              themeColor="success"></kendo-svg-icon>
            } @else {
            <span
              venioSvgLoader
              showOn="hover"
              svgUrl="assets/svg/icon-warning-error-theme-triangle.svg"
              height="16px"
              width="16px"></span>
            }
          </ng-template>
        </kendo-grid-column>
      </kendo-grid>
    </div>
  </div>

  }

  <!----------------- BATCH MEDIA BLOCK END----------------->

  <!----------------- REPOSITORY BLOCK----------------->

  @if(selectedSource === 3){
  <div class="t-flex t-gap-[30px] t-flex-row">
    <div class="t-flex t-flex-col t-gap-2 t-w-[304px]">
      <div class="t-flex t-flex-row t-gap-3 t-items-center t-mb-4">
        <kendo-textbox
          class="!t-border-[#ccc] !t-w-full"
          placeholder="Search"
          [clearButton]="true">
          <ng-template kendoTextBoxSuffixTemplate>
            <button
              kendoButton
              fillMode="clear"
              class="t-text-[#1EBADC]"
              imageUrl="assets/svg/icon-updated-search.svg"></button>
          </ng-template>
        </kendo-textbox>

        <button
          #parentRefresh
          class="!t-p-1.5 t-h-fit !t-border-[#9AD3A6] t-leading-[0.8] hover:!t-bg-[#9AD3A6]"
          kendoButton
          size="none"
          fillMode="outline"
          title="Refresh">
          <span
            [parentElement]="parentRefresh.element"
            venioSvgLoader
            svgUrl="assets/svg/refresh.svg"
            color="#9AD3A6"
            hoverColor="#FFFFFF"
            height="1rem"
            width="1rem"></span>
        </button>
      </div>

      <kendo-grid
        [kendoGridBinding]="repoGridData"
        venioDynamicHeight
        [resizable]="true"
        kendoGridSelectBy="id"
        [height]="140">
        <kendo-grid-column
          headerClass="t-text-primary"
          field="id"
          title="#"
          [width]="50"></kendo-grid-column>
        <kendo-grid-checkbox-column
          [showSelectAll]="true"
          [width]="40"></kendo-grid-checkbox-column>
        <kendo-grid-column
          headerClass="t-text-primary"
          field="name"
          title="Select All"></kendo-grid-column>
      </kendo-grid>
    </div>
    <div class="t-flex t-flex-1 t-flex-col t-gap-3">
      <p class="t-text-[#263238] t-font-medium [14px] t-mb-2">
        Repository Hierarchy
      </p>
      <div class="t-flex t-flex-row t-gap-5 t-items-center t-mb-4">
        <kendo-textbox
          class="!t-border-[#ccc] !t-w-full"
          placeholder="Search"
          [clearButton]="true">
          <ng-template kendoTextBoxSuffixTemplate>
            <button
              kendoButton
              fillMode="clear"
              class="t-text-[#1EBADC]"
              imageUrl="assets/svg/icon-updated-search.svg"></button>
          </ng-template>
        </kendo-textbox>

        <button
          kendoButton
          class="v-custom-secondary-button !t-rounded-none t-self-end"
          themeColor="secondary"
          fillMode="outline"
          data-qa="upload-button">
          ADD
        </button>
      </div>

      <div class="t-flex t-flex-row">
        <button
          #expandBtn
          kendoButton
          class="t-p-0 t-cursor-pointer"
          fillMode="clear"
          title="Expand All"
          (click)="expandAll()">
          <span
            venioSvgLoader
            color="#9BD2A7"
            hoverColor="#7AB88A"
            applyEffectsTo="fill"
            [parentElement]="expandBtn.element"
            svgUrl="assets/svg/plus-circle-expand-icon-svg.svg"
            height="1rem"
            width="1rem">
            <kendo-loader size="small"></kendo-loader>
          </span>
        </button>
        <button
          kendoButton
          #collapseBtn
          class="t-p-0 t-cursor-pointer t-mx-1"
          fillMode="clear"
          title="Collapse All"
          (click)="collapseAll()">
          <span
            venioSvgLoader
            color="#ED7425"
            hoverColor="#C85D1E"
            applyEffectsTo="fill"
            [parentElement]="collapseBtn.element"
            svgUrl="assets/svg/minus-circle-collapse-svg.svg"
            height="1rem"
            width="1rem">
            <kendo-loader size="small"></kendo-loader>
          </span>
        </button>
        <input
          class="t-mx-[2px] !t-h-[16px] !t-w-[16.5px]"
          kendoCheckBox
          type="checkbox"
          [checked]="isAllSelected()"
          (change)="toggleSelectAll($event)" />
        <span class="t-ml-2">Select All</span>
      </div>

      <kendo-treeview
        class="t-ml-[20px]"
        [nodes]="repositoryTreeData"
        textField="text"
        childrenField="items"
        [hasChildren]="hasChildren"
        kendoTreeViewHierarchyBinding
        kendoTreeViewCheckable
        [(checkedKeys)]="checkedKeys"
        checkBy="id"
        kendoTreeViewExpandable
        [(expandedKeys)]="expandedKeys">
        <ng-template kendoTreeViewNodeTemplate let-dataItem>
          <span class="t-flex t-gap-2">
            <span *ngIf="hasChildren(dataItem)" class="t-flex t-align-center">
              <span
                venioSvgLoader
                applyEffectsTo="fill"
                svgUrl="assets/svg/icon-folder.svg"
                height="18px"
                width="18px">
              </span>
            </span>
            <span *ngIf="!hasChildren(dataItem)" class="t-flex t-align-center">
              <span
                venioSvgLoader
                applyEffectsTo="fill"
                svgUrl="assets/svg/Icon-file-text.svg"
                height="16px"
                width="16px">
              </span>
            </span>
            <span class="tree-text">{{ dataItem.text }}</span>
          </span>
        </ng-template>
      </kendo-treeview>
      <p class="t-text-[#1DBADC] t-text-[16px] t-text-wrap t-mt-2">
        If folder exceeds 255 char's we need to notify the user, Saying it is
        exceeding the limit.
      </p>
    </div>
    <div
      class="t-flex t-flex-col t-gap-5 t-bg-[#9BD2A70D] t-p-3 t-w-[304px] t-h-[78vh] t-overflow-hidden">
      <p class="t-text-[#263238] t-text-[14px] t-font-medium">
        Selected Files & Folders
      </p>
      <div class="t-flex t-flex-col t-justify-between t-flex-1">
        <div
          class="t-flex t-flex-col t-max-h-[75%] t-overflow-y-auto t-p-[10px] t-gap-5 flex-1">
          <div
            class="t-flex t-flex-col t-gap-2 t-shadow-[0px_6px_10px_#00000029] t-p-[20px] t-rounded-[2px]">
            <div class="t-flex t-flex-row t-justify-between">
              <div class="t-flex t-items-center">
                <span class="t-text-[#000000] t-text-[14px]">Source One</span>
              </div>

              <button
                kendoButton
                class="t-bg-[#ffffff] t-p-0 t-w-[12px] t-h-[12px]"
                rounded="full"
                fillMode="clear"
                title="Delete">
                <span
                  venioSvgLoader
                  class="t-w-[20px] t-h-[20px] t-rounded-full t-bg-[#ffffff]"
                  svgUrl="assets/svg/Icon-material-delete-grey.svg"></span>
              </button>
            </div>

            <kendo-dropdownlist
              [valuePrimitive]="true"
              textField="CustodianName"
              valueField="CustodianName"
              placeholder="Assign your custodian value">
            </kendo-dropdownlist>

            <kendo-textbox
              placeholder="Enter media name"
              type="text"
              class="t-flex t-flex-1">
            </kendo-textbox>

            <kendo-textbox
              placeholder="Enter password"
              type="text"
              class="t-flex t-flex-1">
            </kendo-textbox>
          </div>

          <div
            class="t-flex t-flex-col t-gap-2 t-shadow-[0px_6px_10px_#00000029] t-p-[20px] t-rounded-[2px]">
            <div class="t-flex t-flex-row t-justify-between">
              <div class="t-flex t-items-center">
                <span class="t-text-[#000000] t-text-[14px]">Source Two</span>
              </div>

              <button
                kendoButton
                class="t-bg-[#ffffff] t-p-0 t-w-[12px] t-h-[12px]"
                rounded="full"
                fillMode="clear"
                title="Delete">
                <span
                  venioSvgLoader
                  class="t-w-[20px] t-h-[20px] t-rounded-full t-bg-[#ffffff]"
                  svgUrl="assets/svg/Icon-material-delete-grey.svg"></span>
              </button>
            </div>

            <kendo-dropdownlist
              [valuePrimitive]="true"
              textField="CustodianName"
              valueField="CustodianName"
              placeholder="Assign your custodian value">
            </kendo-dropdownlist>

            <kendo-textbox
              placeholder="Enter media name"
              type="text"
              class="t-flex t-flex-1">
            </kendo-textbox>

            <kendo-textbox
              placeholder="Enter password"
              type="text"
              class="t-flex t-flex-1">
            </kendo-textbox>
          </div>
        </div>

        <button
          kendoButton
          class="v-custom-secondary-button !t-rounded-none t-self-end"
          themeColor="secondary"
          fillMode="outline"
          data-qa="upload-button">
          PROCESS
        </button>
      </div>
    </div>
  </div>

  }

  <!----------------- REPOSITORY BLOCK END----------------->
</div>
<div kendoDialogContainer></div>

<!--- File Info Dialog content Start -->
<ng-template #fileInfoContent>
  <kendo-dialog-titlebar class="t-pl-0">
    <div class="t-flex t-flex-row t-justify-center t-items-center">
      <div class="t-flex t-flex-row t-mr-[20px]">
        <div
          class="t-bg-[#0000000C] t-w-[40px] t-h-[40px] t-flex t-items-center t-justify-center t-rounded-full">
          <img
            src="assets/svg/share-svgrepo.svg"
            alt="Share Icon"
            style="width: 12px; height: 14px" />
        </div>
        <div
          class="t-flex t-text-[#2F3080DE] t-text-[16px] t-font-medium t-relative t-top-[10px] t-ml-2">
          File Type Information
        </div>
      </div>
    </div>
  </kendo-dialog-titlebar>
  <div class="t-flex t-my-5 t-w-[90%]">
    <kendo-grid
      class="t-flex t-flex-col-reverse t-relative t-overflow-y-auto"
      [kendoGridBinding]="fileTypesInfo"
      [resizable]="true">
      <kendo-grid-column headerClass="t-text-primary" title="#" [width]="50">
        <ng-template kendoGridCellTemplate let-rowIndex="rowIndex">
          {{ rowIndex + 1 }}
        </ng-template>
      </kendo-grid-column>
      <kendo-grid-column
        headerClass="t-text-primary"
        field="fileType"
        title="File Type"></kendo-grid-column>
      <kendo-grid-column
        headerClass="t-text-primary"
        field="extension"
        title="File Extension"></kendo-grid-column>
    </kendo-grid>
  </div>
</ng-template>
<!--- File Info Dialog content End -->
