import { ChangeDetectionStrategy, Component, inject } from '@angular/core'
import { CommonModule } from '@angular/common'
import { IconsModule } from '@progress/kendo-angular-icons'
import { SelectLoadFileComponent } from './select-load-file/select-load-file.component'
import { MapFilePathComponent } from './map-file-path/map-file-path.component'
import { FileMappingComponent } from './file-mapping/file-mapping.component'
import { LayoutModule } from '@progress/kendo-angular-layout'
import { checkIcon, pencilIcon } from '@progress/kendo-svg-icons'
import { DialogRef, DialogsModule } from '@progress/kendo-angular-dialog'

@Component({
  selector: 'venio-import-data-container',
  standalone: true,
  imports: [
    CommonModule,
    IconsModule,
    LayoutModule,
    SelectLoadFileComponent,
    MapFilePathComponent,
    FileMappingComponent,
    DialogsModule,
  ],
  templateUrl: './import-data-container.component.html',
  styleUrl: './import-data-container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ImportDataContainerComponent {
  public readonly pencilIcon = pencilIcon

  public readonly checkIcon = checkIcon

  private dialogRef = inject(DialogRef)

  public steps = [
    { label: 'SELECT LOAD FILE' },
    { label: 'MAP FILE PATH' },
    { label: 'FIELD MAPPING' },
  ]

  public currentStepIndex = 0

  public goToStep(index: number): void {
    this.currentStepIndex = index
  }

  public activateStep(event: string): void {
    const matchedStepIndex = this.steps.findIndex(
      (step) => step.label === event
    )

    if (matchedStepIndex !== -1) {
      this.currentStepIndex = matchedStepIndex
    }
  }

  public close(status: string): void {
    this.dialogRef.close()
  }
}
