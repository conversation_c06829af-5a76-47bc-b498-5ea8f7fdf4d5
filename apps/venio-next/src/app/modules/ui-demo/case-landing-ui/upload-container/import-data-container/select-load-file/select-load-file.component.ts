import { ChangeDetectionStrategy, Component } from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { TextBoxModule } from '@progress/kendo-angular-inputs'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'

@Component({
  selector: 'venio-select-load-file',
  standalone: true,
  imports: [
    CommonModule,
    DropDownListModule,
    ButtonsModule,
    TextBoxModule,
    SvgLoaderDirective,
  ],
  templateUrl: './select-load-file.component.html',
  styleUrl: './select-load-file.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SelectLoadFileComponent {
  public defaultItem = {
    text: 'Select Template',
    value: -1,
  }

  public sourceList = []
}
