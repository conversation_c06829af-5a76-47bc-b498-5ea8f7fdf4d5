<div class="t-flex t-flex-row t-gap-[30px] t-my-[10px]">
  <div class="t-flex t-h-[300px]">
    <div class="t-flex t-self-stretch t-flex-1">
      <kendo-stepper
        class="v-custom-upload-stepper"
        [style.width.px]="150"
        [steps]="steps"
        [currentStep]="currentStepIndex"
        stepType="full"
        orientation="vertical"
        (currentStepChange)="goToStep($event)">
        <ng-template kendoStepperStepTemplate let-step let-index="index">
          <div class="t-flex t-items-center" (click)="activateStep(step.label)">
            <!-- Icon Container -->
            <div
              class="k-step-indicator t-w-8 t-h-8 t-rounded-full t-flex t-items-center t-justify-center">
              <kendo-svg-icon
                *ngIf="index === currentStepIndex"
                [icon]="pencilIcon"
                class="t-text-white"></kendo-svg-icon>

              <kendo-svg-icon
                *ngIf="index < currentStepIndex"
                [icon]="checkIcon"
                class="t-text-white"></kendo-svg-icon>
            </div>

            <!-- Step Label -->
            <span
              class="t-ml-1"
              [ngClass]="{
                't-text-[#4A4B90]': index === currentStepIndex,
                't-text-[#9BD2A7]': index < currentStepIndex,
                't-text-[#C7C7C7]': index > currentStepIndex
              }">
              {{ step.label }}
            </span>
          </div>
        </ng-template>
      </kendo-stepper>
    </div>
  </div>

  <venio-upload-source-selection
    class="t-flex t-flex-1"
    [ngClass]="{ 't-hidden': currentStepIndex !== 0 }"
    style="padding: 5px 40px" />

  <venio-upload-detail
    class="t-flex t-flex-1"
    [ngClass]="{ 't-hidden': currentStepIndex !== 1 }" />

  <venio-upload-status
    class="t-flex t-flex-1"
    [ngClass]="{ 't-hidden': currentStepIndex !== 2 }" />
</div>
