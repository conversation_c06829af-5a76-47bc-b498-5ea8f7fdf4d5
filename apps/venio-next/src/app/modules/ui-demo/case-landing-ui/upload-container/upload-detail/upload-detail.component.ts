import { TreeViewModule } from '@progress/kendo-angular-treeview'
import {
  ChangeDetectionStrategy,
  Component,
  inject,
  ViewChild,
  ElementRef,
  TemplateRef,
  signal,
  output,
} from '@angular/core'
import { CommonModule, NgOptimizedImage } from '@angular/common'
import { UploadModule } from '@progress/kendo-angular-upload'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { DialogService, DialogsModule } from '@progress/kendo-angular-dialog'
import { GridModule } from '@progress/kendo-angular-grid'
import { CheckBoxModule, InputsModule } from '@progress/kendo-angular-inputs'
import { FormsModule } from '@angular/forms'
import { ButtonModule, ButtonsModule } from '@progress/kendo-angular-buttons'
import { IconsModule } from '@progress/kendo-angular-icons'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { checkIcon } from '@progress/kendo-svg-icons'
import { LoaderModule } from '@progress/kendo-angular-indicators'

interface TreeNode {
  name: string
  type: 'folder' | 'file'
  uploadable?: boolean
  children?: TreeNode[]
}

@Component({
  selector: 'venio-upload-detail',
  standalone: true,
  imports: [
    CommonModule,
    DropDownListModule,
    GridModule,
    ButtonModule,
    DialogsModule,
    NgOptimizedImage,
    InputsModule,
    FormsModule,
    UploadModule,
    ButtonsModule,
    IconsModule,
    SvgLoaderDirective,
    TreeViewModule,
    LoaderModule,
    CheckBoxModule,
  ],
  templateUrl: './upload-detail.component.html',
  styleUrl: './upload-detail.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UploadDetailComponent {
  @ViewChild('fileInput') private fileInput!: ElementRef

  @ViewChild('dropZone', { static: false })
  public dropZone!: ElementRef<HTMLDivElement>

  @ViewChild('fileInfoContent') private fileInfoContent!: TemplateRef<any>

  private dialogService = inject(DialogService)

  public uploadedFile: { name: string; size: number }[] | null = null

  public errorMessage: string | null = null

  public uploadStatusVisible = output<boolean>()

  public icons = { checkIcon: checkIcon }

  public selectedSource = 1

  public uploadProgressEmit = output<{
    UploadedFileSize: number
    OriginalFileSize: number
    UploadProgressPercent: number
    UploadStartedDate: string
    UploadedBy: string
  }>()

  public batchMediaData: Array<{
    id: number
    custodian_name: string
    media_name: string
    source_path: string
    validity: boolean
  }> = [
    {
      id: 1,
      custodian_name: 'John Vs Sate Of Florida',
      media_name: 'Media One',
      source_path: '//Source/Source/Hh/..',
      validity: false,
    },
    {
      id: 2,
      custodian_name: 'John Vs Sate Of Florida',
      media_name: 'Media One',
      source_path: '//Source/Source Two/Hh/..',
      validity: true,
    },
  ]

  /*********EDITABLE CELL CODE****** */
  public editingCell: { id: number; field: string } | null = null

  public onCellClick(event: any): void {
    if (
      ['custodian_name', 'media_name', 'source_path'].includes(
        event.column.field
      )
    ) {
      this.editingCell = { id: event.dataItem.id, field: event.column.field }
    }
  }

  public isEditing(dataItem: any, field: string): boolean {
    return (
      this.editingCell?.id === dataItem.id && this.editingCell?.field === field
    )
  }

  public onCellEditComplete(dataItem: any, field: string): void {
    this.editingCell = null
  }
  /*********EDITABLE CELL CODE****** */

  public treeData: TreeNode[] = [
    {
      name: 'Source One',
      type: 'folder',
      children: [
        { name: 'Case00.Csv', type: 'file' },
        { name: 'Case 3.Csv', type: 'file' },
      ],
    },
    {
      name: 'Venio',
      type: 'folder',
      children: [{ name: 'Case Cus.Csv', type: 'file', uploadable: true }],
    },
  ]

  public filteredTreeData: any[] = [...this.treeData]

  public searchTerm = ''

  public filterTree(): void {
    if (!this.searchTerm) {
      this.filteredTreeData = [...this.treeData]
      return
    }

    const searchLower = this.searchTerm.toLowerCase()

    this.filteredTreeData = this.#filterRecursive(this.treeData, searchLower)
  }

  #filterRecursive(nodes: TreeNode[], search: string): TreeNode[] {
    return nodes
      .map((node) => {
        if (node.name.toLowerCase().includes(search)) {
          return node // Return entire node if it matches
        } else if (node.children) {
          const filteredChildren = this.#filterRecursive(node.children, search)
          if (filteredChildren.length) {
            return { ...node, children: filteredChildren }
          }
        }
        return null
      })
      .filter((node): node is TreeNode => node !== null) // Type guard for filtering
  }

  public onUpload(file: any): void {
    console.log(`Uploading ${file.name}`)
  }

  /********* RESPOSITORY STATIC DATA********** */

  public repoGridData: any[] = [
    {
      id: 1,
      name: 'Source One',
    },
    {
      id: 2,
      name: 'Venio',
    },
  ]

  public repositoryTreeData: any[] = [
    {
      id: '1',
      text: 'Source One',
      items: [
        { id: '1.1', text: '25498261-Mp4_H264_Aac_512Kb.Mp4' },
        { id: '1.2', text: '25498261-Mp4_H264_Aac_512Kb.Mp4' },
      ],
    },
    {
      id: '2',
      text: 'Venio',
      items: [{ id: '2.1', text: 'EDRM Public Download.Zip' }],
    },
  ]

  /** Holds the checked node IDs */
  public checkedKeys: string[] = []

  /** Holds expanded node IDs */
  public expandedKeys: string[] = []

  public hasChildren = (item: any): boolean => {
    return Array.isArray(item.items) && item.items.length > 0
  }

  public expandAll(): void {
    this.expandedKeys = this.getAllNodeIds(this.repositoryTreeData)
  }

  public collapseAll(): void {
    this.expandedKeys = []
  }

  public toggleSelectAll(event: Event): void {
    const isChecked: boolean = (event.target as HTMLInputElement).checked
    this.checkedKeys = isChecked
      ? this.getAllNodeIds(this.repositoryTreeData)
      : []
  }

  public isAllSelected(): boolean {
    const allNodeIds: string[] = this.getAllNodeIds(this.repositoryTreeData)
    return (
      allNodeIds.length > 0 &&
      allNodeIds.every((id) => this.checkedKeys.includes(id))
    )
  }

  private getAllNodeIds(nodes: any[]): string[] {
    let ids: string[] = []
    nodes.forEach((node) => {
      ids.push(node.id)
      if (Array.isArray(node.items) && node.items.length > 0) {
        ids = ids.concat(this.getAllNodeIds(node.items))
      }
    })
    return ids
  }

  /********* RESPOSITORY STATIC DATA********** */

  public placeholderItem = {
    text: 'Local Computer',
    value: 1,
  }

  public fileTypesInfo = [
    {
      fileType: '7Z Archive File',
      extension: '.7Z',
    },
    {
      fileType: '.ZIP File',
      extension: '.ZIP',
    },
    {
      fileType: '.RAR File',
      extension: '.Rar',
    },
    {
      fileType: 'UNIX Tar',
      extension: '.Tar',
    },
    {
      fileType: 'Lotus Notes Database R6.X',
      extension: '.NS2',
    },
    {
      fileType: 'Microsoft Cabinet File',
      extension: '.Cab',
    },
    {
      fileType: 'LZH Compress',
      extension: '.Lzh',
    },
    {
      fileType: 'Self-Extracting LZH',
      extension: '.Lzh',
    },
    {
      fileType: 'UNIX Gzip',
      extension: '.Gz',
    },
    {
      fileType: 'Mbox(RFC-822 Mailbox)',
      extension: '.Mbox',
    },
    {
      fileType: 'MS Office Binder',
      extension: '.OBD',
    },
    {
      fileType: 'Outlook Express File Type',
      extension: '.Dbx',
    },
    {
      fileType: 'Mail Archive DXL',
      extension: '.Dxl',
    },
    {
      fileType: 'Microsoft Office 365 OST File',
      extension: '.Ost',
    },
    {
      fileType: 'Microsoft Outlook PST/OST 2003',
      extension: '.Pst',
    },
    {
      fileType: 'Microsoft Outlook PST/OST 97/2000/XP',
      extension: '.Pst',
    },
    {
      fileType: 'Microsoft Outlook For Mac 2011',
      extension: '.Pst',
    },
    {
      fileType: 'Lotus Notes Database File',
      extension: '.Nsf',
    },
    {
      fileType: 'UNIX Compress',
      extension: '.Gz',
    },
    {
      fileType: 'Forensic Image',
      extension: '.E01, .L01, .Ad1, .Vhd',
    },
  ]

  public sourceList = [
    {
      text: 'Local Computer',
      value: 1,
    },
    {
      text: 'Batch Media',
      value: 2,
    },
    {
      text: 'Repository',
      value: 3,
    },
    {
      text: 'AWS S3 Data',
      value: 4,
    },
  ]

  public isOpenOrClose = signal(false)

  public isInitializeUpload = signal<boolean>(false)

  public openFileInfoDialog(event: Event): void {
    event.stopPropagation()
    this.dialogService.open({
      content: this.fileInfoContent,
      maxWidth: '40vw',
      minWidth: '35%',
    })
  }

  /**
   * ---------------------------------------------------
   * Manual File Upload Code Instead of kendo Component
   * ---------------------------------------------------
   */

  // Trigger file input programmatically
  public triggerFileInput(): void {
    this.fileInput.nativeElement.click()
  }

  public onDragOver(event: DragEvent): void {
    event.preventDefault()
    event.stopPropagation()
    if (this.dropZone) {
      this.dropZone.nativeElement.classList.add('drag-over')
    }
  }

  public onDragLeave(event: DragEvent): void {
    event.preventDefault()
    event.stopPropagation()
    if (this.dropZone) {
      this.dropZone.nativeElement.classList.remove('drag-over')
    }
  }

  public onDrop(event: DragEvent): void {
    event.preventDefault()
    event.stopPropagation()
    if (this.dropZone) {
      this.dropZone.nativeElement.classList.remove('drag-over')
    }
  }

  // Remove file
  public removeFile(index: number): void {
    console.log('Remove files Triggered')
  }

  public uploadedFiles: Array<{ name: string; size: number }> = []

  /**
   * ---------------------------------------------------
   * Manual File Upload Code Instead of kendo Component
   * ---------------------------------------------------
   */
}
