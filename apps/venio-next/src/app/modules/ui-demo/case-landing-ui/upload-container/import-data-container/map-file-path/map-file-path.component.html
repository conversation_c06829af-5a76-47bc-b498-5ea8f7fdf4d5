<div class="t-flex t-flex-col t-flex-1">
  <div class="t-flex t-flex-col t-gap-5 t-flex-1">
    <div
      class="t-flex t-flex-row t-justify-between t-gap-5 t-border t-border-b-1 t-border-t-1 t-border-l-0 t-border-r-0 t-border-[#0000001A] t-border-solid t-py-[10px]">
      <div class="t-flex t-flex-row t-gap-4">
        <p class="t-font-medium t-text-[16px] t-text-[#000000DE] t-self-center">
          Map File Path
        </p>
      </div>

      <button
        kendoButton
        class="v-custom-secondary-button !t-rounded-none t-self-end"
        themeColor="secondary"
        fillMode="outline"
        data-qa="upload-button">
        VALIDATE PATH
      </button>
    </div>

    <div class="t-flex t-flex-row t-gap-1">
      <div class="t-flex t-flex-col t-gap-4 t-flex-1">
        <div class="t-flex- t-flex row t-gap-1">
          <p class="t-font-black t-font-bold t-text-[14px]">
            Process Full-Text
          </p>
          <kendo-switch
            class="v-upload-custom-switch"
            [(ngModel)]="checked"
            size="large"
            onLabel=""
            offLabel=""></kendo-switch>
        </div>

        <label class="t-bg-white t-py-1 t-flex t-items-center">
          <input type="checkbox" kendoCheckBox rounded="small" size="small" />
          <span class="t-pl-2 t-text-sm t-tracking-tight"
            >Load File Has Extracted Text In Field</span
          >
        </label>
        <kendo-dropdowntree
          textField="text"
          [kendoDropDownTreeHierarchyBinding]="treeData"
          valueField="value"
          title="Select load file field"
          childrenField="items"
          [valuePrimitive]="true"
          [popupSettings]="{ popupClass: 'v-hide-placeholder' }">
        </kendo-dropdowntree>

        <kendo-textbox
          placeholder="Native Folder Path"
          type="text"
          class="t-max-w-full t-rounded v-input-shadow">
          <ng-template kendoTextBoxSuffixTemplate>
            <button kendoButton class="t-p-0 t-px-2 t-m-0" fillMode="outline">
              <span
                venioSvgLoader
                applyEffectsTo="fill"
                hoverColor="#FFFFFF"
                color="#9AD3A6"
                svgUrl="assets/svg/icon-dots.svg"
                height="1rem"
                width="1rem"></span>
            </button>
          </ng-template>
        </kendo-textbox>

        <div class="t-flex t-flex-row t-gap-[48px]">
          <span class="t-font-black t-font-bold t-text-[14px]"
            >Number of Fulltext files</span
          >
          <span>42</span>
        </div>
      </div>
      <div
        class="v-dashed-sperator t-mx-4 t-opacity-100 t-w-[1px] t-h-auto"></div>
      <div class="t-flex t-flex-col t-gap-4 t-flex-1">
        <div class="t-flex- t-flex row t-gap-1">
          <p class="t-font-black t-font-bold t-text-[14px]">
            Process Full-Text
          </p>
          <kendo-switch
            class="v-upload-custom-switch"
            [(ngModel)]="checked"
            size="large"
            onLabel=""
            offLabel=""></kendo-switch>
        </div>

        <kendo-dropdowntree
          textField="text"
          [kendoDropDownTreeHierarchyBinding]="treeData"
          valueField="value"
          title="Select load file field"
          childrenField="items"
          [valuePrimitive]="true"
          [popupSettings]="{ popupClass: 'v-hide-placeholder' }">
        </kendo-dropdowntree>

        <kendo-textbox
          placeholder="Native Folder Path"
          type="text"
          class="t-max-w-full t-rounded v-input-shadow">
          <ng-template kendoTextBoxSuffixTemplate>
            <button kendoButton class="t-p-0 t-px-2 t-m-0" fillMode="outline">
              <span
                venioSvgLoader
                applyEffectsTo="fill"
                hoverColor="#FFFFFF"
                color="#9AD3A6"
                svgUrl="assets/svg/icon-dots.svg"
                height="1rem"
                width="1rem"></span>
            </button>
          </ng-template>
        </kendo-textbox>

        <div class="t-flex t-flex-row t-gap-[48px]">
          <span class="t-font-black t-font-bold t-text-[14px]"
            >Number of Fulltext files</span
          >
          <span>42</span>
        </div>

        <label class="t-bg-white t-flex t-items-center">
          <input
            type="checkbox"
            kendoCheckBox
            rounded="small"
            size="small"
            class="t-self-start" />
          <span
            class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap t-relative t-top-[-3px]"
            >Extract Fulltext From Load File If Fulltext Is Missing</span
          >
        </label>
      </div>
      <div
        class="v-dashed-sperator t-mx-4 t-opacity-100 t-w-[1px] t-h-auto"></div>
      <div class="t-flex t-flex-col t-gap-4 t-flex-1">
        <div class="t-flex- t-flex row t-gap-1">
          <p class="t-font-black t-font-bold t-text-[14px]">
            Process Full-Text
          </p>
          <kendo-switch
            class="v-upload-custom-switch"
            [(ngModel)]="checked"
            size="large"
            onLabel=""
            offLabel=""></kendo-switch>
        </div>
        <kendo-dropdownlist
          textField="text"
          valueField="value"
          title="Image Mapping Field"
          [popupSettings]="{ popupClass: 'v-hide-placeholder' }"
          [valuePrimitive]="true">
        </kendo-dropdownlist>
        <kendo-textbox
          placeholder="Image Folder Path"
          type="text"
          class="t-max-w-full t-rounded v-input-shadow">
          <ng-template kendoTextBoxSuffixTemplate>
            <button kendoButton class="t-p-0 t-px-2 t-m-0" fillMode="outline">
              <span
                venioSvgLoader
                applyEffectsTo="fill"
                hoverColor="#FFFFFF"
                color="#9AD3A6"
                svgUrl="assets/svg/icon-dots.svg"
                height="1rem"
                width="1rem"></span>
            </button>
          </ng-template>
        </kendo-textbox>

        <div class="t-flex t-flex-row t-gap-[48px]">
          <span class="t-font-black t-font-bold t-text-[14px]"
            >Number of Fulltext files</span
          >
          <span>42</span>
        </div>
      </div>
    </div>
  </div>

  <div class="t-flex t-gap-4 t-justify-end">
    <button
      kendoButton
      class="v-custom-secondary-button !t-rounded-none t-self-end"
      themeColor="secondary"
      fillMode="outline"
      data-qa="upload-button">
      PREVIOUS
    </button>

    <button
      kendoButton
      class="v-custom-secondary-button !t-rounded-none t-self-end"
      themeColor="secondary"
      fillMode="outline"
      data-qa="upload-button">
      NEXT
    </button>
  </div>
</div>
