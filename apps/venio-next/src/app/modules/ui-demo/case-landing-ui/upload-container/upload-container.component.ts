import { UploadStatusComponent } from './upload-status/upload-status.component'
import { CommonModule } from '@angular/common'
import { ChangeDetectionStrategy, Component, signal } from '@angular/core'
import { IconsModule } from '@progress/kendo-angular-icons'
import { LayoutModule } from '@progress/kendo-angular-layout'
import { UploadSourceSelectionComponent } from './upload-source-selection/upload-source-selection.component'
import { UploadDetailComponent } from './upload-detail/upload-detail.component'
import { checkIcon, pencilIcon } from '@progress/kendo-svg-icons'

@Component({
  selector: 'venio-image-upload-container',
  standalone: true,
  imports: [
    CommonModule,
    IconsModule,
    LayoutModule,
    UploadSourceSelectionComponent,
    UploadDetailComponent,
    UploadStatusComponent,
  ],
  templateUrl: './upload-container.component.html',
  styleUrl: './upload-container.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UploadContainerComponent {
  public readonly pencilIcon = pencilIcon

  public readonly checkIcon = checkIcon

  public triggerGetUploadMediaStatus: any

  // public selectedSource = signal<SourceType>(null)

  public isLimitedServiceLicense = signal<boolean>(false)

  public steps = [
    { label: 'SOURCE SELECT' },
    { label: 'UPLOAD' },
    { label: 'STATUS' },
  ]

  public currentStepIndex = 0

  public uploadedFileProgressStatus = signal<any>(null)

  public goToStep(index: number): void {
    this.currentStepIndex = index
  }

  public activateStep(event: string): void {
    const matchedStepIndex = this.steps.findIndex(
      (step) => step.label === event
    )

    if (matchedStepIndex !== -1) {
      this.currentStepIndex = matchedStepIndex
    }
  }
}
