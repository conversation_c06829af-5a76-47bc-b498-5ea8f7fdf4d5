<div class="t-flex t-flex-col t-gap-3 t-my-3 t-flex-1">
  <kendo-textbox class="!t-border-[#ccc]" placeholder="Name"> </kendo-textbox>
  <kendo-textarea
    rounded="small"
    #Description
    placeholder="Description"
    [rows]="12"
    resizable="vertical"></kendo-textarea>
</div>

<div class="t-flex t-gap-4 t-justify-end">
  <button
    kendoButton
    class="v-custom-secondary-button !t-rounded-none t-self-end"
    themeColor="secondary"
    fillMode="outline"
    data-qa="upload-button">
    SAVE
  </button>

  <button
    kendoButton
    themeColor="dark"
    fillMode="outline"
    data-qa="cancel-button">
    CANCEL
  </button>
</div>
