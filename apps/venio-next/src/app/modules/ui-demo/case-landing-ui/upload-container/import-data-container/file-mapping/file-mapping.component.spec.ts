import { ComponentFixture, TestBed } from '@angular/core/testing'
import { FileMappingComponent } from './file-mapping.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('FileMappingComponent', () => {
  let component: FileMappingComponent
  let fixture: ComponentFixture<FileMappingComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [FileMappingComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(FileMappingComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
