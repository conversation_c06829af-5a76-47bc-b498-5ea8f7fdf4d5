<div class="t-flex t-flex-col t-flex-1">
  <div class="t-flex t-flex-col t-gap-5 t-flex-1">
    <div
      class="t-flex t-flex-row t-justify-between t-gap-5 t-border t-border-b-1 t-border-t-1 t-border-l-0 t-border-r-0 t-border-[#0000001A] t-border-solid t-py-[10px]">
      <div class="t-flex t-flex-row t-gap-4">
        <p class="t-font-medium t-text-[16px] t-text-[#000000DE] t-self-center">
          Load File
        </p>

        <kendo-dropdownlist
          [data]="sourceList"
          textField="text"
          valueField="value"
          title="Select Document"
          class="t-w-48"
          [defaultItem]="defaultItem"
          [popupSettings]="{ popupClass: 'v-hide-placeholder' }"
          [valuePrimitive]="true">
        </kendo-dropdownlist>
      </div>
    </div>

    <div class="t-flex t-flex-row t-gap-5">
      <div class="t-flex t-flex-col t-gap-5 t-flex-1">
        <p class="t-font-black t-font-bold t-text-[14px]">Import Fields</p>
        <div class="t-flex t-flex-row t-gap-2">
          <kendo-textbox
            placeholder="\import.csv"
            [readonly]="true"
            [maxlength]="6"
            type="text"
            class="t-w-72 t-max-w-full t-rounded v-input-shadow t-flex-1">
          </kendo-textbox>
          <button kendoButton class="t-p-0 t-px-2 t-m-0" fillMode="outline">
            <span
              venioSvgLoader
              applyEffectsTo="fill"
              hoverColor="#FFFFFF"
              color="#2D527C"
              svgUrl="assets/svg/icon-review-search-screen.svg"
              height="1rem"
              width="1rem"></span>
          </button>

          <button kendoButton class="t-p-0 t-px-2 t-m-0" fillMode="outline">
            <span
              venioSvgLoader
              applyEffectsTo="fill"
              hoverColor="#FFFFFF"
              color="#9AD3A6"
              svgUrl="assets/svg/icon-dots.svg"
              height="1rem"
              width="1rem"></span>
          </button>
        </div>

        <div class="t-flex t-flex-row t-gap-5">
          <kendo-dropdownlist
            [data]="sourceList"
            textField="text"
            valueField="value"
            title="Select Document"
            class="t-w-48"
            [defaultItem]="defaultItem"
            [popupSettings]="{ popupClass: 'v-hide-placeholder' }"
            [valuePrimitive]="true">
          </kendo-dropdownlist>

          <kendo-dropdownlist
            [data]="sourceList"
            textField="text"
            valueField="value"
            title="Select Document"
            class="t-w-48"
            [defaultItem]="defaultItem"
            [popupSettings]="{ popupClass: 'v-hide-placeholder' }"
            [valuePrimitive]="true">
          </kendo-dropdownlist>
        </div>

        <div class="t-flex t-flex-row t-gap-[48px]">
          <span class="t-font-black t-font-bold t-text-[14px]"
            >Number of Fields</span
          >
          <span>26</span>
        </div>

        <div class="t-flex t-flex-row t-gap-[15px]">
          <span class="t-font-black t-font-bold t-text-[14px]"
            >Number of Documents</span
          >
          <span>28</span>
        </div>
      </div>
      <div class="v-dashed-sperator t-opacity-100 t-w-[1px] t-h-auto"></div>
      <div class="t-flex t-flex-col t-gap-5 t-flex-1">
        <div class="t-flex t-flex-row t-gap-5">
          <p class="t-font-black t-font-bold t-text-[14px] t-self-center">
            Import Image
          </p>
          <div
            class="t-flex t-relative t-w-[192px] before:t-inset-0 before:t-absolute before:t-bg-[#ED7425] before:t-opacity-25 before:t-rounded-[4px] t-px-5 t-py-[4px] t-text-center t-text-wrap">
            <p class="t-relative t-text-[#000000] t-text-[13px]">
              No Image load file found
            </p>
          </div>
        </div>

        <div class="t-flex t-flex-row t-gap-2">
          <kendo-textbox
            placeholder="\import.csv"
            [readonly]="true"
            [maxlength]="6"
            type="text"
            class="t-w-72 t-max-w-full t-rounded v-input-shadow t-flex-1">
          </kendo-textbox>
          <button kendoButton class="t-p-0 t-px-2 t-m-0" fillMode="outline">
            <span
              venioSvgLoader
              applyEffectsTo="fill"
              hoverColor="#FFFFFF"
              color="#9AD3A6"
              svgUrl="assets/svg/icon-dots.svg"
              height="1rem"
              width="1rem"></span>
          </button>
        </div>
        <div class="t-flex t-flex-row t-gap-[48px]">
          <span class="t-font-black t-font-bold t-text-[14px]"
            >Number of Fields</span
          >
          <span>26</span>
        </div>

        <div class="t-flex t-flex-row t-gap-[15px]">
          <span class="t-font-black t-font-bold t-text-[14px]"
            >Number of Documents</span
          >
          <span>28</span>
        </div>
      </div>
    </div>
  </div>
  <div class="t-flex t-gap-4 t-justify-end">
    <button
      kendoButton
      class="v-custom-secondary-button !t-rounded-none t-self-end"
      themeColor="secondary"
      fillMode="outline"
      data-qa="upload-button">
      NEXT
    </button>
  </div>
</div>
