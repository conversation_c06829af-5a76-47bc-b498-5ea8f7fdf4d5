import { ComponentFixture, TestBed } from '@angular/core/testing'
import { GeneralSettingComponent } from './general-setting.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('GeneralSettingComponent', () => {
  let component: GeneralSettingComponent
  let fixture: ComponentFixture<GeneralSettingComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [GeneralSettingComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(GeneralSettingComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
