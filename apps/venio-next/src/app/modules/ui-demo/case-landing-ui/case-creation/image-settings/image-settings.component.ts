import { ChangeDetectionStrategy, Component } from '@angular/core'
import { CommonModule } from '@angular/common'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import {
  ButtonGroupSelection,
  ButtonsModule,
} from '@progress/kendo-angular-buttons'
import { DatePickerModule } from '@progress/kendo-angular-dateinputs'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { GridModule } from '@progress/kendo-angular-grid'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { KENDO_TREELIST } from '@progress/kendo-angular-treelist'
import { TreeViewModule } from '@progress/kendo-angular-treeview'
import {
  SVGIcon,
  boldIcon,
  italicIcon,
  underlineIcon,
} from '@progress/kendo-svg-icons'
import { FieldModel, FontStyle, StampLocation } from '@venio/data-access/review'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'

@Component({
  selector: 'venio-image-settings',
  standalone: true,
  imports: [
    FormsModule,
    CommonModule,
    LabelModule,
    DropDownListModule,
    InputsModule,
    ButtonsModule,
    TreeViewModule,
    GridModule,
    KENDO_TREELIST,
    ReactiveFormsModule,
    DatePickerModule,
    TreeViewModule,
    TooltipsModule,
    SvgLoaderDirective,
    IndicatorsModule,
  ],
  templateUrl: './image-settings.component.html',
  styleUrl: './image-settings.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ImageSettingsComponent {
  public gridData = [
    {
      fileType: 'Word Processor',
      pageLimit: 'Max Pages',
      colorConversion: 'Black & White',
      timeout: 'Default',
    },
    {
      fileType: 'Database',
      pageLimit: 'Max Pages',
      colorConversion: 'Black & White',
      timeout: 'Default',
    },
  ]

  public imageType = ['TIFF']

  public pageSize = ['1000']

  public pageLimitOptions = ['Max Pages', '10 Pages', '20 Pages']

  public colorOptions = ['Black & White', 'Color', 'Grayscale']

  public categories = [
    { label: 'Multimedia', checked: false },
    { label: 'Denist', checked: false },
    { label: 'System', checked: false },
    { label: 'Password Protected', checked: false },
    { label: 'Corrupted', checked: false },
    { label: 'Zero Bytes', checked: false },
    { label: 'Unknow', checked: false },
    { label: 'Unsupported', checked: false },
    { label: 'Processing Errored', checked: false },
    { label: 'File Extension', checked: false },
    { label: 'File Type', checked: false },
  ]

  public defaultFieldItem = 'Add Field'

  public slipsheetFields: FieldModel[] = []

  public boldSVG: SVGIcon = boldIcon

  public italicSVG: SVGIcon = italicIcon

  public underlineSVG: SVGIcon = underlineIcon

  public fontNames = []

  public fontStyles: (string | FontStyle)[] = []

  public fontSizes = [
    8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72,
  ]

  public selectionMode: ButtonGroupSelection = 'single'

  public stampLocationList = Object.values(StampLocation).filter(
    (x) => typeof x === 'string'
  )

  public expandedRowKeys: any[] = [1] // Expand Bitmap by default

  public engineOptions: string[] = [
    'Native Imaging Engine',
    'Non-Native Imaging Engine',
  ]

  public gridTreeviewData = [
    {
      category: 'Image File Type Settings',
      tree: [
        {
          id: 1,
          parentId: null,
          batchName: 'Bitmap',
        },
        {
          id: 2,
          parentId: 1,
          fileName: 'BMP',
          fileType: 'Bitmap',
          selectedEngine: 'Non-Native Imaging Engine',
        },
        {
          id: 3,
          parentId: 1,
          fileName: 'BMP5',
          fileType: 'Bitmap',
          selectedEngine: 'Non-Native Imaging Engine',
        },
        {
          id: 4,
          parentId: 1,
          fileName: 'OS2DIB',
          fileType: 'Bitmap',
          selectedEngine: 'Non-Native Imaging Engine',
        },
        {
          id: 5,
          parentId: 1,
          fileName: 'OS2V2BMP',
          fileType: 'Bitmap',
          selectedEngine: 'Non-Native Imaging Engine',
        },
        {
          id: 6,
          parentId: 1,
          fileName: 'WINDOWSICON',
          fileType: 'Bitmap',
          selectedEngine: 'Non-Native Imaging Engine',
        },
        {
          id: 7,
          parentId: null,
          batchName: 'Email',
        },
        {
          id: 8,
          parentId: 7,
          fileName: 'MSG',
          fileType: 'Email',
          selectedEngine: 'Native Imaging Engine',
        },
        {
          id: 9,
          parentId: null,
          batchName: 'Database',
        },

        {
          id: 10,
          parentId: null,
          batchName: 'Graphic',
        },
      ],
    },
  ]

  public imagePlugins = [
    { extension: 'BMP', plugin: 'Generic Imaging Engine' },
    { extension: 'PDF', plugin: 'Adobe PDF Imaging Engine' },
  ]
}
