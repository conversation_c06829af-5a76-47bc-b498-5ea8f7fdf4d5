import { ComponentFixture, TestBed } from '@angular/core/testing'
import { CaseCreationComponent } from './case-creation.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('CaseCreationComponent', () => {
  let component: CaseCreationComponent
  let fixture: ComponentFixture<CaseCreationComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CaseCreationComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(CaseCreationComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
