<div
  class="t-flex t-flex-col t-flex-1 t-gap-4 t-p-4 t-max-h-[56vh] t-overflow-y-auto">
  <div class="t-flex t-gap-2 t-flex-1">
    <div class="t-flex t-flex-col t-gap-2 t-w-[20%]">
      <p class="t-font-bold t-text-[14px] t-text-[#263238]">
        Native file Options
      </p>
      <kendo-dropdownlist
        class="t-w-[196px]"
        textField="name"
        valueField="vale"
        [valuePrimitive]="true">
      </kendo-dropdownlist>
    </div>

    <div class="t-flex t-flex-col t-gap-2 t-flex-1">
      <p class="t-font-bold t-text-[14px] t-text-[#263238] t-ml-[40px]">
        Filter System Type
      </p>
      <div class="t-flex t-gap-2">
        <div
          class="v-dashed-sperator t-mx-4 t-opacity-100 t-w-[2px] t-h-[85px]"></div>
        <div class="t-flex t-flex-col t-gap-1">
          <label class="t-bg-white t-flex t-items-center">
            <input
              type="checkbox"
              kendoCheckBox
              rounded="small"
              size="small"
              class="t-self-start" />
            <span
              class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
              >Exclude NIST List(DE-NIST)</span
            >
          </label>

          <label class="t-bg-white t-flex t-items-center">
            <input
              type="checkbox"
              kendoCheckBox
              rounded="small"
              size="small"
              class="t-self-start" />
            <span
              class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
              >Exclude File Types(Venio identifies and filters system files
              using file header)</span
            >
          </label>
        </div>
      </div>
    </div>

    <div class="t-flex t-flex-col t-gap-2 t-flex-1">
      <p class="t-font-bold t-text-[14px] t-text-[#263238] t-ml-[40px]">
        Filter Duplicate Files
      </p>
      <div class="t-flex t-gap-2">
        <div
          class="v-dashed-sperator t-mx-4 t-opacity-100 t-w-[2px] t-h-[85px]"></div>
        <div class="t-flex t-flex-col t-gap-1">
          <p class="t-font-medium t-text-[14px] t-text-[#263238]">
            Choose Hash Algorithm for deduplication
          </p>
          <kendo-dropdownlist
            class="t-w-[196px]"
            textField="name"
            valueField="vale"
            [valuePrimitive]="true">
          </kendo-dropdownlist>
        </div>
      </div>
    </div>
  </div>
  <div class="t-flex t-gap-[0.5rem] t-flex-1">
    <div class="t-flex t-flex-col t-gap-2 t-w-[20%]">
      <p class="t-font-bold t-text-[14px] t-text-[#263238]">
        Advance Hash Setting
      </p>
      <div class="t-flex">
        <kendo-grid
          class="t-pb-5"
          [kendoGridBinding]="advance_hash"
          venioDynamicHeight
          kendoGridSelectBy="id"
          [resizable]="true">
          <kendo-grid-checkbox-column
            [width]="30"
            [showSelectAll]="true"></kendo-grid-checkbox-column>
          <kendo-grid-column
            headerClass="t-text-primary"
            [filterable]="false"
            field="name"
            title="Has Fields"></kendo-grid-column>
        </kendo-grid>
      </div>
    </div>

    <div class="t-flex t-flex-col t-gap-2 t-flex-1">
      <div class="t-flex t-gap-5">
        <p class="t-font-bold t-text-[14px] t-text-[#263238] t-ml-[40px]">
          File Extension Filter
        </p>
        <p
          class="t-font-medium t-text-[10px] t-text-[#232323] t-relative t-top-[1px] t-self-center t-flex t-flex-1 t-text-wrap">
          <span class="t-font-normal t-text-[#ED7428]">NOTE: </span>Applicable
          at First Level file(s).
        </p>
      </div>
      <div class="t-flex t-gap-2">
        <div
          class="v-dashed-sperator t-mx-4 t-opacity-100 t-w-[2px] t-h-auto"></div>
        <div class="t-flex t-flex-col t-gap-2">
          <kendo-dropdownlist
            class="t-w-[196px]"
            textField="name"
            valueField="vale"
            [valuePrimitive]="true">
          </kendo-dropdownlist>

          <p class="t-font-normal t-text-[14px] t-text-[#263238]">
            Each extension(s) can be entered in separate line or can be entered
            in comma separated way
          </p>
          <kendo-textarea
            rounded="small"
            [rows]="4"
            resizable="vertical"></kendo-textarea>

          <p class="t-font-bold t-text-[14px] t-text-[#263238]">
            Advance Options
          </p>
          <kendo-dropdownlist
            class="t-w-[247px]"
            textField="name"
            valueField="vale"
            [valuePrimitive]="true">
          </kendo-dropdownlist>
        </div>
      </div>
    </div>
  </div>
  <div class="t-flex t-gap-2 t-flex-1">
    <div class="t-flex t-flex-col t-gap-2">
      <div class="t-flex t-gap-5">
        <p class="t-font-bold t-text-[14px] t-text-[#263238]">
          File Type Filter
        </p>
        <p
          class="t-font-medium t-text-[10px] t-text-[#232323] t-relative t-top-[1px] t-self-center t-flex t-flex-1 t-text-wrap">
          <span class="t-font-normal t-text-[#ED7428]">NOTE: </span>Applicable
          at First Level file(s).
        </p>
      </div>

      <kendo-dropdownlist
        class="t-w-[111px]"
        textField="name"
        valueField="vale"
        [valuePrimitive]="true">
      </kendo-dropdownlist>
      <div class="t-flex t-gap-4">
        <kendo-treelist
          class="t-w-[90%]"
          [kendoTreeListHierarchyBinding]="treeListView"
          kendoTreeListExpandable
          kendoTreeListSelectable
          childrenField="items"
          [selectable]="{ multiple: true, mode: 'row' }"
          editMode="incell"
          [filterable]="'menu'"
          (cellClick)="onCellClick($event)"
          (cellClose)="onCellClose($event)"
          (save)="onSave($event)">
          <ng-template
            kendoGridFilterMenuTemplate
            let-column="column"
            let-filter="filter"
            let-filterService="filterService">
          </ng-template>
          <kendo-treelist-checkbox-column
            [headerStyle]="{ left: '24px', position: 'relative' }"
            [expandable]="true"
            [width]="150"
            [showSelectAll]="true"
            [checkChildren]="true">
          </kendo-treelist-checkbox-column>

          <kendo-treelist-column
            headerClass="t-text-primary"
            field="fileType"
            title="File Type"></kendo-treelist-column>
          <kendo-treelist-column
            headerClass="t-text-primary"
            field="description"
            title="Description"></kendo-treelist-column>

          <kendo-treelist-column
            headerClass="t-text-primary"
            field="extension"
            title="Extensions">
            <ng-template
              kendoTreeListEditTemplate
              let-dataItem="dataItem"
              let-formGroup="formGroup">
              <input
                class="k-textbox"
                [formControl]="formGroup.get('extension')"
                (blur)="formGroup.markAsDirty()" />
            </ng-template>
          </kendo-treelist-column>
        </kendo-treelist>

        <div class="t-flex t-flex-col t-gap-3 t-w-[20%]">
          <p class="t-font-bold t-text-[14px] t-text-[#263238]">
            Advanced Options
          </p>
          <kendo-dropdownlist
            class="t-w-full"
            textField="name"
            valueField="vale"
            [valuePrimitive]="true">
          </kendo-dropdownlist>
        </div>
      </div>
    </div>
  </div>

  <div class="t-flex t-gap-2 t-flex-1">
    <div class="t-flex t-flex-col t-gap-2 t-flex-1">
      <p class="t-font-bold t-text-[14px] t-text-[#263238]">
        Date Filter Settings
      </p>
      <kendo-dropdownlist
        class="t-w-[130px]"
        textField="name"
        valueField="vale"
        [valuePrimitive]="true">
      </kendo-dropdownlist>

      <div class="t-flex t-gap-4">
        <kendo-dropdownlist
          class="t-w-[187px]"
          textField="name"
          valueField="vale"
          [valuePrimitive]="true">
        </kendo-dropdownlist>

        <kendo-dropdownlist
          class="t-w-[187px]"
          textField="name"
          valueField="vale"
          [valuePrimitive]="true">
        </kendo-dropdownlist>
      </div>

      <div class="t-flex t-gap-4">
        <kendo-datepicker
          class="t-w-[187px]"
          [format]="'M/d/yyyy'"
          data-qa="searchTextDate"></kendo-datepicker>

        <kendo-datepicker
          class="t-w-[187px]"
          [format]="'M/d/yyyy'"
          data-qa="searchTextDate"></kendo-datepicker>

        <button
          kendoButton
          class="!t-rounded-none"
          themeColor="primary"
          fillMode="outline">
          ADD
        </button>
      </div>

      <kendo-grid
        [kendoGridBinding]="filterList"
        [resizable]="true"
        class="t-pb-5"
        venioDynamicHeight>
        <!-- Date Filter Text Column -->
        <kendo-grid-column
          field="text"
          title="Date Filter Text"
          headerClass="t-text-primary">
        </kendo-grid-column>

        <!-- Action Column -->
        <kendo-grid-column
          title="Action"
          [width]="100"
          headerClass="t-text-primary">
          <ng-template kendoGridCellTemplate let-dataItem>
            <button
              kendoButton
              #actionGrid
              class="!t-p-[5px] t-w-[24px] !t-border !t-border-[#263238] !t-rounded-l-[2px] hover:!t-bg-[#263238]"
              kendoTooltip
              [title]="'Delete'"
              size="none">
              <span
                [parentElement]="actionGrid.element"
                venioSvgLoader
                [hoverColor]="'#fff'"
                [color]="'#979797'"
                [svgUrl]="'assets/svg/icon-material-delete-new.svg'"
                height="0.6rem"
                width="1rem"></span>
            </button>
          </ng-template>
        </kendo-grid-column>
      </kendo-grid>
    </div>
    <div class="t-flex t-flex-col t-gap-2 t-w-[40%]">
      <p class="t-font-bold t-text-[14px] t-text-[#263238] t-ml-[40px]">
        Embedded Item Setting
      </p>
      <div class="t-flex t-gap-2">
        <div
          class="v-dashed-sperator t-mx-4 t-opacity-100 t-w-[2px] t-h-auto"></div>
        <div class="t-flex t-flex-col t-gap-2">
          <p class="t-font-medium t-text-[14px] t-text-[#263238]">Email</p>

          <kendo-dropdownlist
            class="t-w-[247px]"
            textField="name"
            valueField="vale"
            [valuePrimitive]="true">
          </kendo-dropdownlist>

          <div class="t-flex t-flex-col t-gap-1">
            <label class="t-bg-white t-flex t-items-center">
              <input
                type="checkbox"
                kendoCheckBox
                rounded="medium"
                class="t-self-center custom-sm-checkbox" />
              <span
                class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
                >Exclude all non-recognizable embedded files</span
              >
            </label>

            <label class="t-bg-white t-flex t-items-center">
              <input
                type="checkbox"
                kendoCheckBox
                rounded="medium"
                class="t-self-center custom-sm-checkbox" />
              <span
                class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
                >Exclude all embedded images(based on file type)</span
              >
            </label>
          </div>
          <div
            class="v-dashed-horizontal-sperator t-opacity-100 t-w-[auto] t-h-[2px]"></div>
          <p class="t-font-medium t-text-[14px] t-text-[#263238]">Edoc</p>
          <kendo-dropdownlist
            class="t-w-[247px]"
            textField="name"
            valueField="vale"
            [valuePrimitive]="true">
          </kendo-dropdownlist>
          <div
            class="v-dashed-horizontal-sperator t-opacity-100 t-w-[auto] t-h-[2px]"></div>
          <p class="t-font-medium t-text-[14px] t-text-[#263238]">
            Power Point
          </p>
          <kendo-dropdownlist
            class="t-w-[247px]"
            textField="name"
            valueField="vale"
            [valuePrimitive]="true">
          </kendo-dropdownlist>
        </div>
      </div>
    </div>
  </div>
</div>
