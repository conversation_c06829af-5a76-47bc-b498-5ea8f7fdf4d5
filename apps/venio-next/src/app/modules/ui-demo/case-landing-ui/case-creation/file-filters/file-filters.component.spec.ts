import { ComponentFixture, TestBed } from '@angular/core/testing'
import { FileFiltersComponent } from './file-filters.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('FileFiltersComponent', () => {
  let component: FileFiltersComponent
  let fixture: ComponentFixture<FileFiltersComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [FileFiltersComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(FileFiltersComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
