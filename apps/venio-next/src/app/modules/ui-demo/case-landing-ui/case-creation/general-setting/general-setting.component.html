<div class="t-flex t-flex-1 t-gap-4 t-p-4 t-max-h-[56vh] t-overflow-y-auto">
  <div class="t-flex t-flex-col t-gap-4">
    <p class="t-font-bold t-text-[14px] t-text-[#263238]">Fulltext Options</p>
    <p
      class="t-font-normal t-text-[13px] t-text-[#263238] t-w-[340px] t-text-wrap">
      Default Fulltext Preference for (Indexing, Near Duplicates, Fulltext
      Viewer and Console Export)
    </p>

    <div
      class="t-flex t-flex-col t-border !t-border-t-1 t-border-b-0 t-border-r-0 t-border-l-0 t-border-[#DADADA] t-w-[340px]">
      <p
        class="t-mx-[30px] t-my-[8px] t-text-[#2F3080] t-font-medium t-text-[16px]">
        Fulltext Type
      </p>
      <kendo-sortable
        [data]="sortableItems"
        [kendoSortableBinding]="sortableItems"
        (dragStart)="onDragStart($event)"
        (dragOver)="onDragOver($event)"
        [navigable]="true"
        [animation]="true"
        class="t-flex t-flex-col t-w-full">
        <ng-template let-item="item" let-index="index">
          <div
            [ngClass]="{
              't-bg-[#F7F7F7]': index % 2 === 1,
              't-bg-white': index % 2 === 0
            }"
            class="t-flex t-items-center t-border t-p-1 t-border t-border-solid t-border-[#DADADA]">
            <!-- Drag handle button -->
            <button
              kendoButton
              fillMode="flat"
              size="small"
              class="t-cursor-move t-px-[10px] t-py-[1px]"
              (mousedown)="setDragInitiated(true)"
              (touchstart)="setDragInitiated(true)">
              <span
                venioSvgLoader
                svgUrl="assets/svg/icon-drag-drop.svg"
                height="1.2rem"
                width="1rem">
                <kendo-loader size="small"></kendo-loader>
              </span>
            </button>

            <!-- Item text -->
            <div class="t-text-[14px] t-text-[#5E6366] t-ml-[2px]">
              {{ item }}
            </div>
          </div>
        </ng-template>
      </kendo-sortable>
    </div>

    <div class="t-flex t-flex-col t-gap-1">
      <label class="t-bg-white t-flex t-items-center">
        <input
          type="checkbox"
          kendoCheckBox
          rounded="small"
          size="small"
          class="t-self-start" />
        <span
          class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
          >Automatically Image and OCR files without text (for Non- image
          files)</span
        >
      </label>

      <p
        class="t-pl-[25px] t-font-medium t-text-[#1DBADC] t-text-[12px] t-cursor-pointer">
        View & Select File Types to Image and OCR
      </p>
      <label class="t-bg-white t-flex t-items-center">
        <input
          type="checkbox"
          kendoCheckBox
          rounded="small"
          size="small"
          class="t-self-start" />
        <span
          class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
          >Extract Text from master view for Microsoft Office Powerpoint</span
        >
      </label>
    </div>

    <p class="t-font-bold t-text-[14px] t-text-[#263238]">
      Native File Options
    </p>
    <div class="t-flex t-flex-col t-gap-2">
      <p class="t-text-[#263238] t-font-normal t-text-[13px]">
        Select File type to replace native
      </p>
      <div class="t-flex t-flex-row t-gap-4">
        <div class="t-flex t-flex-col t-flex-1">
          <label class="text-text-[13px] t-text-[#263238] t-mb-[4px]"
            >For PST/MSG</label
          >
          <kendo-dropdownlist
            class="t-flex-1"
            textField="name"
            valueField="vale"
            [valuePrimitive]="true">
          </kendo-dropdownlist>
        </div>

        <div class="t-flex t-flex-col t-flex-1">
          <label class="text-text-[13px] t-text-[#263238] t-mb-[4px]"
            >For MBOX/EML</label
          >
          <kendo-dropdownlist
            class="t-flex-1"
            textField="name"
            valueField="vale"
            [valuePrimitive]="true">
          </kendo-dropdownlist>
        </div>

        <div class="t-flex t-flex-col t-flex-1">
          <label class="text-text-[13px] t-text-[#263238] t-mb-[4px]"
            >NFS/DXL</label
          >
          <kendo-dropdownlist
            class="t-flex-1"
            textField="name"
            valueField="vale"
            [valuePrimitive]="true">
          </kendo-dropdownlist>
        </div>
      </div>
    </div>

    <div class="t-flex t-flex-col t-gap-2">
      <p class="t-text-[#263238] t-font-normal t-text-[13px]">
        Create Html/Mht
      </p>
      <div class="t-flex t-flex-row t-gap-5">
        <div class="t-flex t-flex-col t-gap-1">
          <div class="t-flex t-items-start">
            <div>
              <input
                id="default-limit"
                type="radio"
                size="small"
                [value]="0"
                kendoRadioButton />
            </div>
            <div class="t-w-auto t-flex t-flex-wrap">
              <kendo-label
                class="t-ml-[5px] t-relative t-top-[2px]"
                text="Create HTML Files"></kendo-label>
            </div>
          </div>
          <div class="t-flex t-items-start">
            <div>
              <input
                id="default-limit"
                type="radio"
                size="small"
                [value]="0"
                kendoRadioButton />
            </div>
            <div class="t-w-auto t-flex t-flex-wrap">
              <kendo-label
                class="t-ml-[5px] t-relative t-top-[2px]"
                text="Create MHT Files"></kendo-label>
            </div>
          </div>
        </div>
        <label class="t-bg-white t-flex t-items-center">
          <input
            type="checkbox"
            kendoCheckBox
            rounded="small"
            size="small"
            class="t-self-start" />
          <span
            class="t-pl-2 t-text-[13px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
            >Preserve HTML Files</span
          >
        </label>
      </div>
    </div>

    <label class="t-bg-white t-flex t-items-center">
      <input
        type="checkbox"
        kendoCheckBox
        rounded="small"
        size="small"
        class="t-self-start" />
      <span
        class="t-pl-2 t-text-[13px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
        >Automatically Queue documents for generating MHT and/or RTF based on
        the selection above</span
      >
    </label>

    <p class="t-font-bold t-text-[14px] t-text-[#263238] t-mt-[55px]">
      Advance Settings
    </p>
    <div class="t-flex t-justify-between">
      <span class="t-text-[#263238] t-font-medium t-text-[13px]"
        >Tag Settings</span
      >
      <span class="t-cursor-pointer t-text-[#1DBADC] t-text-[12px]"
        >Customize Default System Tags</span
      >
    </div>

    <label class="t-bg-white t-flex t-items-center">
      <input
        type="checkbox"
        kendoCheckBox
        rounded="small"
        size="small"
        class="t-self-start" />
      <span
        class="t-pl-2 t-text-[13px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
        >Auto populate default system tags</span
      >
    </label>

    <div class="t-flex t-gap-1">
      <span class="t-text-[#263238] t-font-medium t-text-[13px]"
        >Hide Media Settings</span
      >
      <span
        class="t-cursor-pointer t-text-[#1DBADC] t-text-[10px] t-font-medium t-self-center"
        ><span class="t-text-[#ED7428] t-font-bold t-mr-1">NOTE :</span
        >Site-Admin and Project-Admin will be able to view all media(s)</span
      >
    </div>

    <label class="t-bg-white t-flex t-items-center">
      <input
        type="checkbox"
        kendoCheckBox
        rounded="small"
        size="small"
        class="t-self-start" />
      <span
        class="t-pl-2 t-text-[13px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
        >Hide newly ingested media for non-admin groups</span
      >
    </label>
  </div>

  <div class="t-flex t-flex-col t-gap-4 t-flex-1">
    <div class="t-flex t-flex-row t-gap-4 t-flex-1">
      <div class="t-flex t-flex-col t-gap-4 t-flex-1">
        <p class="t-font-bold t-text-[14px] t-text-[#263238] t-ml-[40px]">
          Fulltext Options
        </p>
        <div class="t-flex t-gap-2">
          <div
            class="v-dashed-sperator t-mx-4 t-opacity-100 t-w-[2px] t-h-auto"></div>
          <div class="t-flex t-flex-col t-gap-1">
            <label class="t-bg-white t-flex t-items-center">
              <input
                type="checkbox"
                kendoCheckBox
                rounded="small"
                size="small"
                class="t-self-start" />
              <span
                class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
                >Show email header in fulltext viewer</span
              >
            </label>

            <label class="t-bg-white t-flex t-items-center">
              <input
                type="checkbox"
                kendoCheckBox
                rounded="small"
                size="small"
                class="t-self-start" />
              <span
                class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
                >Show edoc metadata in fulltext viewer</span
              >
            </label>

            <label class="t-bg-white t-flex t-items-center">
              <input
                type="checkbox"
                kendoCheckBox
                rounded="small"
                size="small"
                class="t-self-start" />
              <span
                class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
                >If email body empty, view text file with email headers
                only</span
              >
            </label>
          </div>
        </div>

        <p class="t-font-bold t-text-[14px] t-text-[#263238] t-ml-[40px]">
          Track Change Option (Word document only)
        </p>

        <div class="t-flex t-gap-2">
          <div
            class="v-dashed-sperator t-mx-4 t-opacity-100 t-w-[2px] t-h-auto"></div>
          <div class="t-flex t-flex-col t-gap-1">
            <div class="t-flex t-items-start">
              <div>
                <input
                  id="default-limit"
                  type="radio"
                  size="small"
                  [value]="0"
                  kendoRadioButton />
              </div>
              <div class="t-w-auto t-flex t-flex-wrap">
                <kendo-label
                  class="t-ml-[5px] t-relative t-top-[2px]"
                  text="Include all track changes inline with extracted text"></kendo-label>
              </div>
            </div>
            <div class="t-flex t-items-start">
              <div>
                <input
                  id="default-limit"
                  type="radio"
                  size="small"
                  [value]="0"
                  kendoRadioButton />
              </div>
              <div class="t-w-auto t-flex t-flex-wrap">
                <kendo-label
                  class="t-ml-[5px] t-relative t-top-[2px]"
                  text="Append all track changes text at the end of extracted text (in hidden text section)"></kendo-label>
              </div>
            </div>
          </div>
        </div>

        <p class="t-font-bold t-text-[14px] t-text-[#263238] t-ml-[40px]">
          Discovery Exception Handling
        </p>

        <div class="t-flex t-gap-2">
          <div
            class="v-dashed-sperator t-mx-4 t-opacity-100 t-w-[2px] t-h-auto"></div>
          <kendo-dropdownlist
            id="template-select"
            textField="displayName"
            valueField="tzTimeZone"
            [valuePrimitive]="true"
            class="t-flex-2">
          </kendo-dropdownlist>
        </div>

        <p class="t-font-bold t-text-[14px] t-text-[#263238] t-ml-[40px]">
          Search Terms
        </p>

        <div class="t-flex t-gap-2 t-w-[98%]">
          <div
            class="v-dashed-sperator t-mx-4 t-opacity-100 t-w-[2px] t-h-auto"></div>
          <div class="t-flex t-flex-col t-gap-2">
            <p class="t-text-wrap t-text-[13px] t-text-[#263238]">
              Please list the search terms below, each terms in separate line.
              The searched document will be tagged with the respective tag.
            </p>

            <kendo-textarea
              rounded="small"
              [rows]="4"
              resizable="vertical"></kendo-textarea>
          </div>
        </div>

        <p class="t-font-bold t-text-[14px] t-text-[#263238] t-ml-[40px]">
          Passwords
        </p>
        <div class="t-flex t-gap-2">
          <div
            class="v-dashed-sperator t-mx-4 t-opacity-100 t-w-[2px] t-h-auto"></div>
          <div class="t-flex t-flex-col t-gap-2">
            <p class="t-text-wrap t-text-[13px] t-text-[#263238]">
              Please list passwords below, list each password on a separate
              line.
            </p>

            <kendo-textarea
              rounded="small"
              [rows]="4"
              resizable="vertical"></kendo-textarea>
          </div>
        </div>
      </div>

      <div class="t-flex t-flex-col t-gap-4 t-flex-1">
        <p class="t-font-bold t-text-[14px] t-text-[#263238] t-pl-[40px]">
          HTML Conversion Settings
        </p>
        <div class="t-flex t-gap-2">
          <div
            class="v-dashed-sperator t-mx-4 t-opacity-100 t-w-[2px] t-h-[222px]"></div>
          <div class="t-flex t-flex-col t-gap-2">
            <p class="t-font-normal t-text-[13px] t-text-[#263238]">
              Hidden Objects
            </p>

            <div class="t-flex t-gap-1 t-flex-col">
              <label class="t-bg-white t-flex t-items-center">
                <input
                  type="checkbox"
                  kendoCheckBox
                  rounded="small"
                  size="small"
                  class="t-self-start" />
                <span
                  class="t-pl-2 t-text-[13px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
                  >Show hidden rows/column/sheet</span
                >
              </label>

              <label class="t-bg-white t-flex t-items-center">
                <input
                  type="checkbox"
                  kendoCheckBox
                  rounded="small"
                  size="small"
                  class="t-self-start" />
                <span
                  class="t-pl-2 t-text-[13px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
                  >Show hidden text</span
                >
              </label>

              <label class="t-bg-white t-flex t-items-center">
                <input
                  type="checkbox"
                  kendoCheckBox
                  rounded="small"
                  size="small"
                  class="t-self-start" />
                <span
                  class="t-pl-2 t-text-[13px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
                  >Show track changes</span
                >
              </label>
            </div>

            <div class="t-flex t-gap-1 t-flex-col t-mt-2">
              <p class="t-font-normal t-text-[13px] t-text-[#263238]">
                Spreadsheet
              </p>

              <label class="t-bg-white t-flex t-items-center">
                <input
                  type="checkbox"
                  kendoCheckBox
                  rounded="small"
                  size="small"
                  class="t-self-start" />
                <span
                  class="t-pl-2 t-text-[13px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
                  >Show hidden rows/column/sheet</span
                >
              </label>
            </div>
          </div>
        </div>
        <p class="t-font-bold t-text-[14px] t-text-[#263238] t-pl-[40px]">
          Transcript Options
        </p>

        <div class="t-flex t-gap-2">
          <div
            class="v-dashed-sperator t-mx-4 t-opacity-100 t-w-[2px] t-h-[33px]"></div>
          <label class="t-bg-white t-flex t-items-center t-self-center">
            <input
              type="checkbox"
              kendoCheckBox
              rounded="small"
              size="small"
              class="t-self-start" />
            <span
              class="t-pl-2 t-text-[13px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
              >Enable Transcript</span
            >
          </label>
        </div>

        <p class="t-font-bold t-text-[14px] t-text-[#263238] t-pl-[40px]">
          Entity Extraction Option
        </p>
        <div class="t-flex t-gap-2">
          <div
            class="v-dashed-sperator t-mx-4 t-opacity-100 t-w-[2px] t-h-[270px]"></div>
          <label class="t-bg-white t-flex t-items-center t-self-start">
            <input
              type="checkbox"
              kendoCheckBox
              rounded="small"
              size="small"
              class="t-self-start" />
            <span
              class="t-pl-2 t-text-[13px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
              >Auto queue ingested files for entity extraction</span
            >
          </label>
        </div>
      </div>
    </div>

    <div class="t-flex t-flex-col t-gap-4 t-flex-1">
      <p class="t-font-bold t-text-[14px] t-text-[#263238] t-ml-[40px]">
        Redaction Settings
      </p>
      <div class="t-flex t-gap-2">
        <div
          class="v-dashed-sperator t-mx-4 t-opacity-100 t-w-[2px] t-h-auto"></div>
        <div class="t-flex t-flex-col t-gap-2">
          <label class="t-bg-white t-flex t-items-center">
            <input
              type="checkbox"
              kendoCheckBox
              rounded="small"
              size="small"
              class="t-self-start" />
            <span
              class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
              >Create default redaction sets</span
            >
          </label>

          <div class="t-flex t-flex-row t-gap-1">
            <div class="t-flex t-gap-1">
              <span class="t-w-[20px] t-h-[15px] t-bg-[#EAFD8A]"></span>
              <span class="t-text-[13px] t-text-[#263238]">Highlight</span>
            </div>
            <div class="t-flex t-gap-1">
              <span class="t-w-[20px] t-h-[15px] t-bg-[#000000]"></span>
              <span class="t-text-[13px] t-text-[#263238]">Redaction</span>
            </div>

            <div class="t-flex t-gap-1">
              <span class="t-w-[20px] t-h-[15px] t-bg-[#AAACAD]"></span>
              <span class="t-text-[13px] t-text-[#263238]"
                >Whiteout redaction sets will be created in this project.</span
              >
            </div>
          </div>

          <p class="t-font-medium t-text-[13px] t-text-[#263238]">
            Export Settings
          </p>

          <label class="t-bg-white t-flex t-items-center">
            <input
              type="checkbox"
              kendoCheckBox
              rounded="small"
              size="small"
              class="t-self-start" />
            <span
              class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
              >Show warning in export if files with ingestion error were not
              reprocessed</span
            >
          </label>

          <p class="t-font-medium t-text-[13px] t-text-[#263238]">
            Other Settings
          </p>

          <kendo-dropdownlist
            id="template-select"
            textField="displayName"
            valueField="tzTimeZone"
            [valuePrimitive]="true"
            class="t-flex-2">
          </kendo-dropdownlist>
        </div>
      </div>
    </div>
  </div>
</div>
