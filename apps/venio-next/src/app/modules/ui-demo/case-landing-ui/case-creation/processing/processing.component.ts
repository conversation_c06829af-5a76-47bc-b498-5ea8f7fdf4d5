import { ChangeDetectionStrategy, Component } from '@angular/core'
import { CommonModule } from '@angular/common'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import {
  DateInputsModule,
  DatePickerModule,
} from '@progress/kendo-angular-dateinputs'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { GridModule } from '@progress/kendo-angular-grid'
import { IconsModule } from '@progress/kendo-angular-icons'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { SortableModule } from '@progress/kendo-angular-sortable'
import { TooltipDirective } from '@progress/kendo-angular-tooltip'
import { KENDO_TREELIST } from '@progress/kendo-angular-treelist'
import { TreeViewModule } from '@progress/kendo-angular-treeview'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'

@Component({
  selector: 'venio-processing',
  standalone: true,
  imports: [
    FormsModule,
    CommonModule,
    LabelModule,
    DropDownListModule,
    InputsModule,
    SortableModule,
    SvgLoaderDirective,
    IconsModule,
    TooltipDirective,
    IndicatorsModule,
    ButtonsModule,
    TreeViewModule,
    ReactiveFormsModule,
    GridModule,
    DateInputsModule,
    DatePickerModule,
    KENDO_TREELIST,
  ],
  templateUrl: './processing.component.html',
  styleUrl: './processing.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ProcessingComponent {
  public options = [
    { text: 'Operations', isHeader: true },
    { text: 'Child Extraction', value: 'Child Extraction' },
    { text: 'File Type Identification', value: 'File Type Identification' },
    { text: 'Fulltext Extraction', value: 'Fulltext Extraction' },
    { text: 'Meta Extraction', value: 'Meta Extraction' },
    { text: 'Set Priority', isHeader: true },
    {
      text: 'File Type Identification Priority',
      value: 'File Type Identification Priority',
    },
  ]

  public selectedSetting = 'Advanced Ingesting Setting'

  public settingsOptions = [
    { name: 'API Priority Settings', value: 'API Priority Settings' },
    { name: 'Social Media Settings', value: 'Social Media Settings' },
    { name: 'Advanced Ingesting Setting', value: 'Advanced Ingesting Setting' },
  ]

  public isItemDisabled(itemArgs: { dataItem: any }): boolean {
    return <boolean>itemArgs.dataItem.isHeader
  }

  public selectedKeys: string[] = []

  public repoGridData = [
    {
      id: 1,
      file_name: 'Absolute File Path',
    },
    {
      id: 2,
      file_name: 'Channel',
    },
    {
      id: 3,
      file_name: 'Document Type',
    },
    {
      id: 4,
      file_name: 'Edoc Author',
    },
  ]

  public treeListView1: any[] = [
    {
      id: 1,
      fileType: '7Z',
      extraction: '',
      priorty: '',
      items: [
        {
          id: 11,
          fileType: 'Child Extraction',
          extraction: 'Veniosocialmedia...',
          priority: 1,
        },
        {
          id: 12,
          fileType: 'Child Extraction',
          extraction: 'Veniosocialmedia...',
          priority: 4,
        },
      ],
    },

    {
      id: 2,
      fileType: '8Z',
      extraction: '',
      priorty: '',
      items: [
        {
          id: 21,
          fileType: 'Child Extraction',
          extraction: 'Veniosocialmedia...',
          priority: 1,
        },
        {
          id: 22,
          fileType: 'Child Extraction',
          extraction: 'Veniosocialmedia...',
          priority: 4,
        },
      ],
    },

    {
      id: 3,
      fileType: '1Z',
      extraction: '',
      priorty: '',
      items: [
        {
          id: 31,
          fileType: 'Child Extraction',
          extraction: 'Veniosocialmedia...',
          priority: 1,
        },
        {
          id: 32,
          fileType: 'Child Extraction',
          extraction: 'Veniosocialmedia...',
          priority: 4,
        },
      ],
    },
  ]

  public job_setting_grid_data = [
    { id: 1, name: 'TIFF' },
    { id: 2, name: 'OCR' },
  ]

  public stop_word_grid = [
    { id: 1, name: 'Path' },
    { id: 2, name: 'None' },
    { id: 3, name: 'Some' },
    { id: 4, name: 'Whatever' },
  ]
}
