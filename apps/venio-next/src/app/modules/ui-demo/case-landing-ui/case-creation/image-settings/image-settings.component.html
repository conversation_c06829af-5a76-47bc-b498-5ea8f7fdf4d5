<div
  class="t-flex t-flex-col t-flex-1 t-gap-4 t-p-4 t-max-h-[56vh] t-overflow-y-auto">
  <div class="t-flex t-flex-1 t-gap-4">
    <div class="t-flex t-gap-4 t-flex-col t-flex-1">
      <p class="t-font-bold t-text-[14px] t-text-[#263238]">
        Image Conversion Options
      </p>
      <label class="t-bg-white t-flex t-items-center">
        <input
          type="checkbox"
          kendoCheckBox
          rounded="small"
          size="small"
          class="t-self-start" />
        <span
          class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
          >Enable Image</span
        >
      </label>
      <div class="t-flex t-gap-3">
        <kendo-dropdownlist
          class="t-w-[130px]"
          [data]="imageType"
          [value]="'TIFF'">
        </kendo-dropdownlist>
        <kendo-dropdownlist
          class="t-w-[130px]"
          [data]="pageSize"
          [value]="'1000'">
        </kendo-dropdownlist>
        <kendo-dropdownlist
          class="t-w-[160px]"
          [data]="pageLimitOptions"
          [value]="'Black & White'">
        </kendo-dropdownlist>
        <kendo-numerictextbox class="t-w-[95px]"></kendo-numerictextbox>
      </div>
      <kendo-grid [data]="gridData">
        <kendo-grid-column field="fileType" title="File Type Class">
        </kendo-grid-column>

        <kendo-grid-column field="pageLimit" title="Page Limit">
          <ng-template kendoGridCellTemplate let-dataItem>
            <kendo-dropdownlist
              [data]="pageLimitOptions"
              [value]="dataItem.pageLimit">
            </kendo-dropdownlist>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column field="colorConversion" title="Color Conversion">
          <ng-template kendoGridCellTemplate let-dataItem>
            <kendo-dropdownlist
              [data]="colorOptions"
              [value]="dataItem.colorConversion">
            </kendo-dropdownlist>
          </ng-template>
        </kendo-grid-column>

        <kendo-grid-column field="timeout" title="Timeout (In Mins)">
          <ng-template kendoGridCellTemplate let-dataItem>
            <kendo-textbox
              placeholder="Default"
              [value]="dataItem.timeout"
              class="!t-border-[#ccc]"
              [disabled]="true"></kendo-textbox>
          </ng-template>
        </kendo-grid-column>
      </kendo-grid>
    </div>

    <div class="t-flex t-gap-4 t-flex-col t-flex-1">
      <p class="t-font-bold t-text-[14px] t-text-[#263238] t-ml-[40px]">
        Advanced Image Option
      </p>
      <div class="t-flex t-gap-2">
        <div
          class="v-dashed-sperator t-mx-4 t-opacity-100 t-w-[2px] t-h-auto"></div>
        <div class="t-flex t-flex-col t-gap-2">
          <p class="t-font-normal t-text-[14px] t-text-[#263238]">
            Apply System Bates Number Option
          </p>
          <label class="t-bg-white t-flex t-items-center">
            <input
              type="checkbox"
              kendoCheckBox
              rounded="small"
              size="small"
              class="t-self-start" />
            <span
              class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
              >Generate bates number for generated images</span
            >
          </label>

          <div class="t-flex t-flex-col t-gap-2 t-ml-4">
            <div class="t-flex t-gap-3">
              <kendo-dropdownlist
                class="t-w-[140px]"
                [data]="pageLimitOptions"
                [value]="'Black & White'">
              </kendo-dropdownlist>
              <kendo-textbox
                placeholder="IMG"
                class="!t-border-[#ccc] t-w-[140px]"
                [disabled]="true"></kendo-textbox>
            </div>
            <div class="t-flex t-gap-3">
              <kendo-numerictextbox
                placeholder="Start Number"
                class="t-w-[140px]"></kendo-numerictextbox>
              <kendo-numerictextbox
                placeholder="Padding"
                class="t-w-[140px]"></kendo-numerictextbox>
            </div>
            <div class="t-flex t-items-start">
              <div>
                <input
                  id="default-limit"
                  type="radio"
                  size="small"
                  [value]="0"
                  [checked]="true"
                  kendoRadioButton />
              </div>
              <div class="t-w-auto t-flex t-flex-wrap">
                <kendo-label
                  class="t-ml-[5px] t-relative t-top-[2px] t-text-[#2F3080] t-font-bold"
                  text="Generates Image without branding bates number"></kendo-label>
              </div>
            </div>

            <div class="t-flex t-items-start">
              <div>
                <input
                  id="default-limit"
                  type="radio"
                  size="small"
                  [value]="1"
                  kendoRadioButton />
              </div>
              <div class="t-w-auto t-flex t-flex-wrap">
                <kendo-label
                  class="t-ml-[5px] t-relative t-top-[2px]"
                  text="Generates Image with branding bates number"></kendo-label>
              </div>
            </div>
          </div>
          <label class="t-bg-white t-flex t-items-center">
            <input
              type="checkbox"
              kendoCheckBox
              rounded="medium"
              class="t-self-center custom-sm-checkbox" />
            <span class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap"
              >Auto generate images for ingested files</span
            >
          </label>

          <label class="t-bg-white t-flex t-items-center">
            <input
              type="checkbox"
              kendoCheckBox
              rounded="medium"
              class="t-self-center custom-sm-checkbox" />
            <span class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap"
              >Notify me after image generation is complete</span
            >
          </label>
        </div>
      </div>
    </div>
  </div>

  <div class="t-flex t-flex-1 t-gap-4">
    <div class="t-flex t-gap-2 t-flex-col t-flex-1">
      <div class="t-flex t-gap-5">
        <p class="t-font-bold t-text-[14px] t-text-[#263238]">
          Image File Type Settings
        </p>
        <p
          class="t-font-medium t-text-[10px] t-text-[#232323] t-self-center t-flex t-flex-1 t-text-wrap t-relative t-top-[1px]">
          <span class="t-font-normal t-text-[#ED7428]">NOTE: </span>Password
          protected and decrypted pdf documents are always imaged using
          non-native engine.
        </p>
      </div>
      <kendo-grid
        class="!t-border-transparent !t-border-0"
        [kendoGridBinding]="gridTreeviewData"
        [scrollable]="'none'"
        venioDynamicHeight
        [filterable]="'menu'">
        <kendo-grid-column title="File Type Name" [width]="200">
          <ng-template
            kendoGridFilterMenuTemplate
            let-column="column"
            let-filter="filter"
            let-filterService="filterService">
          </ng-template>
          <ng-template kendoGridCellTemplate let-dataItem>
            <kendo-treeview
              [(expandedKeys)]="expandedRowKeys"
              [nodes]="dataItem.tree"
              kendoTreeViewFlatDataBinding
              idField="id"
              parentIdField="parentId"
              textField="batchName"
              kendoTreeViewExpandable
              class="v-hide-scrollbar custom-treeview-template"
              [navigable]="false"
              checkBy="id">
              <ng-template kendoTreeViewNodeTemplate let-node>
                <div class="t-flex t-items-start t-gap-2 t-w-full">
                  @if (!node.fileName) {
                  <span class="t-font-semibold">
                    File Type Group: {{ node.batchName }}
                  </span>
                  } @else {
                  <div class="t-flex t-items-start t-justify-between t-w-full">
                    <p class="t-font-medium">{{ node.fileName }}</p>

                    <kendo-dropdownlist
                      class="t-w-72"
                      [data]="engineOptions"
                      [(ngModel)]="node.selectedEngine"
                      [defaultItem]="'Select Engine'"></kendo-dropdownlist>
                  </div>
                  }
                </div>
              </ng-template>
            </kendo-treeview>
          </ng-template>
        </kendo-grid-column>
      </kendo-grid>
    </div>
    <div class="t-flex t-gap-2 t-flex-col t-flex-1">
      <p class="t-font-bold t-text-[14px] t-text-[#263238] t-ml-[40px]">
        Image File Extension Setting
      </p>
      <div class="t-flex t-gap-2 t-flex-1">
        <div
          class="v-dashed-sperator t-mx-4 t-opacity-100 !t-w-[2px] t-h-auto"></div>
        <div class="t-flex t-flex-col t-gap-2 t-w-[90%]">
          <label class="t-bg-white t-flex t-items-center">
            <input
              type="checkbox"
              kendoCheckBox
              rounded="small"
              size="small"
              class="t-self-start" />
            <span
              class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
              >Override the filetype assignment of imaging engine conflict
              between filetypes & extension</span
            >
          </label>

          <div class="t-flex t-flex-col t-ml-4 t-gap-2">
            <div class="t-flex t-gap-2">
              <kendo-textbox
                class="t-w-[245px] !t-border-[#ccc]"
                placeholder="File Extension"></kendo-textbox>
              <kendo-dropdownlist
                class="t-w-[245px]"
                textField="name"
                valueField="value"
                [valuePrimitive]="true">
              </kendo-dropdownlist>
              <button
                kendoButton
                class="!t-rounded-none t-self-end"
                themeColor="primary"
                fillMode="outline"
                data-qa="upload-button">
                ADD
              </button>
            </div>

            <kendo-grid [data]="imagePlugins">
              <kendo-grid-column
                headerClass="t-text-primary"
                field="extension"
                title="Extension"></kendo-grid-column>

              <kendo-grid-column
                headerClass="t-text-primary"
                field="plugin"
                title="Image Plugin"></kendo-grid-column>

              <kendo-grid-column
                headerClass="t-text-primary"
                title="Action"
                [width]="100">
                <ng-template kendoGridCellTemplate let-dataItem>
                  <button
                    kendoButton
                    #actionGrid
                    class="!t-p-[5px] t-w-[24px] !t-border !t-border-[#263238] !t-rounded-l-[2px] hover:!t-bg-[#263238]"
                    kendoTooltip
                    [title]="'Delete'"
                    size="none">
                    <span
                      [parentElement]="actionGrid.element"
                      venioSvgLoader
                      [hoverColor]="'#fff'"
                      [color]="'#979797'"
                      [svgUrl]="'assets/svg/icon-material-delete-new.svg'"
                      height="0.6rem"
                      width="1rem"></span>
                  </button>
                </ng-template>
              </kendo-grid-column>
            </kendo-grid>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="t-flex t-flex-1 t-gap-4">
    <div class="t-flex t-gap-4 t-flex-col t-flex-1">
      <p class="t-font-bold t-text-[14px] t-text-[#263238]">OCR Setting</p>
      <label class="t-bg-white t-flex t-items-center">
        <input
          type="checkbox"
          kendoCheckBox
          rounded="small"
          size="small"
          class="t-self-start" />
        <span
          class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
          >Enable OCR</span
        >
      </label>
      <kendo-grid
        [kendoGridBinding]="categories"
        [scrollable]="'none'"
        [resizable]="true">
        <kendo-grid-checkbox-column
          [width]="30"
          [showSelectAll]="true"></kendo-grid-checkbox-column>
        <kendo-grid-column
          headerClass="t-text-primary"
          [filterable]="false"
          field="label"
          title="File Type"></kendo-grid-column>
      </kendo-grid>
    </div>

    <div class="t-flex t-flex-col t-gap-2 t-flex-1">
      <p class="t-font-bold t-text-[14px] t-text-[#263238] t-ml-[40px]">
        Advanced OCR Option
      </p>
      <div class="t-flex t-gap-2">
        <div
          class="v-dashed-sperator t-mx-4 t-opacity-100 t-w-[2px] t-h-auto"></div>
        <div class="t-flex t-flex-col t-gap-2">
          <p class="t-font-medium t-text-[14px] t-text-[#263238]">Email</p>
          <label class="t-bg-white t-flex t-items-center">
            <input
              type="checkbox"
              kendoCheckBox
              rounded="small"
              size="small"
              class="t-self-start" />
            <span
              class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
              >Enable OCR for PDF document with fewer text</span
            >
          </label>
          <div class="t-flex t-flex-col t-gap-2 t-ml-3">
            <div class="t-flex t-items-start">
              <div>
                <input
                  id="default-limit"
                  type="radio"
                  size="small"
                  [value]="0"
                  kendoRadioButton />
              </div>
              <div class="t-w-auto t-flex t-flex-wrap">
                <kendo-label
                  class="t-ml-[5px] t-relative t-top-[2px]"
                  text="OCR PDF document with average character per page less than"></kendo-label>
              </div>
            </div>
            <kendo-textbox
              placeholder="20"
              class="!t-border-[#ccc] !t-w-[86px]"></kendo-textbox>

            <div class="t-flex t-items-start">
              <div>
                <input
                  id="default-limit"
                  type="radio"
                  size="small"
                  [value]="0"
                  kendoRadioButton />
              </div>
              <div class="t-w-auto t-flex t-flex-wrap">
                <kendo-label
                  class="t-ml-[5px] t-relative t-top-[2px]"
                  text="OCR PDF document if at least one page has character less than"></kendo-label>
              </div>
            </div>
            <kendo-textbox
              placeholder="10"
              class="!t-border-[#ccc] !t-w-[86px]"></kendo-textbox>
          </div>
          <div
            class="v-dashed-horizontal-sperator t-opacity-100 t-w-[auto] t-h-[0.1em]"></div>
          <p class="t-font-medium t-text-[14px] t-text-[#263238]">Language</p>
          <p class="t-font-normal t-text-[12px] t-text-[#263238]">
            This setting will be only applicable for Nuance OCR Engine
          </p>
          <kendo-dropdownlist
            class="t-w-[245px]"
            textField="name"
            valueField="value"
            [valuePrimitive]="true">
          </kendo-dropdownlist>
          <div
            class="v-dashed-horizontal-sperator t-opacity-100 t-w-[auto] t-h-[0.1em]"></div>
          <p class="t-font-medium t-text-[14px] t-text-[#263238]">Others</p>
          <p class="t-font-normal t-text-[14px] t-text-[#263238]">
            Numbers of time to retry when failed
          </p>
          <kendo-numerictextbox class="t-w-[245px]"></kendo-numerictextbox>
        </div>
      </div>
    </div>
  </div>

  <div class="t-flex t-flex t-flex-col t-gap-4">
    <div class="t-flex t-gap-4 t-flex-col t-flex-1">
      <p class="t-font-bold t-text-[14px] t-text-[#263238]">Slipsheet</p>
      <label class="t-bg-white t-flex t-items-center">
        <input
          type="checkbox"
          kendoCheckBox
          rounded="small"
          size="small"
          class="t-self-start" />
        <span
          class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
          >Generate slipsheet for following file types. (Slipsheets are
          generated when the files are imaged)</span
        >
      </label>
    </div>
    <div class="t-flex t-gap-4 t-flex-1">
      <kendo-grid
        class="t-w-[25%]"
        [kendoGridBinding]="categories"
        [scrollable]="'none'"
        [hideHeader]="true"
        [resizable]="true">
        <kendo-grid-checkbox-column
          [width]="30"
          [showSelectAll]="false"></kendo-grid-checkbox-column>
        <kendo-grid-column
          headerClass="t-text-primary"
          [filterable]="false"
          field="label"
          title="File Type"></kendo-grid-column>
      </kendo-grid>

      <div class="t-flex t-flex-col t-gap-2 t-flex-1">
        <div class="t-flex t-flex-row t-w-[80%]">
          <div class="t-w-4 t-py-2">
            <input
              size="small"
              type="radio"
              [value]="'0'"
              id="rdPlaceholderTemplate"
              data-qa="slipsheet-option-load-from-template"
              kendoRadioButton />
          </div>
          <div class="t-w-auto t-px-1.5 t-py-2">
            <kendo-label
              for="rdPlaceholderTemplate"
              class="t-k-radio-label t-px-[5px] t-relative t-top-[2px] t-flex t-flex-wrap"
              text="Load from a template">
            </kendo-label>
          </div>
          <!-- template selection dropdown-->
          <div class="t-w-1/5 t-mt-[2px]">
            <kendo-dropdownlist
              id="slipsheetTemplateId"
              textField="slipsheetTemplateName"
              valueField="slipsheetTemplateId">
            </kendo-dropdownlist>
          </div>
        </div>

        <div class="t-flex t-flex-row t-w-[80%]">
          <div class="t-w-4">
            <input
              size="small"
              type="radio"
              [value]="'1'"
              id="rdPlaceholderText"
              data-qa="slipsheet-option-create-from-text"
              kendoRadioButton />
          </div>
          <div class="t-w-1/2 t-px-1.5">
            <kendo-label
              for="rdPlaceholderText"
              class="t-k-radio-label t-px-[5px] t-relative t-top-[2px] t-flex t-flex-wrap"
              text="Text">
            </kendo-label>
          </div>
        </div>
        <div class="t-w-full t-flex t-flex-row">
          <!-- Slipsheet text area div-->
          <div
            class="t-bg-white t-bg-no-repeat t-border-[1px] t-border-solid t-border-[#BEBEBE] t-rounded-[4px] t-opacity-100 t-p-3 t-flex t-flex-col t-w-full">
            <div class="t-px-2 t-flex t-flex-row t-gap-x-2">
              <div class="t-flex t-flex-row t-gap-x-2 t-w-[310px]">
                <kendo-dropdownlist
                  class="t-w-1/2"
                  id="fontName"
                  [data]="fontNames"
                  data-qa="slipsheet-font-name-dropdown"></kendo-dropdownlist>

                <kendo-dropdownlist
                  class="t-w-1/4"
                  id="fontSize"
                  [data]="fontSizes"
                  data-qa="slipsheet-font-size-dropdown"></kendo-dropdownlist>

                <kendo-buttongroup [selection]="selectionMode">
                  <button
                    kendoButton
                    [svgIcon]="boldSVG"
                    title="Bold"
                    [value]="'Bold'"></button>
                  <button
                    kendoButton
                    [svgIcon]="italicSVG"
                    title="Italic"
                    [value]="'Italic'"></button>
                </kendo-buttongroup>
              </div>

              <kendo-dropdownlist
                class="t-w-[120px]"
                id="placeHolderPosition"
                [data]="stampLocationList">
              </kendo-dropdownlist>
              <kendo-dropdownlist
                id="slipsheetField"
                [data]="slipsheetFields"
                [defaultItem]="defaultFieldItem"
                class="t-w-1/5"
                data-qa="slipsheet-field-dropdownlist"></kendo-dropdownlist>
            </div>
            <hr class="t-my-2 t-border-[#0000001F]" />

            <div>
              <kendo-textarea
                [rows]="3"
                class="textarea-min-height t-border-0 t-border-transparent"
                id="placeHolderText"
                resizable="none"></kendo-textarea>
            </div>
          </div>
        </div>
        <!-- Slipsheet image file upload control -->
        <div class="t-flex t-flex-row t-w-1/5 t-gap-2 t-w-[80%]">
          <div class="t-w-4">
            <input
              size="small"
              type="radio"
              [value]="'3'"
              id="rdPlaceholderTiffFile"
              data-qa="slipsheet-option-create-from-image"
              kendoRadioButton />
          </div>
          <div class="t-pr-2">
            <kendo-label
              for="rdPlaceholderTiffFile"
              class="t-k-radio-label t-pl-[2px] t-relative t-top-[2px] t-flex t-flex-wrap"
              text="Select a Slipsheet tiff file">
            </kendo-label>
          </div>
        </div>
        <div class="v-custom-upload-container t-mt-3">
          <div
            class="v-upload-area t-flex t-flex-col t-border-2 t-border-dashed t-border-[#1EBADC] t-p-3 t-min-h-[64px] t-w-[330px] t-flex t-items-center t-justify-center t-text-center t-cursor-pointer">
            <p class="t-text-sm t-font-noraml t-text-[#5E6366]">
              Drag & Drop file or Click
              <span class="t-font-bold t-text-[#1EBADC] t-cursor-pointer">
                here
              </span>
              to upload the file
            </p>
          </div>

          <input type="file" hidden />
        </div>
      </div>
    </div>
    <div
      class="v-dashed-horizontal-sperator t-opacity-100 t-w-[auto] t-h-[0.1em]"></div>
    <div class="t-flex t-flex-col t-gap-1">
      <div class="t-flex t-gap-2">
        <label class="t-bg-white t-flex t-flex-1 t-items-center">
          <input
            type="checkbox"
            kendoCheckBox
            rounded="medium"
            class="t-self-center custom-sm-checkbox" />
          <span
            class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
            >If slipsheet created for document, create/replace extracted
            fulltext with slipsheet</span
          >
        </label>

        <label class="t-bg-white t-flex t-flex-1 t-items-center">
          <input
            type="checkbox"
            kendoCheckBox
            rounded="medium"
            class="t-self-center custom-sm-checkbox" />
          <span
            class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
            >Delete Image OCR and Redacted Image OCR files when slipsheets are
            generated</span
          >
        </label>
      </div>
      <label class="t-bg-white t-flex t-items-center">
        <input
          type="checkbox"
          kendoCheckBox
          rounded="medium"
          class="t-self-center custom-sm-checkbox" />
        <span
          class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
          >Generate slipsheet at the first page when less than available pages
          of a document is imaged</span
        >
      </label>
      <label class="t-bg-white t-flex t-items-center">
        <input
          type="checkbox"
          kendoCheckBox
          rounded="small"
          size="small"
          class="t-self-start" />
        <span
          class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
          >Generate slipsheet when imaging timed out</span
        >
      </label>
    </div>
  </div>
</div>
