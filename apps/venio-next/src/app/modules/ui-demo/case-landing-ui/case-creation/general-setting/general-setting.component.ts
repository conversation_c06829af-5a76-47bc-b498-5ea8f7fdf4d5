import { ChangeDetectionStrategy, Component, ViewChild } from '@angular/core'
import { CommonModule } from '@angular/common'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import {
  DateInputsModule,
  DatePickerModule,
} from '@progress/kendo-angular-dateinputs'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { GridModule } from '@progress/kendo-angular-grid'
import { IconsModule } from '@progress/kendo-angular-icons'
import { IndicatorsModule } from '@progress/kendo-angular-indicators'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import {
  DragOverEvent,
  DragStartEvent,
  SortableComponent,
  SortableModule,
} from '@progress/kendo-angular-sortable'
import { TooltipDirective } from '@progress/kendo-angular-tooltip'
import { KENDO_TREELIST } from '@progress/kendo-angular-treelist'
import { TreeViewModule } from '@progress/kendo-angular-treeview'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'

@Component({
  selector: 'venio-general-setting',
  standalone: true,
  imports: [
    FormsModule,
    CommonModule,
    LabelModule,
    DropDownListModule,
    InputsModule,
    SortableModule,
    SvgLoaderDirective,
    IconsModule,
    TooltipDirective,
    IndicatorsModule,
    ButtonsModule,
    TreeViewModule,
    ReactiveFormsModule,
    GridModule,
    DateInputsModule,
    DatePickerModule,
    KENDO_TREELIST,
  ],
  templateUrl: './general-setting.component.html',
  styleUrl: './general-setting.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GeneralSettingComponent {
  @ViewChild(SortableComponent) public sortable: SortableComponent

  private dragInitiated = false

  public sortableItems = [
    'Redacted OCR Text',
    'Image OCR Text',
    'Extracted Text',
  ]

  public isItemDisabled(itemArgs: { dataItem: any }): boolean {
    return <boolean>itemArgs.dataItem.isHeader
  }

  // On drag start event handler for button and prevent default action for kendo sortable
  public onDragStart(event: DragStartEvent): void {
    if (!this.dragInitiated) {
      event.preventDefault()
    }
    this.dragInitiated = false
  }

  public setDragInitiated(value: boolean): void {
    this.dragInitiated = value
  }

  public onDragOver(event: DragOverEvent): void {
    event.preventDefault()
    this.sortable.moveItem(event.oldIndex, event.index)
  }
}
