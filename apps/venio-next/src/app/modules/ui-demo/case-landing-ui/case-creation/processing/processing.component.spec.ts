import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ProcessingComponent } from './processing.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('ProcessingComponent', () => {
  let component: ProcessingComponent
  let fixture: ComponentFixture<ProcessingComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ProcessingComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(ProcessingComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
