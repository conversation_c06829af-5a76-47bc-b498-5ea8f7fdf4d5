<div class="t-flex t-flex-col t-gap-4 t-p-4 t-max-h-[56vh] t-overflow-y-auto">
  <div class="t-flex t-flex-1 t-gap-4">
    <div class="t-flex t-flex-col t-gap-2 t-flex-1">
      <p class="t-font-bold t-text-[14px] t-text-[#263238]">Ingestion Engine</p>
      <label class="t-bg-white t-flex t-items-center">
        <input
          type="checkbox"
          kendoCheckBox
          rounded="small"
          size="small"
          class="t-self-start" />
        <span
          class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
          >Legacy</span
        >
      </label>
      <p class="t-font-bold t-text-[14px] t-text-[#263238]">
        Ingestion Settings
      </p>
      <p class="t-font-normal t-text-[14px] t-text-[#263238]">Date Setting</p>
      <kendo-datepicker
        class="t-w-1/2"
        [format]="'M/d/yyyy'"
        data-qa="searchTextDate"></kendo-datepicker>

      <kendo-datepicker
        class="t-w-1/2"
        [format]="'M/d/yyyy'"
        data-qa="searchTextDate"></kendo-datepicker>
      <label class="t-bg-white t-flex t-items-center">
        <input
          type="checkbox"
          kendoCheckBox
          rounded="small"
          size="small"
          class="t-self-start" />
        <span
          class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
          >Do not extract future date metadata values</span
        >
      </label>
      <div
        class="v-dashed-horizontal-sperator t-opacity-100 t-w-[auto] t-h-[2px]"></div>
      <p class="t-font-medium t-text-[14px] t-text-[#263238]">
        Group Date priority
      </p>
      <label class="t-bg-white t-flex t-items-center">
        <input
          type="checkbox"
          kendoCheckBox
          rounded="small"
          size="small"
          class="t-self-start" />
        <span
          class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
          >Set meeting date as group date if available Image</span
        >
      </label>
      <div
        class="v-dashed-horizontal-sperator t-opacity-100 t-w-[auto] t-h-[2px]"></div>
      <p class="t-font-medium t-text-[14px] t-text-[#263238]">
        CrashDocument Setting
      </p>
      <kendo-dropdownlist
        class="t-w-[333px]"
        textField="name"
        valueField="vale"
        [valuePrimitive]="true">
      </kendo-dropdownlist>
      <p class="t-font-medium t-text-[14px] t-text-[#263238]">Indexing</p>

      <div class="t-flex t-flex-col t-gap-1">
        <label class="t-bg-white t-flex t-items-center">
          <input
            type="checkbox"
            kendoCheckBox
            rounded="small"
            size="small"
            class="t-self-start" />
          <span
            class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
            >Index Fulltext</span
          >
        </label>

        <label class="t-bg-white t-flex t-items-center t-ml-3">
          <input
            type="checkbox"
            kendoCheckBox
            rounded="small"
            size="small"
            class="t-self-start" />
          <span
            class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
            >Index Email Headers with Fulltext</span
          >
        </label>

        <label class="t-bg-white t-flex t-items-center">
          <input
            type="checkbox"
            kendoCheckBox
            rounded="small"
            size="small"
            class="t-self-start" />
          <span
            class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
            >Index MetaData</span
          >
        </label>
      </div>

      <div
        class="v-dashed-horizontal-sperator t-opacity-100 t-w-[auto] t-h-[2px]"></div>
      <p class="t-font-medium t-text-[14px] t-text-[#263238]">
        Customize metadata for indexing
      </p>

      <p class="t-font-normal t-text-[12px] t-text-[#000000] t-text-wrap">
        Selected Metadata Are Indexed Into 'Attributes' Field.This Field Is Used
        When Running Searches With Attributes Or 'Fulltext And Attributes'
        Option In Search.
      </p>

      <kendo-grid
        class="t-pb-5"
        [kendoGridBinding]="repoGridData"
        venioDynamicHeight
        kendoGridSelectBy="id"
        [resizable]="true">
        <kendo-grid-checkbox-column
          [showSelectAll]="false"
          [width]="35"
          class="!t-px-[5px]"></kendo-grid-checkbox-column>
        <kendo-grid-column
          headerClass="t-text-primary"
          [filterable]="false"
          field="file_name"
          title="File Name"></kendo-grid-column>
      </kendo-grid>
    </div>

    <div class="t-flex t-flex-col t-gap-2 t-flex-1">
      <div class="t-flex t-gap-2">
        <div
          class="v-dashed-sperator t-mx-4 t-opacity-100 t-w-[2px] t-h-auto"></div>
        <div class="t-flex t-flex-col t-gap-2 t-flex-1">
          <p class="t-font-bold t-text-[14px] t-text-[#263238]">
            Ingestion Timeout Setting
          </p>

          <kendo-dropdownlist
            class="t-w-[224px]"
            textField="name"
            valueField="vale"
            [valuePrimitive]="true">
          </kendo-dropdownlist>

          <p class="t-font-bold t-text-[14px] t-text-[#1DBADC]">
            Ingestion FileType Timeout
          </p>
          <div
            class="v-dashed-horizontal-sperator t-opacity-100 t-w-[auto] t-h-[2px]"></div>
          <p class="t-font-bold t-text-[14px] t-text-[#263238]">
            Email Header Setting
          </p>
          <label class="t-bg-white t-flex t-items-center">
            <input
              type="checkbox"
              kendoCheckBox
              rounded="small"
              size="small"
              class="t-self-start" />
            <span
              class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap t-relative t-top-[-1px]"
              >Extract internet message header</span
            >
          </label>
          <div
            class="v-dashed-horizontal-sperator t-opacity-100 t-w-[auto] t-h-[2px]"></div>
          <p class="t-font-bold t-text-[14px] t-text-[#263238]">
            Post Processing Setting
          </p>

          <div class="t-flex t-flex-col t-gap-1">
            <label class="t-bg-white t-flex t-items-center">
              <input
                type="checkbox"
                kendoCheckBox
                rounded="small"
                size="medium"
                class="t-self-center custom-sm-checkbox" />
              <span class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap"
                >Auto compute and populate custodian dedupe field during
                ingestion</span
              >
            </label>

            <label class="t-bg-white t-flex t-items-center">
              <input
                type="checkbox"
                kendoCheckBox
                rounded="medium"
                class="t-self-center custom-sm-checkbox" />
              <span class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap"
                >Auto compute and populate duplicate file path during
                ingestion</span
              >
            </label>

            <label class="t-bg-white t-flex t-items-center">
              <input
                type="checkbox"
                kendoCheckBox
                rounded="medium"
                class="t-self-center custom-sm-checkbox" />
              <span class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap"
                >Auto folder using relative path during ingestion</span
              >
            </label>
          </div>
          <div
            class="v-dashed-horizontal-sperator t-opacity-100 t-w-[auto] t-h-[2px]"></div>
          <div class="t-flex t-flex-col t-gap-1">
            <p class="t-font-bold t-text-[14px] t-text-[#263238]">
              Nsf Viewing Extraction Setting
            </p>

            <kendo-dropdownlist
              class="t-w-[234px]"
              textField="name"
              valueField="vale"
              [valuePrimitive]="true">
            </kendo-dropdownlist>
          </div>
        </div>
      </div>

      <p class="t-font-bold t-text-[14px] t-text-[#263238] t-ml-[40px]">
        Transcribe Settings
      </p>
      <div class="t-flex t-gap-2">
        <div
          class="v-dashed-sperator t-mx-4 t-opacity-100 t-w-[2px] t-h-auto"></div>
        <div class="t-flex t-flex-col t-gap-2">
          <kendo-dropdownlist
            class="t-w-[224px]"
            textField="name"
            valueField="vale"
            [valuePrimitive]="true">
          </kendo-dropdownlist>

          <p class="t-font-normal t-text-[14px] t-text-[#263238]">
            Select file type for transcribing
          </p>

          <div class="t-flex t-flex-col t-gap-1">
            <label class="t-bg-white t-flex t-items-center">
              <input
                type="checkbox"
                kendoCheckBox
                rounded="medium"
                class="t-self-center custom-sm-checkbox" />
              <span class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap"
                >RIFFWAVE</span
              >
            </label>

            <label class="t-bg-white t-flex t-items-center">
              <input
                type="checkbox"
                kendoCheckBox
                rounded="medium"
                class="t-self-center custom-sm-checkbox" />
              <span class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap"
                >Automatically queue document for transcribing</span
              >
            </label>
          </div>
        </div>
      </div>

      <p class="t-font-bold t-text-[14px] t-text-[#263238] t-mt-4 t-ml-[40px]">
        Language Identification, Email Analysis & Email Threading
      </p>

      <div class="t-flex t-gap-2">
        <div
          class="v-dashed-sperator t-mx-4 t-opacity-100 t-w-[2px] t-h-[140px]"></div>
        <div class="t-flex t-flex-col t-gap-1">
          <label class="t-bg-white t-flex t-items-center">
            <input
              type="checkbox"
              kendoCheckBox
              rounded="medium"
              class="t-self-center custom-sm-checkbox" />
            <span class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap"
              >Enable Language Identification</span
            >
          </label>
          <label class="t-bg-white t-flex t-items-center t-ml-2">
            <input
              type="checkbox"
              kendoCheckBox
              rounded="medium"
              class="t-self-center custom-sm-checkbox" />
            <span class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap"
              >Compute Language Identification for Spreadsheet</span
            >
          </label>

          <label class="t-bg-white t-flex t-items-center">
            <input
              type="checkbox"
              kendoCheckBox
              rounded="medium"
              class="t-self-center custom-sm-checkbox" />
            <span class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap"
              >Enable Email Analytic</span
            >
          </label>

          <label class="t-bg-white t-flex t-items-center">
            <input
              type="checkbox"
              kendoCheckBox
              rounded="medium"
              class="t-self-center custom-sm-checkbox" />
            <span class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap"
              >Compute Email Thread Identification during Ingestion</span
            >
          </label>

          <label class="t-bg-white t-flex t-items-center t-ml-2">
            <input
              type="checkbox"
              kendoCheckBox
              rounded="medium"
              class="t-self-center custom-sm-checkbox" />
            <span class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap"
              >Compute Inclusive email during Ingestion</span
            >
          </label>

          <label class="t-bg-white t-flex t-items-center t-ml-2">
            <input
              type="checkbox"
              kendoCheckBox
              rounded="medium"
              class="t-self-center custom-sm-checkbox" />
            <span class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap"
              >Identify and generate missing email during Ingestion</span
            >
          </label>
        </div>
      </div>
    </div>

    <div class="t-flex t-flex-col t-gap-2 t-flex-1">
      <kendo-dropdownlist
        class="t-w-[224px] t-ml-[40px]"
        [data]="settingsOptions"
        textField="name"
        valueField="value"
        [valuePrimitive]="true"
        [(ngModel)]="selectedSetting">
      </kendo-dropdownlist>

      <div class="t-flex t-gap-2">
        <div
          class="v-dashed-sperator t-mx-4 t-opacity-100 t-w-[2px] t-h-auto"></div>

        <div
          class="t-flex t-flex-1 t-flex-col t-gap-2"
          *ngIf="selectedSetting === 'Advanced Ingesting Setting'">
          <p class="t-font-normal t-text-[14px] t-text-[#263238]">
            Duplicate computing option
          </p>

          <kendo-dropdownlist
            class="t-w-[368px]"
            textField="name"
            valueField="value"
            [valuePrimitive]="true">
          </kendo-dropdownlist>
          <div
            class="v-dashed-horizontal-sperator t-opacity-100 t-w-[auto] t-h-[2px]"></div>

          <p class="t-font-normal t-text-[14px] t-text-[#263238]">
            Auto Queued Job Setting
          </p>

          <label class="t-bg-white t-flex t-items-center">
            <input
              type="checkbox"
              kendoCheckBox
              rounded="medium"
              class="t-self-center custom-sm-checkbox" />
            <span class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap"
              >Do not queue duplicates during ingestion for following
              jobs:</span
            >
          </label>

          <div class="t-flex t-flex-row t-gap-2">
            <kendo-dropdownlist
              class="t-w-[125px]"
              textField="name"
              valueField="value"
              [valuePrimitive]="true">
            </kendo-dropdownlist>

            <p
              class="t-font-medium t-text-[10px] t-text-[#232323] t-self-center t-flex t-flex-1">
              <span class="t-font-normal t-text-[#ED7428]">NOTE: </span
              >unchecked job types all documents will be queued
            </p>
          </div>
          <hr />
          <kendo-grid
            class="t-pb-5"
            [kendoGridBinding]="job_setting_grid_data"
            venioDynamicHeight
            kendoGridSelectBy="id"
            [resizable]="false">
            <kendo-grid-checkbox-column
              [showSelectAll]="true"
              [width]="35"></kendo-grid-checkbox-column>
            <kendo-grid-column
              headerClass="t-text-primary"
              field="name"
              title="Job Name"></kendo-grid-column>
          </kendo-grid>
          <div
            class="v-dashed-horizontal-sperator t-opacity-100 t-w-[auto] t-h-[2px]"></div>
          <p class="t-font-normal t-text-[14px] t-text-[#263238]">
            File Copy Option
          </p>

          <kendo-dropdownlist
            class="t-w-[386px]"
            textField="name"
            valueField="value"
            [valuePrimitive]="true">
          </kendo-dropdownlist>
        </div>

        <div
          class="t-flex t-flex-1 t-flex-col t-gap-2"
          *ngIf="selectedSetting === 'Social Media Settings'">
          <div class="t-flex t-flex-col t-gap-2">
            <p class="t-font-normal t-text-[14px] t-text-[#263238]">
              Split Option
            </p>

            <div class="t-flex t-flex-col t-gap-1">
              <div class="t-flex t-items-start">
                <div>
                  <input
                    id="default-limit"
                    type="radio"
                    size="small"
                    [value]="0"
                    kendoRadioButton />
                </div>
                <div class="t-w-auto t-flex t-flex-wrap">
                  <kendo-label
                    class="t-ml-[5px] t-relative t-top-[2px]"
                    text="Entire Conversation"></kendo-label>
                </div>
              </div>
              <p
                class="t-font-medium t-text-[10px] t-text-[#232323] t-self-start t-ml-[1.35rem]">
                <span class="t-font-normal t-text-[#ED7428] t-mr-1">NOTE: </span
                >Entire conversation will be used to generate single eml file.
              </p>
            </div>

            <div class="t-flex t-flex-col t-gap-1">
              <div class="t-flex t-items-start">
                <div>
                  <input
                    id="default-limit"
                    type="radio"
                    size="small"
                    [value]="0"
                    kendoRadioButton />
                </div>
                <div class="t-w-auto t-flex t-flex-wrap">
                  <kendo-label
                    class="t-ml-[5px] t-relative t-top-[2px]"
                    text="Individual Message"></kendo-label>
                </div>
              </div>
              <p
                class="t-font-medium t-text-[10px] t-text-[#232323] t-self-start t-ml-[1.35rem]">
                <span class="t-font-normal t-text-[#ED7428] t-mr-1">NOTE: </span
                >EML files will be generated for individual messages.
              </p>
            </div>

            <div class="t-flex t-flex-col t-gap-1">
              <div class="t-flex t-items-start">
                <div>
                  <input
                    id="default-limit"
                    type="radio"
                    size="small"
                    [value]="0"
                    kendoRadioButton />
                </div>
                <div class="t-w-auto t-flex t-flex-wrap">
                  <kendo-label
                    class="t-ml-[5px] t-relative t-top-[2px]"
                    text="Number of Messages"></kendo-label>
                </div>
              </div>
              <p
                class="t-font-medium t-text-[10px] t-text-[#232323] t-self-start t-ml-[1.35rem]">
                <span class="t-font-normal t-text-[#ED7428] t-mr-1">NOTE: </span
                >EML files will be generated based on specified number of
                messages.
              </p>
              <div class="t-flex t-flex-row t-gap-2 t-ml-[22px]">
                <kendo-numerictextbox class="t-w-[86px]"></kendo-numerictextbox>
              </div>
            </div>

            <div class="t-flex t-flex-col t-gap-1">
              <div class="t-flex t-items-start">
                <div>
                  <input
                    id="default-limit"
                    type="radio"
                    size="small"
                    [value]="0"
                    kendoRadioButton />
                </div>
                <div class="t-w-auto t-flex t-flex-wrap">
                  <kendo-label
                    class="t-ml-[5px] t-relative t-top-[2px]"
                    text="Duration of Conversation"></kendo-label>
                </div>
              </div>
              <p
                class="t-font-medium t-text-[10px] t-text-[#232323] t-self-start t-ml-[1.35rem]">
                <span class="t-font-normal t-text-[#ED7428] t-mr-1">NOTE: </span
                >EML files will be generated based on conversation duration (in
                days or months or years)
              </p>
              <div class="t-flex t-flex-row t-gap-2 t-ml-[22px]">
                <kendo-numerictextbox class="t-w-[86px]"></kendo-numerictextbox>

                <kendo-numerictextbox class="t-w-[86px]"></kendo-numerictextbox>
              </div>
            </div>

            <div class="t-flex t-flex-col t-gap-1">
              <div class="t-flex t-items-start">
                <div>
                  <input
                    id="default-limit"
                    type="radio"
                    size="small"
                    [value]="0"
                    kendoRadioButton />
                </div>
                <div class="t-w-auto t-flex t-flex-wrap">
                  <kendo-label
                    class="t-ml-[5px] t-relative t-top-[2px]"
                    text="Gap of Conversation"></kendo-label>
                </div>
              </div>
              <p
                class="t-font-medium t-text-[10px] t-text-[#232323] t-self-start t-ml-[1.35rem]">
                <span class="t-font-normal t-text-[#ED7428] t-mr-1">NOTE: </span
                >EML files will be generated based on conversation gap (in days
                or months)
              </p>
              <div class="t-flex t-flex-row t-gap-2 t-ml-[22px]">
                <kendo-numerictextbox class="t-w-[86px]"></kendo-numerictextbox>

                <kendo-numerictextbox class="t-w-[86px]"></kendo-numerictextbox>
              </div>
            </div>
          </div>
        </div>

        <div
          class="t-flex t-flex-1 t-flex-col t-gap-2"
          *ngIf="selectedSetting === 'API Priority Settings'">
          <kendo-treelist
            class="v-custom-no-border-bg-grid"
            [kendoTreeListHierarchyBinding]="treeListView1"
            childrenField="items"
            kendoTreeListExpandable
            [rowReorderable]="true">
            <!-- Expandable Column -->
            <kendo-treelist-column
              [expandable]="true"
              title=""
              field="id"
              [width]="50">
              <ng-template kendoTreeListCellTemplate let-dataItem>
                <span [hidden]="true"></span>
              </ng-template>
            </kendo-treelist-column>

            <kendo-dropdownlist
              class="v-custom-dropdown-template"
              [data]="options"
              [valueField]="'value'"
              [textField]="'text'"
              [itemDisabled]="isItemDisabled">
              <ng-template kendoDropDownListItemTemplate let-dataItem>
                <ng-container *ngIf="dataItem.isHeader; else optionItem">
                  <div
                    class="t-py-2 t-mr-[3rem] t-flex t-flex-1 t-flex-col t-border t-border-b-[1px] t-border-t-[1px] t-border-l-[0px] t-border-r-[0px] t-border-t-solid t-border-[#979797] t-text-[#1DBADC]"
                    style="border-bottom: dashed">
                    {{ dataItem.text }}
                  </div>
                </ng-container>

                <ng-template #optionItem>
                  <div class="t-flex t-flex-col t-gap-1">
                    <span>{{ dataItem.text }}</span>
                  </div>
                </ng-template>
              </ng-template>
            </kendo-dropdownlist>

            <!-- File Type Column -->
            <kendo-treelist-column field="fileType" title="File Type">
              <ng-template
                kendoTreeListCellTemplate
                let-dataItem
                let-level="level">
                <ng-container *ngIf="level === 0">
                  <strong>{{ dataItem.fileType }}</strong>
                </ng-container>
                <ng-container *ngIf="level > 0">
                  <div class="t-flex t-items-center">
                    <button
                      kendoTreeListDragHandle
                      kendoButton
                      fillMode="flat"
                      size="small"
                      class="t-cursor-move t-px-[10px]">
                      <span
                        venioSvgLoader
                        svgUrl="assets/svg/icon-drag-drop.svg"
                        height="1.2rem"
                        width="1rem">
                        <kendo-loader size="small"></kendo-loader>
                      </span>
                    </button>
                    <span>{{ dataItem.fileType }}</span>
                  </div>
                </ng-container>
              </ng-template>
            </kendo-treelist-column>
            <!-- Other Columns -->
            <kendo-treelist-column field="extraction" title="Extraction">
              <ng-template kendoTreeListCellTemplate let-dataItem>
                {{ dataItem.extraction }}
              </ng-template>
            </kendo-treelist-column>

            <kendo-treelist-column field="priority" title="Priority">
              <ng-template kendoTreeListCellTemplate let-dataItem>
                {{ dataItem.priority }}
              </ng-template>
            </kendo-treelist-column>
          </kendo-treelist>
        </div>
      </div>

      <p class="t-font-bold t-text-[14px] t-text-[#263238] t-mt-4 t-ml-[40px]">
        File Identification of Archive/Container Files
      </p>
      <div class="t-flex t-gap-2">
        <div
          class="v-dashed-sperator t-mx-4 t-opacity-100 t-w-[2px] t-h-auto"></div>
        <div class="t-flex t-flex-col t-gap-2">
          <label class="t-bg-white t-flex t-items-center">
            <input
              type="checkbox"
              kendoCheckBox
              rounded="medium"
              class="t-self-center custom-sm-checkbox" />
            <span class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap"
              >Automatically queue document for transcribing</span
            >
          </label>

          <p class="t-font-normal t-text-[13px] t-text-[#263238]">
            Each extension(s) can be entered in separate line or can be entered
            in comma separated way
          </p>
          <kendo-textarea
            rounded="small"
            [rows]="2"
            placeholder="OXPS"
            resizable="vertical"></kendo-textarea>
        </div>
      </div>
      <p class="t-font-bold t-text-[14px] t-text-[#263238] t-mt-4 t-ml-[40px]">
        Near Duplicates Settings
      </p>
      <div class="t-flex t-gap-2">
        <div
          class="v-dashed-sperator t-mx-4 t-opacity-100 t-w-[2px] t-h-auto"></div>
        <div class="t-flex t-flex-col t-gap-1">
          <label class="t-bg-white t-flex t-items-center">
            <input
              type="checkbox"
              kendoCheckBox
              rounded="medium"
              class="t-self-center custom-sm-checkbox" />
            <span class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap"
              >Automatically queue documents for computing signatures</span
            >
          </label>

          <div class="t-flex t-flex-col t-gap-2">
            <div class="t-flex t-gap-3 t-justify-between">
              <p
                class="t-font-normal t-text-[13px] t-text-[#263238] t-self-center t-w-[199px]">
                Minimum threshold % to be similar
              </p>
              <kendo-numerictextbox
                #numeric
                class="v-custom-numerictextbox-adornment t-self-start t-w-[130px]">
                <ng-template kendoSuffixTemplate>
                  <span class="suffix-after-spinner">%</span>
                </ng-template>
              </kendo-numerictextbox>
            </div>

            <div class="t-flex t-gap-3 t-justify-between">
              <p
                class="t-font-normal t-text-[13px] t-text-[#263238] t-self-center t-w-[199px]">
                Minimum characters in fulltext for computing signature
              </p>
              <kendo-numerictextbox
                #numeric
                class="v-custom-numerictextbox-adornment t-self-start t-w-[130px]">
                <ng-template kendoSuffixTemplate>
                  <span class="suffix-after-spinner">C</span>
                </ng-template>
              </kendo-numerictextbox>
            </div>
          </div>

          <label class="t-bg-white t-flex t-items-center">
            <input
              type="checkbox"
              kendoCheckBox
              rounded="medium"
              class="t-self-center custom-sm-checkbox" />
            <span class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap"
              >Include metadata when computing near duplicate hashes</span
            >
          </label>

          <label class="t-bg-white t-flex t-items-center">
            <input
              type="checkbox"
              kendoCheckBox
              rounded="medium"
              class="t-self-center custom-sm-checkbox" />
            <span class="t-pl-2 t-text-[14px] t-tracking-tight t-text-wrap"
              >Filter Disclaimers</span
            >
          </label>
        </div>
      </div>
    </div>
  </div>
  <div
    class="v-dashed-horizontal-sperator t-opacity-100 t-w-[auto] t-h-[2px]"></div>
  <div class="t-flex t-flex-col t-gap-2">
    <p class="t-font-medium t-text-[14px] t-text-[#263238]">Stop Words</p>

    <div class="t-flex t-gap-2">
      <kendo-textbox
        class="!t-border-[#ccc] !t-w-[310px]"
        placeholder="Add more stop terms"
        [clearButton]="true">
      </kendo-textbox>
      <button
        kendoButton
        class="!t-rounded-none t-self-end"
        themeColor="primary"
        fillMode="outline"
        data-qa="upload-button">
        ADD
      </button>
    </div>

    <div class="t-flex t-flex-col t-gap-1 t-w-[346px]">
      <kendo-textbox
        class="!t-border-[#ccc] !t-w-[310px]"
        placeholder="Search"
        [clearButton]="true">
        <ng-template kendoTextBoxSuffixTemplate>
          <button
            kendoButton
            fillMode="clear"
            class="t-text-[#1EBADC]"
            imageUrl="assets/svg/icon-updated-search.svg"></button>
        </ng-template>
      </kendo-textbox>

      <kendo-grid
        class="t-pb-5"
        [kendoGridBinding]="stop_word_grid"
        venioDynamicHeight
        kendoGridSelectBy="id"
        [resizable]="true">
        <kendo-grid-checkbox-column
          [showSelectAll]="true"
          [width]="40"></kendo-grid-checkbox-column>
        <kendo-grid-column
          headerClass="t-text-primary"
          [filterable]="false"
          field="name"
          title="All"></kendo-grid-column>
      </kendo-grid>
    </div>
  </div>
</div>
