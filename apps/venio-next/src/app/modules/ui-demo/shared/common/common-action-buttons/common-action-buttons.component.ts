import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'

@Component({
  selector: 'venio-common-action-buttons',
  standalone: true,
  imports: [CommonModule, ButtonsModule, SvgLoaderDirective],
  templateUrl: './common-action-buttons.component.html',
  styleUrls: ['./common-action-buttons.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CommonActionButtonsComponent {
  @Input() public icons: any[]

  @Output() public readonly buttonClick = new EventEmitter<string>()

  public buttonClicked(actionType: string): void {
    this.buttonClick.emit(actionType)
  }
}
