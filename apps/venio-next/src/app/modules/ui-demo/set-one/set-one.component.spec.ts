import { ComponentFixture, TestBed } from '@angular/core/testing'
import { SetOneComponent } from './set-one.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('SetOneComponent', () => {
  let component: SetOneComponent
  let fixture: ComponentFixture<SetOneComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SetOneComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(SetOneComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
