import { ComponentFixture, TestBed } from '@angular/core/testing'
import { SetOneBatchReponsiveComponent } from './set-one-batch-reponsive.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('SetOneBatchReponsiveComponent', () => {
  let component: SetOneBatchReponsiveComponent
  let fixture: ComponentFixture<SetOneBatchReponsiveComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SetOneBatchReponsiveComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(SetOneBatchReponsiveComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
