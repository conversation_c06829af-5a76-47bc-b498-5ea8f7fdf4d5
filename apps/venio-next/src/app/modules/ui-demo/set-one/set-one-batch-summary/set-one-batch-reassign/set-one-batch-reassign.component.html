<div class="t-mt-5">
  <div class="t-mt-4 t-flex t-gap-2">
    <div class="t-w-full">
      <kendo-treeview
        [nodes]="treeViewData"
        textField="text"
        kendoTreeViewExpandable
        class="v-hide-scrollbar"
        [hasChildren]="hasChildren"
        [children]="getChildren"
        [expandedKeys]="expandedKeys"
        [extraSpacing]="70"
        venioDynamicHeight>
        <ng-template kendoTreeViewNodeTemplate let-dataItem>
          <div class="t-flex t-items-center t-gap-2">
            @if(dataItem.items){
            <span class="t-font-semibold">
              {{ dataItem.text }}
            </span>
            } @else {
            <span class="t-inline-flex t-items-center t-gap-2">
              <input
                type="radio"
                size="small"
                kendoRadioButton
                name="treeViewRadio"
                [attr.id]="'radio-' + dataItem.id"
                [value]="dataItem.id"
                [(ngModel)]="selectedRadio" />
            </span>
            <kendo-label
              [for]="'radio-' + dataItem.id"
              [text]="dataItem.text"></kendo-label>
            }
          </div>
        </ng-template>
      </kendo-treeview>
    </div>
  </div>

  <!-- footer-->
  <div class="t-flex t-w-full t-gap-4 t-justify-end t-pt-4">
    <button
      kendoButton
      class="v-custom-secondary-button"
      themeColor="secondary"
      fillMode="outline"
      data-qa="save">
      SAVE
    </button>
    <button data-qa="cancel" kendoButton themeColor="dark" fillMode="outline">
      CANCEL
    </button>
  </div>
</div>
