import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core'
import { CommonModule } from '@angular/common'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { FormsModule } from '@angular/forms'
import {
  DynamicHeightDirective,
  SvgLoaderDirective,
} from '@venio/feature/shared/directives'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { GridModule } from '@progress/kendo-angular-grid'
import { UiPaginationModule } from '@venio/ui/pagination'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { CommonActionTypes } from '@venio/shared/models/constants'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import { TooltipsModule } from '@progress/kendo-angular-tooltip'
import { chevronDownIcon, SVGIcon, xIcon } from '@progress/kendo-svg-icons'
import { IconsModule, SVGIconModule } from '@progress/kendo-angular-icons'
import { SetOneBatchReassignComponent } from './set-one-batch-reassign/set-one-batch-reassign.component'
import { SetOneBatchReponsiveComponent } from './set-one-batch-reponsive/set-one-batch-reponsive.component'
import { FilterExpandSettings } from '@progress/kendo-angular-treeview'

export interface Case {
  id: number
  name: string
  totalDocuments: number
  remaining: number
  reviewer: string
  status: string
}

@Component({
  selector: 'venio-set-one-batch-summary',
  standalone: true,
  imports: [
    CommonModule,
    DropDownsModule,
    FormsModule,
    SvgLoaderDirective,
    ButtonsModule,
    GridModule,
    DynamicHeightDirective,
    UiPaginationModule,
    InputsModule,
    LoaderModule,
    TooltipsModule,
    IconsModule,
    SVGIconModule,
    FormsModule,
    SetOneBatchReassignComponent,
    SetOneBatchReponsiveComponent,
  ],
  templateUrl: './set-one-batch-summary.component.html',
  styleUrl: './set-one-batch-summary.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SetOneBatchSummaryComponent implements OnInit {
  public downIcon: SVGIcon = chevronDownIcon

  public selectedTag = ''

  public pageSize = 10

  public sizes = [5, 10, 20, 50]

  public skip = 0

  public cases: any[] = []

  public commonActionTypes = CommonActionTypes

  public listItems: Array<string> = ['Tags', 'Query']

  public selectedValue = 'Tags'

  public tagList: Array<{ text: string; value: number }> = [
    { text: 'John Doe', value: 1 },
    { text: 'Ronnie', value: 2 },
    { text: 'Anthony', value: 3 },
  ]

  public tagData: Array<{ text: string; value: number }>

  public isOverlayActive = false

  public activeComponent: string | null = null

  public overlayTitle = ''

  public overlayIconUrl = ''

  public icons = {
    closeIcon: xIcon,
    custodianIcon: 'assets/svg/icon-shuffle-communicate.svg',
  }

  public selectedId: string

  public batchResponsive = false

  public tagDropdowndata: any[] = [
    {
      text: 'Node One',
      items: [{ text: 'Node A0' }, { text: 'Node A1' }, { text: 'Node A2' }],
    },
    {
      text: 'Node Two',
      items: [{ text: 'Node B0' }, { text: 'Node B1' }, { text: 'Node B2' }],
    },
  ]

  public expandedOnClear: 'none' | 'all' | 'initial' | 'unchanged' = 'none'

  constructor() {
    this.tagData = this.tagList.slice()
  }

  public ngOnInit(): void {
    this.generateDummyData()
  }

  private generateDummyData(): void {
    const reviewers = [
      'John Doe',
      'Melissa',
      'Sarah Smith',
      'Michael Johnson',
      'Emily Davis',
    ]
    const statuses = ['IN PROGRESS', 'COMPLETED', 'FAILED']

    for (let i = 1; i <= 50; i++) {
      this.cases.push({
        id: i,
        name: `Batch ${i.toString().padStart(3, '0')}`,
        totalDocuments: this.getRandomNumber(10000, 100000),
        remaining: this.getRandomNumber(1000, 50000),
        reviewer: reviewers[Math.floor(Math.random() * reviewers.length)],
        status: statuses[Math.floor(Math.random() * statuses.length)],
      })
    }
  }

  private getRandomNumber(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1)) + min
  }

  public caseActionControls(
    dataItem: any,
    actionType: CommonActionTypes
  ): void {
    if (actionType === CommonActionTypes.REASSIGN) {
      console.log(dataItem)
      this.selectedId = dataItem.name
      this.openOverlay('reassign')
    }
  }

  public capitalizeTitle(title: string): string {
    return title.replace(
      /\w\S*/g,
      (text) => text.charAt(0).toUpperCase() + text.slice(1).toLowerCase()
    )
  }

  public handleMainLayout(event: string): void {
    if (event === 'true') {
      //this.ifCreate = false
    }
  }

  public tagMapper(tags: any[]): any[] {
    return tags.length < 2 ? tags : [tags]
  }

  public onFilterChange(searchTerm: string): void {
    const contains = (
      value: string
    ): ((item: { text: string; value: number }) => boolean) => {
      return (item: { text: string; value: number }): boolean =>
        item.text.toLowerCase().includes(value.toLowerCase())
    }

    this.tagData = this.tagList.filter(contains(searchTerm))
  }

  public openOverlay(component: string): void {
    this.isOverlayActive = true
    this.activeComponent = component

    // Set the overlay title based on the active component
    if (component === 'reassign') {
      this.overlayTitle = 'Reassign'
      this.overlayIconUrl = this.icons.custodianIcon
    }
  }

  public closeOverlay(): void {
    this.isOverlayActive = false
    this.activeComponent = null
    this.overlayTitle = ''
    this.overlayIconUrl = ''
  }

  public get filterExpandSettings(): FilterExpandSettings {
    return { expandedOnClear: this.expandedOnClear }
  }
}
