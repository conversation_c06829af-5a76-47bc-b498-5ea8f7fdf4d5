import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core'
import { CommonModule } from '@angular/common'
import { FormsModule } from '@angular/forms'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { IconsModule } from '@progress/kendo-angular-icons'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LabelModule } from '@progress/kendo-angular-label'
import { TooltipModule } from '@progress/kendo-angular-tooltip'
import { TreeListModule } from '@progress/kendo-angular-treelist'
import { TreeViewModule } from '@progress/kendo-angular-treeview'
import { xIcon } from '@progress/kendo-svg-icons'
import {
  SvgLoaderDirective,
  DynamicHeightDirective,
} from '@venio/feature/shared/directives'
import { Observable, of } from 'rxjs'

@Component({
  selector: 'venio-set-one-batch-reponsive',
  standalone: true,
  imports: [
    CommonModule,
    IconsModule,
    LabelModule,
    FormsModule,
    InputsModule,
    ButtonsModule,
    LoaderModule,
    SvgLoaderDirective,
    TooltipModule,
    TreeListModule,
    DynamicHeightDirective,
    TreeViewModule,
  ],
  templateUrl: './set-one-batch-reponsive.component.html',
  styleUrl: './set-one-batch-reponsive.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SetOneBatchReponsiveComponent implements OnInit {
  public treeViewData: any[] = [
    {
      text: 'Batch 001',
      id: '0',
      name: 'John doe',
      status: 'IN PROGRESS',
      items: [
        { text: 'Facebook few', id: '1' },
        { text: 'Excel', id: '2' },
        { text: 'Slack Message', id: '3' },
        { text: 'Cellebrite', id: '4' },
      ],
    },
    {
      text: 'Batch 002',
      id: '5',
      name: 'Robert C. Willis',
      status: 'COMPLETED',
      items: [{ text: 'M001', id: '6' }],
    },
    {
      text: 'Batch 003',
      id: '6',
      name: 'Ernest T. Johnson',
      status: 'NOT STARTED',
      items: [{ text: 'M001', id: '6' }],
    },
  ]

  public checkedKeys: any[] = ['1', '3', '6']

  public expandedKeys: any[] = []

  public treeViewData2: any[] = [
    {
      text: 'Media Name',
      id: '0',
      items: [
        { text: 'Facebook few', id: '1' },
        { text: 'Excel', id: '2' },
        { text: 'Slack Message', id: '3' },
        { text: 'Cellebrite', id: '4' },
      ],
    },
  ]

  public checkedKeys2: any[] = ['0']

  public expandedKeys2: any[] = []

  constructor() {}

  public ngOnInit(): void {
    this.expandedKeys = this.getAllNodeIds(this.treeViewData)
  }

  public icons = {
    closeIcon: xIcon,
  }

  public hasChildren = (item: any): boolean => {
    return item.items && item.items.length > 0
  }

  public getChildren(node: any): Observable<any[]> {
    return of(node.items)
  }

  private getAllNodeIds(nodes: any[]): string[] {
    let keys: string[] = []

    for (const node of nodes) {
      if (node.id) {
        keys.push(node.id)
      }

      if (node.items) {
        keys = keys.concat(this.getAllNodeIds(node.items))
      }
    }

    return keys
  }
}
