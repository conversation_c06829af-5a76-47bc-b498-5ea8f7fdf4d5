import { ComponentFixture, TestBed } from '@angular/core/testing'
import { SetOneBatchReassignComponent } from './set-one-batch-reassign.component'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('SetOneBatchReassignComponent', () => {
  let component: SetOneBatchReassignComponent
  let fixture: ComponentFixture<SetOneBatchReassignComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SetOneBatchReassignComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(SetOneBatchReassignComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
