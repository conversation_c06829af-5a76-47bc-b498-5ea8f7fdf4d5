<p>Set One Loading ..</p>

<div kendoWindowContainer></div>

<!-- Window title bar-->
<ng-template #windowTitleBar let-win>
  <span class="k-window-title t-items-center t-text-primary t-text-lg">
    <button
      class="t-inline-block t-w-[35px] t-p-0 t-bg-[#F3F3F3] t-rounded-full t-mr-2 t-h-[35px] t-flex t-items-center"
      fillMode="clear"
      kendoButton
      imageUrl="assets/svg/icon-set-one-rating-multiple.svg"></button>
    Set One

    <span
      class="t-inline-block t-rounded t-px-3 t-py-1 t-text-uppercase t-text-xs t-bg-[#9BD2A7] t-ml-2">
      <span class="t-text-[#0F4B1B] t-tracking-widest"> CASE NAME </span>

      <span class="t-text-[#FFFFFF] t-tracking-wide">
        John <PERSON> Sate Of Florida</span
      >
    </span>

    <span class="t-text-[#263238] t-text-base t-ml-2">
      Number of documents 100K
    </span>
  </span>

  <div class="t-flex t-w-[270px] t-gap-4 t-justify-between t-items-center">
    <kendo-dropdownlist
      [data]="selectUsersData"
      [filterable]="true"
      textField="text"
      valueField="value"
      [defaultItem]="defaultItem"
      placeholder="Select Reviewer"
      class="t-w-full">
    </kendo-dropdownlist>

    <button
      kendoWindowCloseAction
      [window]="win"
      class="v-dialog-action-button v-dialog-action-button-cancel v-dialog-close t-rounded-full t-w-6 t-h-6 t-p-0"></button>
  </div>
</ng-template>
