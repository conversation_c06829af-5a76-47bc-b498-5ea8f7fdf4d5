import {
  ChangeDetectionStrategy,
  Component,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { WindowService, WindowModule } from '@progress/kendo-angular-dialog'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import {
  DropDownListComponent,
  KENDO_DROPDOWNS,
} from '@progress/kendo-angular-dropdowns'

@Component({
  selector: 'venio-set-one',
  standalone: true,
  imports: [CommonModule, ButtonsModule, WindowModule, KENDO_DROPDOWNS],
  templateUrl: './set-one.component.html',
  styleUrl: './set-one.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SetOneComponent implements OnInit {
  @ViewChild('windowTitleBar', { static: true })
  public windowTitleBar!: TemplateRef<unknown>

  @ViewChild('list') public list: DropDownListComponent

  public source: Array<{ text: string; value: number }> = [
    { text: '<PERSON>', value: 1 },
    { text: '<PERSON><PERSON> hayden', value: 2 },
    { text: '<PERSON><PERSON><PERSON>', value: 3 },
  ]

  public defaultItem: { text: string; value: number } = {
    text: 'Select Reviewer',
    value: null,
  }

  public selectUsersData: Array<{ text: string; value: number }>

  constructor(public windowService: WindowService) {}

  public ngOnInit(): void {
    this.dashboard()
    this.selectUsersData = this.source.slice()
  }

  private reprocess(): void {
    console.log('Reprocess action triggered')
    import('./set-one-batch-summary/set-one-batch-summary.component').then(
      (td) => {
        this.windowService.open({
          content: td.SetOneBatchSummaryComponent, // Dynamically load main content
          left: 0,
          state: 'maximized',
          top: 100,
          cssClass: 'v-custom-window',
          titleBarContent: this.windowTitleBar,
          resizable: false,
          draggable: false,
        })
      }
    )
  }

  private dashboard(): void {
    console.log('Reprocess action triggered')
    import('./set-one-batch-dashboard/set-one-batch-dashboard.component').then(
      (td) => {
        this.windowService.open({
          content: td.SetOneBatchDashboardComponent, // Dynamically load main content
          left: 0,
          state: 'maximized',
          top: 100,
          cssClass: 'v-custom-window',
          titleBarContent: this.windowTitleBar,
          resizable: false,
          draggable: false,
        })
      }
    )
  }
}
