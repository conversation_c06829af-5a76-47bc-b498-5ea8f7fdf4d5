import { ComponentFixture, TestBed } from '@angular/core/testing'
import { LoginPageUiComponent } from './login-page-ui.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { HttpClientTestingModule } from '@angular/common/http/testing'
import { ActivatedRoute } from '@angular/router'
import { of } from 'rxjs'

describe('LoginPageUiComponent', () => {
  let component: LoginPageUiComponent
  let fixture: ComponentFixture<LoginPageUiComponent>

  const activatedRouteMock = {
    queryParams: of({ action: 'forgot-password' }),
  }

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        LoginPageUiComponent,
        HttpClientTestingModule,
        NoopAnimationsModule,
      ],
      providers: [
        { provide: ActivatedRoute, useValue: activatedRouteMock }, // Provide the mock
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(LoginPageUiComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
