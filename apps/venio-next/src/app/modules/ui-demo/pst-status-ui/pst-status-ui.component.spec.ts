import { ComponentFixture, TestBed } from '@angular/core/testing'
import { PstStatusUiComponent } from './pst-status-ui.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('PstStatusUiComponent', () => {
  let component: PstStatusUiComponent
  let fixture: ComponentFixture<PstStatusUiComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [PstStatusUiComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(PstStatusUiComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
