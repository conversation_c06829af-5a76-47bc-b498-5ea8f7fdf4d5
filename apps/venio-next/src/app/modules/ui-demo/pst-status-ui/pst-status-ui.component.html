<div class="t-flex t-flex-col">
  <kendo-dialog
    *ngIf="opened"
    (close)="close('cancel')"
    [height]="'90vh'"
    [minWidth]="250"
    [maxWidth]="1600"
    [width]="'80%'">
    <kendo-dialog-titlebar>
      <div class="t-flex t-w-[65%] t-justify-between">
        <div class="t-block">{{ dialogTitle }}</div>
        <div class="t-block">
          <div
            #appendNotification
            class="v-append-notification-container t-text-sm t-inline-block t-text-xs t-overflow-hidden t-relative t-h-8 t-w-[420px]"></div>
        </div>
      </div>
    </kendo-dialog-titlebar>

    <kendo-tabstrip (tabSelect)="onSelect($event)">
      <kendo-tabstrip-tab title="Create PST" [selected]="true">
        <ng-template kendoTabContent>
          <div class="t-flex t-flex-col t-w-full t-mt-3">
            <kendo-grid
              class="t-flex t-flex-col-reverse"
              [data]="gridDataPST.data"
              [pageSize]="pageSize"
              [skip]="skip"
              [pageable]="true"
              (pageChange)="pageChange($event)"
              [resizable]="true">
              <ng-template kendoPagerTemplate>
                <div class="t-flex t-w-full t-justify-end">
                  <venio-pagination
                    [disabled]="gridDataPST?.data.length === 0"
                    [totalRecords]="gridDataPST?.total"
                    [pageSize]="pageSize"
                    [showPageJumper]="false"
                    [showPageSize]="true"
                    [showRowNumberInputBox]="true"
                    class="t-px-5 t-block t-py-2">
                  </venio-pagination>
                </div>
              </ng-template>
              <kendo-grid-column
                field="id"
                title="#"
                headerClass="t-text-primary"
                [width]="50"
                [minResizableWidth]="30"></kendo-grid-column>
              <kendo-grid-column
                field="rootFolder"
                title="Root Folder"
                headerClass="t-text-primary"
                [width]="160"
                [minResizableWidth]="130">
                <ng-template kendoGridHeaderTemplate>
                  <span title="Root Folder">Root Folder</span>
                </ng-template>
                <ng-template kendoGridCellTemplate let-dataItem>
                  <span title="{{ dataItem.rootFolder }}">{{
                    dataItem.rootFolder
                  }}</span>
                </ng-template>
              </kendo-grid-column>
              <kendo-grid-column
                field="logicalPath"
                headerClass="t-text-primary"
                title="Logical Path">
                <ng-template kendoGridHeaderTemplate>
                  <span title="Logical Path">Logical Path</span>
                </ng-template>
                <ng-template kendoGridCellTemplate let-dataItem>
                  <span title="{{ dataItem.logicalPath }}">{{
                    dataItem.logicalPath
                  }}</span>
                </ng-template>
              </kendo-grid-column>
            </kendo-grid>
          </div>
        </ng-template>
      </kendo-tabstrip-tab>
      <kendo-tabstrip-tab title="PST Status">
        <ng-template kendoTabContent>
          <kendo-grid
            class="t-flex t-flex-col-reverse"
            [data]="gridDataStatus.data"
            [pageSize]="pageSize"
            [skip]="skip"
            [pageable]="true"
            (pageChange)="pageChange($event)"
            [resizable]="true">
            <ng-template kendoPagerTemplate>
              <div class="t-flex t-w-full t-justify-end">
                <venio-pagination
                  [disabled]="gridDataStatus?.data.length === 0"
                  [totalRecords]="gridDataStatus?.total"
                  [pageSize]="pageSize"
                  [showPageJumper]="false"
                  [showPageSize]="true"
                  [showRowNumberInputBox]="true"
                  class="t-px-5 t-block t-py-2">
                </venio-pagination>
              </div>
            </ng-template>
            <kendo-grid-column
              field="id"
              title="#"
              headerClass="t-text-primary"
              [width]="40"
              [minResizableWidth]="40"></kendo-grid-column>
            <kendo-grid-column
              field="pstName"
              title="PST Name"
              headerClass="t-text-primary"
              [width]="150"
              [minResizableWidth]="150">
              <ng-template kendoGridHeaderTemplate>
                <span kendoTooltip title="PST Name">PST Name</span>
              </ng-template>
              <ng-template kendoGridCellTemplate let-dataItem>
                <span kendoTooltip title="{{ dataItem.pstName }}">{{
                  dataItem.pstName
                }}</span>
              </ng-template>
            </kendo-grid-column>
            <kendo-grid-column
              field="createdByOn"
              title="Created By & On"
              headerClass="t-text-primary"
              [width]="250"
              [minResizableWidth]="250">
              <ng-template kendoGridHeaderTemplate>
                <span kendoTooltip title="Created By & On"
                  >Created By & On</span
                >
              </ng-template>
              <ng-template kendoGridCellTemplate let-dataItem>
                <span
                  kendoTooltip
                  title="{{ dataItem.createdByGroup }} {{
                    dataItem.createdByOn
                  }}">
                  <span class="t-font-semibold">{{
                    dataItem.createdByGroup | titlecase
                  }}</span>
                  <span> {{ dataItem.createdByOn | date : 'dd-MM-yyyy' }}</span>
                  <span class="t-text-xs">
                    {{ dataItem.createdByOn | date : 'shortTime' }}</span
                  >
                </span>
              </ng-template>
            </kendo-grid-column>
            <kendo-grid-column
              field="status"
              title="Status"
              headerClass="t-text-primary"
              [width]="150"
              [minResizableWidth]="150">
              <ng-template kendoGridHeaderTemplate>
                <span kendoTooltip title="Status">Status</span>
              </ng-template>
              <ng-template kendoGridCellTemplate let-dataItem>
                <span kendoTooltip title="{{ dataItem.status }}">{{
                  dataItem.status
                }}</span>
              </ng-template>
            </kendo-grid-column>
            <kendo-grid-column
              field="action"
              title="Action"
              headerClass="t-text-primary"
              [width]="150"
              [minResizableWidth]="150">
              <ng-template kendoGridHeaderTemplate>
                <span kendoTooltip title="Action">Action</span>
              </ng-template>
              <ng-template kendoGridCellTemplate let-dataItem>
                <kendo-buttongroup>
                  <button
                    kendoButton
                    #actionGrid
                    *ngFor="let icon of svgIconForGridControls"
                    class="!t-p-[0.3rem] t-w-[1.5rem]"
                    (click)="browseActionClicked(icon.actionType)"
                    fillMode="outline"
                    kendoTooltip
                    [title]="icon.actionType"
                    size="none">
                    <span
                      [parentElement]="actionGrid.element"
                      venioSvgLoader
                      hoverColor="#FFFFFF"
                      [color]="'#979797'"
                      [svgUrl]="icon.iconPath"
                      height="0.9rem"
                      width="1rem"></span>
                  </button>
                </kendo-buttongroup>
              </ng-template>
            </kendo-grid-column>
          </kendo-grid>
        </ng-template>
      </kendo-tabstrip-tab>
    </kendo-tabstrip>

    <kendo-dialog-actions>
      <div class="t-flex t-text-xs" *ngIf="tabStatus === 0">
        <div class="t-block t-text-error t-pr-2">Note:</div>
        <div class="t-flex t-flex-col t-gap-1">
          <p>
            Some of the selected files have extension other than msg/eml/emlx or
            their native doesn't exists.
          </p>
          <p>
            <span class="t-text-primary t-font-semibold"> Click here</span> to
            download excluded files.
          </p>
        </div>
      </div>
      <div class="t-flex t-gap-4 t-justify-end">
        @if(this.tabStatus === 1){
        <button
          kendoButton
          (click)="close('yes')"
          themeColor="dark"
          fillMode="outline"
          data-qa="cancel-button">
          CLOSE
        </button>
        } @else {
        <button
          kendoButton
          (click)="close('no')"
          class="v-custom-secondary-button"
          themeColor="secondary"
          fillMode="outline"
          data-qa="save-button">
          CREATE
        </button>
        <button
          kendoButton
          (click)="close('yes')"
          themeColor="dark"
          fillMode="outline"
          data-qa="cancel-button">
          CANCEL
        </button>
        }
      </div>
    </kendo-dialog-actions>
  </kendo-dialog>
</div>
