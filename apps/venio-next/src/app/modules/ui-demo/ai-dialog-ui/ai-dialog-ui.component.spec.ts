import { ComponentFixture, TestBed } from '@angular/core/testing'
import { AiDialogUiComponent } from './ai-dialog-ui.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('AiDialogUiComponent', () => {
  let component: AiDialogUiComponent
  let fixture: ComponentFixture<AiDialogUiComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AiDialogUiComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(AiDialogUiComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
