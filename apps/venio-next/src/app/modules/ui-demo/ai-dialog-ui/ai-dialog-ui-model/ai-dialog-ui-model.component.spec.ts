import { ComponentFixture, TestBed } from '@angular/core/testing'
import { AiDialogUiModelComponent } from './ai-dialog-ui-model.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { DialogRef } from '@progress/kendo-angular-dialog'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('AiDialogUiModelComponent', () => {
  let component: AiDialogUiModelComponent
  let fixture: ComponentFixture<AiDialogUiModelComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AiDialogUiModelComponent, NoopAnimationsModule],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: DialogRef, useValue: {} },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(AiDialogUiModelComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
