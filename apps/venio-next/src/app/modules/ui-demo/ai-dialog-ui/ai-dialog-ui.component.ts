import {
  ChangeDetectionStrategy,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import {
  DialogRef,
  DialogService,
  DialogsModule,
} from '@progress/kendo-angular-dialog'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { AiDialogUiModelComponent } from './ai-dialog-ui-model/ai-dialog-ui-model.component'
import { Subject, takeUntil } from 'rxjs'

@Component({
  selector: 'venio-ai-dialog-ui',
  standalone: true,
  imports: [CommonModule, DialogsModule, ButtonsModule, InputsModule],
  templateUrl: './ai-dialog-ui.component.html',
  styleUrl: './ai-dialog-ui.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AiDialogUiComponent implements OnInit, On<PERSON><PERSON>roy {
  private dialogRef: DialogRef

  private toDestroy$ = new Subject<void>()

  public opened = false

  constructor(private dialogService: DialogService) {}

  public ngOnInit(): void {
    this.openDialog()
  }

  public openDialog(): void {
    this.openAiAssistant()
  }

  public openAiAssistant(): void {
    this.dialogRef = this.dialogService.open({
      content: AiDialogUiModelComponent,
      cssClass:
        't-transition-all t-ease-in-out t-delay-800 t-translate-y-0 v-custom-dialog-pos',
      maxHeight: '90vh',
      maxWidth: '1500px',
      width: '90%',
      minWidth: '250px',
    })

    const instance = this.dialogRef.content.instance as AiDialogUiModelComponent
    instance.searchEventClicked
      .pipe(takeUntil(this.toDestroy$))
      .subscribe(() => {
        this.addClassToDialog()
      })
  }

  private addClassToDialog(): void {
    if (this.dialogRef) {
      const dialogElement = this.dialogRef.dialog.location.nativeElement
      dialogElement.classList.remove('v-custom-dialog-pos')
    }
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }
}
