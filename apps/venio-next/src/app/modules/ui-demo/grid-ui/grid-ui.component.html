<!--NOTE: as your needs, can make reusable and use it withing multiple files or-->
<!--break it down into smaller chunk of components to handle separate logics etc.,-->
<!--Please refer to the keno's official grid doc for more detail-->
<div #parent class="t-block t-h-72 t-relative">
  <kendo-grid
    [loading]="isLoading"
    [kendoGridBinding]="data"
    filterable="menu"
    [resizable]="true"
    [sortable]="true"
    [pageSize]="50"
    scrollable="virtual"
    [reorderable]="true"
    [rowHeight]="30"
    [virtualColumns]="true"
    kendoGridSelectBy
    [(selectedKeys)]="selectedKeys"
    [height]="parent.clientHeight"
    style="width: 100%; overflow: auto"
    [selectable]="{ mode: 'multiple', drag: true }"
    class="">
    <kendo-grid-column
      [headerStyle]="{
        color: '#2F3080'
      }"
      class="!t-py-[0.6rem]"
      title="#"
      [width]="50"
      [reorderable]="false">
      <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
        {{ rowIndex + 1 }}
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-checkbox-column
      [showSelectAll]="true"
      [width]="50"
      class="!t-py-[0.6rem]"></kendo-grid-checkbox-column>
    <!--    TODO: maybe on the global ro the specific to the certain UI-->
    <!--    Changing the header style according to the mockup-->
    <kendo-grid-column
      [headerStyle]="{
        color: '#2F3080',
        'later-spacing': '0.42px',
        'text-transform': 'capitalize'
      }"
      *ngFor="let col of columns"
      class="!t-py-[0.6rem]"
      [field]="col"
      [title]="'Column ' + col"
      [width]="150"
      [reorderable]="true">
    </kendo-grid-column>
  </kendo-grid>
</div>
