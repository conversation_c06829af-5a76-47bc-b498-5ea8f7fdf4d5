<div class="t-flex t-p-8">
  <button kendoButton (click)="openDialog()">Delete Document Dialog ui</button>
</div>

<kendo-dialog
  *ngIf="opened"
  (close)="close('cancel')"
  [maxHeight]="550"
  [height]="'80vh'"
  [minWidth]="250"
  [width]="'47%'"
  class="t-mt-2">
  <kendo-dialog-titlebar>
    <div>
      {{ dialogTitle }}
    </div>
  </kendo-dialog-titlebar>

  <div class="t-flex t-relative t-w-full t-mt-2 t-flex-col">
    <kendo-grid
      [kendoGridBinding]="gridData"
      filterable="menu"
      [hideHeader]="true"
      class="t-h-50 t-overflow-y-auto">
      <kendo-grid-column field="fileInfo"> </kendo-grid-column>
      <kendo-grid-column [width]="115" field="number"> </kendo-grid-column>
    </kendo-grid>

    <div class="t-mt-2 v-custom-grey-bg">
      <fieldset>
        <div class="t-k-form-field">
          <input
            type="radio"
            name="selectionMode"
            id="singleCheck"
            kendoRadioButton
            value="single"
            [(ngModel)]="checkMode" />
          <label class="t-k-radio-label t-mr-2" for="singleCheck"
            >Documents and all associated files</label
          >

          <input
            type="radio"
            name="checkMode"
            id="multipleCheck"
            value="multiple"
            kendoRadioButton
            [(ngModel)]="checkMode" />
          <label class="t-k-radio-label" for="multipleCheck"
            >Document Type</label
          >

          <kendo-treeview
            [nodes]="docAndASssociatedFiles"
            textField="text"
            [kendoTreeViewCheckable]="checkableSettings"
            [(checkedKeys)]="checkedKeys"
            *ngIf="checkMode === 'single'">
          </kendo-treeview>

          <kendo-treeview
            *ngIf="checkMode === 'multiple'"
            [nodes]="docType"
            [children]="children"
            [hasChildren]="hasChildren"
            textField="text"
            [kendoTreeViewCheckable]="checkableSettings"
            [(checkedKeys)]="checkedKeys1">
          </kendo-treeview>
          <p class="t-mt-2 t-mb-2 t-w-full t-text-error t-text-sm">
            Note: If document selected for delete is not parent then its
            child(ren) will also be deleted. Document(s) will be deleted even if
            they contain Redactions/tags/document notes or are included in
            Review-sets or Exports.
          </p>

          <div class="t-flex t-w-full">
            <kendo-progressbar
              themecolor="warning"
              [max]="max"
              [value]="value"
              [label]="label"
              class="t-w-full"></kendo-progressbar>
          </div>
        </div>
      </fieldset>
    </div>
  </div>

  <kendo-dialog-actions>
    <div class="t-flex t-gap-4 t-justify-end">
      <button
        kendoButton
        (click)="close('no')"
        class="v-custom-secondary-button"
        fillMode="outline"
        themeColor="error">
        Delete
      </button>
      <button
        kendoButton
        (click)="close('yes')"
        themeColor="dark"
        fillMode="outline">
        Close
      </button>
    </div>
  </kendo-dialog-actions>
</kendo-dialog>
