import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DeleteDocComponent } from './delete-doc.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('DeleteDocComponent', () => {
  let component: DeleteDocComponent
  let fixture: ComponentFixture<DeleteDocComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DeleteDocComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(DeleteDocComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
