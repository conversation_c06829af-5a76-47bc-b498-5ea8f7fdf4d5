import {
  ChangeDetectionStrategy,
  Component,
  OnInit,
  ViewChild,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DialogsModule } from '@progress/kendo-angular-dialog'
import { GridModule } from '@progress/kendo-angular-grid'
import {
  LabelSettings,
  ProgressBarModule,
} from '@progress/kendo-angular-progressbar'
import { DropDownListModule } from '@progress/kendo-angular-dropdowns'
import { InputsModule } from '@progress/kendo-angular-inputs'
import {
  CheckableSettings,
  TreeViewModule,
} from '@progress/kendo-angular-treeview'
import { of, Observable } from 'rxjs'
import {
  FlatBindingDirective,
  TreeListModule,
} from '@progress/kendo-angular-treelist'
import { CommonActionButtonsComponent } from '../shared/common/common-action-buttons/common-action-buttons.component'

import { LabelModule } from '@progress/kendo-angular-label'
import { FormsModule } from '@angular/forms'

export const sampleData = [
  {
    fileInfo: 'Files selected for delete from search hits',
    number: 1,
  },
  {
    fileInfo: 'Child files to be detected',
    number: 0,
  },
  {
    fileInfo: 'Total files to be deducted',
    number: 1,
  },
]

@Component({
  selector: 'venio-delete-doc',
  standalone: true,
  imports: [
    CommonModule,
    DialogsModule,
    ButtonsModule,
    LabelModule,
    GridModule,
    TreeViewModule,
    ProgressBarModule,
    FormsModule,
    InputsModule,
    DropDownListModule,
    TreeListModule,
    CommonActionButtonsComponent,
  ],
  templateUrl: './delete-doc.component.html',
  styleUrls: ['./delete-doc.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DeleteDocComponent implements OnInit {
  public opened = false

  public addStatus = false

  public value = 4

  public max = 9

  public dialogTitle = 'Delete Document'

  public label: LabelSettings = {
    visible: false,
    format: 'value',
    position: 'center',
  }

  public checkedKeys: any[] = ['']

  public checkedKeys1: any[] = ['']

  public enableCheck = true

  public checkChildren = true

  public checkDisabledChildren = false

  public checkParents = true

  public checkOnClick = false

  public checkMode: any = 'multiple'

  public selectionMode: any = 'single'

  @ViewChild(FlatBindingDirective) public dataBinding: FlatBindingDirective

  public selected: any[] = []

  public gridData: unknown[] = sampleData

  public ngOnInit(): void {
    this.openDialog()
  }

  public close(status: string): void {
    console.log(`Dialog result: ${status}`)
    this.opened = false
  }

  public openDialog(): void {
    this.opened = true
  }

  public get checkableSettings(): CheckableSettings {
    return {
      checkChildren: this.checkChildren,
      checkDisabledChildren: this.checkDisabledChildren,
      checkParents: this.checkParents,
      enabled: this.enableCheck,
      mode: this.checkMode,
      checkOnClick: this.checkOnClick,
    }
  }

  public docAndASssociatedFiles: any[] = [
    { text: 'Delete child record of all the selected parent documents' },
    { text: 'Delete media if all the files are deleted' },
  ]

  public docType: any[] = [
    { text: 'Only Images' },
    { text: 'Image redaction objects (Pointers and redacted OCR text)' },
    { text: 'Only Native' },
    { text: 'Only HTML and RTF' },
  ]

  public children = (dataItem: any): Observable<any[]> => of(dataItem.items)

  public hasChildren = (dataItem: any): boolean => !!dataItem.items
}
