import { ComponentFixture, TestBed } from '@angular/core/testing'
import { EpocUiComponent } from './epoc-ui.component'
import { HttpClientTestingModule } from '@angular/common/http/testing'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'

describe('EpocUiComponent', () => {
  let component: EpocUiComponent
  let fixture: ComponentFixture<EpocUiComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [EpocUiComponent, HttpClientTestingModule, NoopAnimationsModule],
    }).compileComponents()

    fixture = TestBed.createComponent(EpocUiComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
