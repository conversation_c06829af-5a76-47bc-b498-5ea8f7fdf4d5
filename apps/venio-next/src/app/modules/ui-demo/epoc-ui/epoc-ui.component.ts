import {
  ChangeDetectionStrategy,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core'
import { CommonModule } from '@angular/common'
import {
  DateInputsModule,
  SelectionRange,
} from '@progress/kendo-angular-dateinputs'
import { DialogService, DialogsModule } from '@progress/kendo-angular-dialog'
import {
  LabelSettings,
  ProgressBarModule,
} from '@progress/kendo-angular-progressbar'
import {
  eyeIcon,
  xIcon,
  checkIcon,
  calendarIcon,
  caretAltDownIcon,
  SVGIcon,
  chevronDownIcon,
  plusIcon,
  minusIcon,
} from '@progress/kendo-svg-icons'
import { ConfirmationDialogComponent } from '@venio/feature/notification'
import { Subject } from 'rxjs'
import { ButtonsModule } from '@progress/kendo-angular-buttons'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import { GridModule } from '@progress/kendo-angular-grid'
import { IconsModule } from '@progress/kendo-angular-icons'
import { InputsModule } from '@progress/kendo-angular-inputs'
import { LayoutModule } from '@progress/kendo-angular-layout'
import { TooltipModule, PopoverModule } from '@progress/kendo-angular-tooltip'
import {
  DynamicHeightDirective,
  SvgLoaderDirective,
} from '@venio/feature/shared/directives'
import { UiPaginationModule } from '@venio/ui/pagination'
import { ReportDatePickerComponent } from '../../reports/tabular/report-date-picker/report-date-picker.component'

interface IssueData {
  Issue: number
  Relevant: number
  NonRelevant: number
  NeedFurtherReview: number
  TechIssue: number
}

interface AiData {
  issueNumber: number
  issueTitle: string
  tag: string
  explanation: string
}

interface GridItemHistory {
  hashNum: number
  jobName: string
  redactedBy: string
  redactedOn: string
  term: string
  hits: string
  numOfDoc: number
  redactedStatus: string
  statusCount: number
  viewAction: boolean
}

@Component({
  selector: 'venio-epoc-ui',
  standalone: true,
  imports: [
    CommonModule,
    DialogsModule,
    ButtonsModule,
    InputsModule,
    IconsModule,
    LayoutModule,
    GridModule,
    TooltipModule,
    PopoverModule,
    UiPaginationModule,
    DropDownsModule,
    DateInputsModule,
    SvgLoaderDirective,
    ProgressBarModule,
    ReportDatePickerComponent,
    DynamicHeightDirective,
  ],
  templateUrl: './epoc-ui.component.html',
  styleUrl: './epoc-ui.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EpocUiComponent implements OnInit, OnDestroy {
  public listItems: Array<string> = ['Item 1', 'Item 2', 'Item 3']

  public pageSize = 10

  public sizes = [5, 10, 20, 50]

  public skip = 0

  public range = { start: null, end: null }

  public dialogTitle = 'Relevance Job'

  public opened = false

  public users: Array<{ text: string; value: number }> = [
    { text: '<EMAIL>', value: 1 },
    { text: '<EMAIL>', value: 2 },
    { text: 'Item 3', value: 3 },
  ]

  public gridData: IssueData[] = []

  public aiData: AiData[] = []

  public tabStatus = 0

  public icons = {
    eyeIcon: eyeIcon,
    closeIcon: xIcon,
    checkIcon: checkIcon,
    calendarIcon: calendarIcon,
    downIcon: caretAltDownIcon,
    plusIcon: plusIcon,
    minusIcon: minusIcon,
  }

  public gridDataHistory: GridItemHistory[] = []

  public label: LabelSettings = {}

  public value = 0

  private toDestroy$ = new Subject<void>()

  public downIcon: SVGIcon = chevronDownIcon

  constructor(private dialogService: DialogService) {}

  public ngOnInit(): void {
    this.openDialog()
    this.generateAiDummyData(5)
  }

  private loadHistory(): void {
    this.gridData = []
    this.gridDataHistory = []
    this.generateDataCoding()
  }

  public onSelect(e: any): void {
    this.tabStatus = e.index
    if (e.index === 1) {
      this.loadHistory()
      this.generateDummyData(10)
    }
  }

  public openDialog(): void {
    this.opened = true
  }

  public close(status: string): void {
    this.opened = false
  }

  public generateDataCoding(): void {
    const redactedByOptions = ['Admin', 'John Doe', 'Jane Smith']
    const terms = ['money', 'text message', 'Mobile Number']
    const hitsOptions = [
      'money, Money',
      'Text text message Message TextMessage',
      'Mobile, Number, Mobile Number, mobile',
    ]
    const statuses = ['COMPLETED', 'FAILED', 'IN PROGRESS']
    const jobNames = ['Relevance One', 'AI Two', 'Re three']

    for (let i = 0; i < 20; i++) {
      const redactedBy =
        redactedByOptions[Math.floor(Math.random() * redactedByOptions.length)]
      const termIndex = Math.floor(Math.random() * terms.length)
      const jobName = jobNames[Math.floor(Math.random() * jobNames.length)]
      const term = terms[termIndex]
      const hits = hitsOptions[termIndex]
      const numOfDoc = Math.floor(Math.random() * 100)
      const redactedStatus =
        statuses[Math.floor(Math.random() * statuses.length)]
      const statusCount =
        redactedStatus === 'FAILED' ? Math.floor(Math.random() * 20) + 1 : 0
      const viewAction = redactedStatus === 'COMPLETED'

      this.gridDataHistory.push({
        hashNum: i + 1,
        jobName: jobName,
        redactedBy: redactedBy,
        redactedOn: new Date().toISOString(),
        term: term,
        hits: hits,
        numOfDoc: numOfDoc,
        redactedStatus: redactedStatus,
        statusCount: statusCount,
        viewAction: viewAction,
      })
    }
  }

  public onChange(e: SelectionRange): void {
    this.range = e
  }

  public openSuccessDialog(): void {
    const _dialogRef = this.dialogService.open({
      content: ConfirmationDialogComponent,
      cssClass: 'v-confirmation-dialog v-dialog-save',
      width: '35rem',
    })
  }

  public generateDummyData(rows: number): void {
    for (let i = 1; i <= rows; i++) {
      this.gridData.push({
        Issue: i,
        Relevant: this.getRandomNumber(1, 500), // Random value between 1 and 500
        NonRelevant: this.getRandomNumber(100, 1000), // Random value between 100 and 1000
        NeedFurtherReview: this.getRandomNumber(50, 150), // Random value between 50 and 150
        TechIssue: this.getRandomNumber(1, 20), // Random value between 1 and 20
      })
    }
  }

  public generateAiDummyData(count: number): void {
    for (let i = 1; i <= count; i++) {
      this.aiData.push({
        issueNumber: i,
        issueTitle: `Lorem ipsum dolor sit amet, consectetur adipiscing elit`,
        tag: 'AI-Non-Responsive',
        explanation: `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet, consectetur adipiscing elit`,
      })
    }
  }

  public getRandomNumber(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1)) + min
  }

  public tagMapper(tags: any[]): any[] {
    return tags.length < 2 ? tags : [tags]
  }

  public ngOnDestroy(): void {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }
}
