<div class="t-flex t-flex-col">
  <div class="t-p-4">
    <kendo-dropdownbutton
      kendoTooltip
      title="Tag Group"
      [data]="tagGroupActions"
      class="v-custom-dropdown-tags-btn t-w-[16px] t-max-h-[16px] t-justify-center t-items-center t-bg-[var(--v-custom-sky-blue)] !t-p-[2px] t-rounded-full t-align-top hover:t-bg-[#FFBB12]"
      [popupSettings]="{
        popupClass: 'v-custom-dropdown-case-title ' + getDynamicClass(),
        animate: true
      }">
      <kendo-svgicon
        class="t-w-[12px] t-text-[#FFFFFF] t-h-[12px] t-ml-[1px]"
        [icon]="icons.dotsIcon"></kendo-svgicon>
      <ng-template kendoDropDownButtonItemTemplate let-dropdownItem>
        <span>{{ dropdownItem.text }}</span>
      </ng-template>
    </kendo-dropdownbutton>

    <div class="t-p-4 t-w-[400px]">
      <h2 class="t-font-bold t-text-base t-mb-2 t-text-gray-900">
        AI Log Entry
      </h2>
      <p class="t-text-[#707070] t-mb-4 t-text-sm">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
        tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim
        veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea
        commodo consequat.
      </p>

      <div
        class="t-border t-border-[#E5E5E5] t-border-[2px] t-border-dashed t-rounded-md t-p-4">
        <h3 class="t-font-bold t-text-gray-900 t-text-base t-mb-1">
          Attorney List
        </h3>
        <p class="t-text-[#707070] t-text-sm">John Doe</p>
      </div>
    </div>

    <button kendoButton (click)="openDialog()" class="t-w-48 t-mt-4">
      Open Discovery Ai Review
    </button>
  </div>

  <kendo-dialog
    *ngIf="opened"
    (close)="close('cancel')"
    [height]="'90vh'"
    [minWidth]="250"
    [maxWidth]="1600"
    [width]="'90%'">
    <kendo-dialog-titlebar>
      <div class="t-flex t-w-[65%] t-justify-between">
        <div class="t-block">{{ dialogTitle }}</div>
        <div class="t-block">
          <div
            #appendNotification
            class="v-append-notification-container t-text-sm t-inline-block t-text-xs t-overflow-hidden t-relative t-h-8 t-w-[420px]"></div>
        </div>
      </div>
    </kendo-dialog-titlebar>

    <kendo-tabstrip (tabSelect)="onSelect($event)" class="v-custom-tabstrip">
      <kendo-tabstrip-tab title="Ai Review" [selected]="true">
        <ng-template kendoTabContent>
          <div class="t-w-full">
            <div class="t-flex t-mt-3">
              <div class="t-flex t-gap-2">
                <div class="t-flex t-gap-1">
                  <kendo-textbox
                    placeholder="Job Name"
                    class="t-flex-1"></kendo-textbox>
                  <label class="t-text-sm t-ml-1 t-align-super">
                    <span class="t-text-error">*</span>
                  </label>
                </div>
                <div class="t-flex t-gap-1 t-ml-2">
                  <kendo-dropdownlist
                    [(ngModel)]="selectedPrivilege"
                    [data]="privilegeOptions"
                    class="t-w-56">
                  </kendo-dropdownlist>
                </div>
              </div>
            </div>

            <div class="t-p-4 t-bg-[#F6F6F6] t-mt-3">
              <div class="t-flex t-space-x-6 t-mb-4 t-items-center">
                <!-- TODO: showErrors should be initial 'always' is just for demo purposes -->
                <div
                  showHints="initial"
                  showErrors="always"
                  class="t-gap-2 t-w-full">
                  <div class="t-w-full t-flex t-gap-4">
                    <label class="t-flex t-items-center t-space-x-2">
                      <input type="checkbox" kendoCheckBox />
                      <span>Attorney Client</span>
                    </label>

                    <label class="t-flex t-items-center t-space-x-2">
                      <input type="checkbox" kendoCheckBox />
                      <span>Work Product</span>
                    </label>
                  </div>

                  <div class="t-flex t-w-full t-mt-1">
                    <span class="t-text-error"
                      >Please select one of the option</span
                    >
                  </div>
                </div>
              </div>

              <div class="t-grid t-grid-cols-2 t-gap-4 t-mb-4">
                <kendo-textbox
                  placeholder="Custom Priv type 1 Name"
                  class="t-w-64"></kendo-textbox>
                <kendo-textbox
                  placeholder="Custom Priv type 2 Name"
                  class="t-w-64"></kendo-textbox>

                <div showHints="initial" showErrors="always" class="t-w-full">
                  <kendo-textarea
                    resizable="none"
                    [rows]="4"
                    placeholder="Custom Priv type 1 Definition"></kendo-textarea>
                  <div class="t-flex t-w-full t-mt-1">
                    <span class="t-text-error">Definition is required</span>
                  </div>
                </div>

                <div showHints="initial" showErrors="always" class="t-w-full">
                  <kendo-textarea
                    resizable="none"
                    [rows]="4"
                    placeholder="Custom Priv type 2 Definition"></kendo-textarea>
                  <div class="t-flex t-w-full t-mt-1">
                    <span class="t-text-error"></span>
                  </div>
                </div>
              </div>

              <div class="t-grid t-grid-cols-2 t-gap-4">
                <div showHints="initial" showErrors="always" class="t-w-full">
                  <kendo-textarea
                    placeholder="Attorney List"
                    resizable="none"
                    [rows]="4"></kendo-textarea>
                  <div class="t-flex t-w-full t-mt-1">
                    <span class="t-text-error">Provide Attorney list</span>
                  </div>
                </div>
                <div showHints="initial" showErrors="always" class="t-w-full">
                  <kendo-textarea
                    placeholder="Safe Domains"
                    resizable="none"
                    [rows]="4"></kendo-textarea>
                  <div class="t-flex t-w-full t-mt-1">
                    <span class="t-text-error">Provide Safe Domain names</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ng-template>
      </kendo-tabstrip-tab>
      <kendo-tabstrip-tab title="Status">
        <ng-template kendoTabContent>
          <div class="t-flex t-w-full t-mt-3">
            <div class="t-flex t-flex-col t-gap-4 t-w-full">
              <div
                class="t-flex t-gap-3 t-w-full t-justify-between t-items-center">
                <div class="t-flex t-gap-3">
                  <kendo-multiselect
                    [data]="users"
                    textField="text"
                    valueField="value"
                    [valuePrimitive]="true"
                    [listHeight]="500"
                    [checkboxes]="true"
                    [autoClose]="false"
                    kendoMultiSelectSummaryTag
                    [clearButton]="false"
                    placeholder="All users"
                    class="!t-w-56 v-custom-multiselect-auto-w">
                    <ng-template kendoSuffixTemplate>
                      <kendo-svg-icon
                        [icon]="icons.downIcon"
                        class="t-absolute t-w-[19px] t-h-[19px] t-right-0 t-select-none t-cursor-pointer t-bg-white t-text-[#333333]"
                        fillMode="link"></kendo-svg-icon>
                    </ng-template>
                    <ng-template kendoMultiSelectItemTemplate let-dataItem>
                      <div
                        kendoTooltip
                        title="{{ dataItem.text }}"
                        class="t-overflow-hidden t-text-ellipsis t-whitespace-nowrap t-px-2 t-py-1">
                        {{ dataItem.text }}
                      </div>
                    </ng-template>
                  </kendo-multiselect>
                  <venio-report-date-picker
                    class="t-min-w-[150px] t-w-56 t-self-center" />
                  <kendo-dropdownlist
                    defaultItem="Filter By Status"
                    [data]="listItems"
                    [valuePrimitive]="true"
                    class="t-w-56">
                  </kendo-dropdownlist>

                  <button
                    kendoButton
                    class="v-custom-secondary-button t-p-0"
                    themeColor="secondary"
                    fillMode="outline"
                    data-qa="save-button"
                    #actionGrid2>
                    <span
                      [parentElement]="actionGrid2.element"
                      venioSvgLoader
                      hoverColor="#FFFFFF"
                      color="#9BD2A7"
                      svgUrl="assets/svg/refresh.svg"
                      height="1rem"
                      width="1rem">
                      <kendo-loader size="small"></kendo-loader>
                    </span>
                  </button>
                </div>
              </div>

              <div class="t-flex">
                <div class="t-flex t-mt-4 t-flex-col t-w-full">
                  <kendo-grid
                    class="t-border-l-0 t-border-r-0 t-relative t-overflow-y-auto"
                    [kendoGridBinding]="gridDataHistory"
                    kendoGridSelectBy="hashNum"
                    venioDynamicHeight
                    [isKendoDialog]="true"
                    [sortable]="true"
                    [groupable]="false"
                    [reorderable]="true"
                    [resizable]="true"
                    [filterable]="'menu'"
                    [pageable]="{ type: 'numeric', position: 'top' }">
                    <ng-template kendoPagerTemplate>
                      <div class="t-flex t-gap-2"></div>
                      <kendo-grid-spacer></kendo-grid-spacer>

                      <venio-pagination
                        [disabled]="gridDataHistory?.length === 0"
                        [totalRecords]="gridDataHistory?.length"
                        [pageSize]="pageSize"
                        [showPageJumper]="false"
                        [showPageSize]="true"
                        [showRowNumberInputBox]="true"
                        class="t-px-5 t-block t-py-2">
                      </venio-pagination>
                    </ng-template>
                    <kendo-grid-column
                      field="hashNum"
                      [width]="50"
                      title="#"
                      headerClass="t-text-primary"
                      [filterable]="false">
                      <ng-template kendoGridHeaderTemplate let-column>
                        <span
                          kendoTooltip
                          class="t-text-ellipsis t-overflow-hidden"
                          title="#"
                          >#</span
                        >
                      </ng-template>
                    </kendo-grid-column>
                    <kendo-grid-column
                      field="jobName"
                      [width]="150"
                      title="#"
                      headerClass="t-text-primary"
                      [filterable]="false">
                      <ng-template kendoGridHeaderTemplate let-column>
                        <span
                          kendoTooltip
                          class="t-text-ellipsis t-overflow-hidden"
                          title="#"
                          >Job Name</span
                        >
                      </ng-template>

                      <ng-template kendoGridCellTemplate let-dataItem>
                        <div class="t-cursor-pointer">
                          {{ dataItem.jobName }}
                        </div>
                      </ng-template>
                    </kendo-grid-column>

                    <kendo-grid-column
                      field="jobType"
                      [width]="140"
                      title="Type"
                      headerClass="t-text-primary"
                      [filterable]="true">
                    </kendo-grid-column>

                    <kendo-grid-column
                      field="redactedBy"
                      title="Redacted By & On"
                      headerClass="t-text-primary"
                      [filterable]="false">
                      <ng-template kendoGridHeaderTemplate let-column>
                        <span
                          kendoTooltip
                          class="t-text-ellipsis t-overflow-hidden"
                          title="Created By & On"
                          >Created By & On</span
                        >
                      </ng-template>
                      <ng-template kendoGridCellTemplate let-dataItem>
                        <div class="t-flex t-gap-1 t-flex-col">
                          <div>{{ dataItem.redactedBy }}</div>
                          <div class="t-text-xs">
                            {{
                              dataItem.redactedOn
                                | date : 'MMM dd yyyy HH:mm:ss a'
                            }}
                          </div>
                        </div>
                      </ng-template>
                    </kendo-grid-column>

                    <kendo-grid-column
                      field="numOfDoc"
                      title="Total Documents Submitted"
                      headerClass="t-text-primary"
                      [width]="255"
                      [filterable]="false">
                      <ng-template kendoGridHeaderTemplate let-column>
                        <span
                          kendoTooltip
                          class="t-text-ellipsis t-overflow-hidden"
                          title="Total Documents Submitted"
                          >Total Documents Submitted
                        </span>
                      </ng-template>
                    </kendo-grid-column>

                    <kendo-grid-column
                      field="numOfDoc"
                      [width]="255"
                      title="Total Documents Processed"
                      headerClass="t-text-primary"
                      [filterable]="false">
                      <ng-template kendoGridHeaderTemplate let-column>
                        <span
                          kendoTooltip
                          class="t-text-ellipsis t-overflow-hidden"
                          title="Total Documents Processed "
                          >Total Documents Processed
                        </span>
                      </ng-template>
                    </kendo-grid-column>

                    <kendo-grid-column
                      field="redactedStatus"
                      title="Status"
                      headerClass="t-text-primary"
                      [filterable]="false">
                      <ng-template kendoGridCellTemplate let-dataItem>
                        <div
                          class="t-flex t-items-center t-gap-4 t-justify-between">
                          <div
                            class="t-font-medium"
                            [ngClass]="{
                              't-text-success':
                                dataItem.redactedStatus === 'COMPLETED',
                              't-text-error':
                                dataItem.redactedStatus === 'FAILED',
                              't-text-[#FFBB12]':
                                dataItem.redactedStatus === 'IN PROGRESS'
                            }">
                            <span
                              *ngIf="dataItem.redactedStatus === 'FAILED'"
                              >{{ dataItem.statusCount }}</span
                            >
                            {{ dataItem.redactedStatus }}
                          </div>

                          <div
                            class="t-flex t-items-center t-w-16 t-justify-end t-gap-2">
                            <span
                              *ngIf="dataItem.viewAction"
                              kendoPopoverAnchor
                              [popover]="myPopover"
                              class="hover:t-cursor-pointer hover:t-text-[var(--kendo-custom-secondary-100)] t-flex t-w-full">
                              <kendo-svg-icon
                                class="hover:t-text-[#1EBADC]"
                                [icon]="icons.eyeIcon"></kendo-svg-icon>
                            </span>

                            <span
                              *ngIf="dataItem.redactedStatus === 'IN PROGRESS'"
                              class="t-text-[#FFBB12] t-flex t-w-full">
                              40%
                            </span>
                          </div>
                        </div>
                      </ng-template>
                    </kendo-grid-column>
                  </kendo-grid>

                  <kendo-popover #myPopover position="left" [width]="610">
                    <ng-template kendoPopoverBodyTemplate>
                      <div class="t-flex t-mt-2">
                        <kendo-grid
                          [data]="gridData"
                          class="t-w-full v-hide-scrollbar v-discovery-status-grid"
                          [height]="350">
                          <kendo-grid-column
                            [width]="180"
                            headerClass="t-text-primary"
                            [class]="'t-overflow-visible'"
                            field="Entry"
                            title="Total Documents">
                            <ng-template
                              kendoGridCellTemplate
                              let-column
                              let-rowIndex="rowIndex">
                              <span
                                class="t-text-[var(--v-custom-sky-blue)] t-font-semibold t-cursor-pointer"
                                (click)="
                                  togglePopover($event, rowIndex, column.Entry)
                                ">
                                {{ column.Entry }}
                              </span>
                              <div
                                class="t-relative t-overflow-visible v-custom-popup-grid"
                                *ngIf="
                                  openPopoverKey ===
                                  rowIndex + '-' + column.Entry
                                "
                                (click)="$event.stopPropagation()">
                                <div
                                  class="t-absolute t-rounded-lg t-p-4 t-w-[470px] t-left-[30px] t-bg-[#F5F5F5] t-shadow-[0_0px_7px_-1px_rgba(0,0,0,0.3)] t-whitespace-normal t-break-words"
                                  [ngClass]="
                                    rowIndex === 0
                                      ? 't-top-[-25px]'
                                      : 't-top-[-42px]'
                                  ">
                                  <div
                                    class="t-absolute t-left-[-7px] t-w-4 t-h-4 t-bg-[#F5F5F5] t-shadow-[-2px_3px_4px_-1px_rgba(0,0,0,0.3)] t-rotate-45"
                                    [ngClass]="
                                      rowIndex === 0
                                        ? 't-top-[8px]'
                                        : 't-top-[23px]'
                                    "></div>

                                  <h3
                                    class="t-font-bold t-text-base t-mb-3 t-pl-3">
                                    Selected options
                                  </h3>

                                  <table
                                    class="t-w-full t-border-collapse !t-border-0">
                                    <tbody>
                                      <tr>
                                        <td
                                          class="t-font-semibold t-align-top t-pr-4 t-w-[116px]">
                                          Full Name
                                        </td>
                                        <td
                                          class="!t-border-l-[2px] !t-border-dashed !t-border-[#AAD297] t-w-[1px] t-align-top t-px-3"></td>

                                        <td
                                          class="t-text-[#B4B4B4] t-align-top t-pl-2">
                                          Custom PII Type
                                          <!-- {{ rowIndex }} , Column: {{ column.Entry }} -->
                                        </td>
                                      </tr>

                                      <tr>
                                        <td
                                          class="t-font-semibold t-align-top t-pr-4 t-w-[116px]">
                                          Date of Birth
                                        </td>
                                        <td
                                          class="!t-border-l-[2px] !t-border-dashed !t-border-[#AAD297] t-w-[1px] t-align-top t-px-3"></td>
                                        <td
                                          class="t-text-gray-500 t-align-top t-pl-2">
                                          Some Text here
                                        </td>
                                      </tr>

                                      <tr>
                                        <td
                                          class="t-font-semibold t-align-top t-pr-4 t-w-[116px]">
                                          Place of Birth
                                        </td>
                                        <td
                                          class="!t-border-l-[2px] !t-border-dashed !t-border-[#AAD297] t-w-[1px] t-align-top t-px-3"></td>
                                        <td
                                          class="t-text-[#B4B4B4] t-align-top t-pl-2">
                                          Custom PII Definition
                                        </td>
                                      </tr>
                                      <tr>
                                        <td
                                          class="t-font-semibold t-align-top t-pr-4 t-w-[116px]">
                                          Password Number
                                        </td>
                                        <td
                                          class="!t-border-l-[2px] !t-border-dashed !t-border-[#AAD297] t-w-[1px] t-align-top t-px-3"></td>
                                        <td
                                          class="t-font-semilbold t-align-top t-pl-2">
                                          Lorem ipsum dolor sit amet,
                                          consectetur adipiscing
                                        </td>
                                      </tr>

                                      <tr>
                                        <td
                                          class="t-font-semibold t-align-top t-pr-4 t-w-[116px]">
                                          Tax ID
                                        </td>
                                        <td
                                          class="!t-border-l-[2px] !t-border-dashed !t-border-[#AAD297] t-w-[1px] t-align-top t-px-3"></td>
                                        <td
                                          class="t-font-semibold t-align-top t-pl-2">
                                          Lorem ipsum dolor sit amet,
                                          consectetur adipiscing elit
                                        </td>
                                      </tr>
                                    </tbody>
                                  </table>
                                </div>
                              </div>
                            </ng-template>
                          </kendo-grid-column>
                          <kendo-grid-column
                            [width]="100"
                            headerClass="t-text-primary"
                            field="Privilege"
                            title="Processed">
                          </kendo-grid-column>
                          <!--  <kendo-grid-column
                            [width]="180"
                            headerClass="t-text-primary"
                            field="AttorneyClient"
                            title="AttorneyClient">
                          </kendo-grid-column>
                          <kendo-grid-column
                            [width]="140"
                            headerClass="t-text-primary"
                            field="WorkProduct"
                            title="Work Product">
                          </kendo-grid-column>
                          <kendo-grid-column
                            [width]="150"
                            headerClass="t-text-primary"
                            field="CustomPriv1"
                            title="Custom Priv 1">
                          </kendo-grid-column>
                          <kendo-grid-column
                            [width]="150"
                            headerClass="t-text-primary"
                            field="CustomPriv2"
                            title="Custom Priv 2">
                          </kendo-grid-column>
                          <kendo-grid-column
                            [width]="180"
                            headerClass="t-text-primary"
                            field="PotentialPrivilege"
                            title="Potential-Privilege">
                          </kendo-grid-column>
                          <kendo-grid-column
                            [width]="140"
                            headerClass="t-text-primary"
                            field="NonPrivilege"
                            title="Non-Privilege">
                          </kendo-grid-column> -->
                          <kendo-grid-column
                            [width]="120"
                            headerClass="t-text-primary"
                            field="TechIssue"
                            title="Tech Issue">
                          </kendo-grid-column>
                        </kendo-grid>
                      </div>
                    </ng-template>
                  </kendo-popover>
                </div>
              </div>
            </div>
          </div>
        </ng-template>
      </kendo-tabstrip-tab>
    </kendo-tabstrip>

    <kendo-dialog-actions>
      <div class="t-flex t-gap-4 t-justify-end">
        <button
          kendoButton
          (click)="close('no')"
          class="v-custom-secondary-button"
          themeColor="secondary"
          fillMode="outline"
          data-qa="save-button">
          SUBMIT
        </button>
        <!-- <button
            kendoButton
            (click)="close('yes')"
            themeColor="dark"
            fillMode="outline"
            data-qa="cancel-button">
            CLOSE
          </button> -->
      </div>
    </kendo-dialog-actions>
  </kendo-dialog>
</div>
