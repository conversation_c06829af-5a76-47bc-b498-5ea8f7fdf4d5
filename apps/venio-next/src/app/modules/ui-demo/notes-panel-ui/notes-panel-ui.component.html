<div
  class="t-flex t-w-full t-min-h-[370px] t-flex-col t-gap-1 t-relative t-overflow-hidden">
  <div
    class="t-flex t-sticky t-justify-end t-mt-2 t-top-0 t-bg-white t-z-[1] t-drop-shadow-sm t-pr-2">
    <button
      kendoButton
      #fixedHeader
      kendoTooltip
      [title]="commonActionTypes.NEW_NOTE | titlecase"
      class="!t-p-[0.2rem]"
      (click)="onTagGroupAction(commonActionTypes.NEW_NOTE)"
      fillMode="clear"
      size="none">
      <span
        [parentElement]="fixedHeader.element"
        venioSvgLoader
        [hoverColor]="'#FFBB12'"
        color="#979797"
        [svgUrl]="'assets/svg/icon-new-note-ui.svg'"
        height="1.2rem"
        width="1.2rem">
        <!-- loader if required -->
        <!-- <kendo-loader size="small"></kendo-loader> -->
      </span>
    </button>
  </div>
  <kendo-treeview
    [nodes]="chatMessages"
    [children]="getChildren"
    textField="text"
    kendoTreeViewExpandable
    [hasChildren]="hasChildren"
    class="v-note-thread-tree t-w-full"
    (expand)="handleScroll($event)"
    (collapse)="handleScroll($event)">
    <ng-template kendoTreeViewNodeTemplate let-dataItem>
      <div
        class="t-flex t-border t-border-1 t-border-[#dbdbdb] t-flex-col t-p-2 t-rounded-lg t-gap2 t-w-full">
        <div class="t-flex t-gap-2 t-items-center t-justify-between">
          <div class="t-flex t-gap-2 t-items-center">
            <span
              class="t-font-semibold t-max-w-[80px] t-truncate"
              kendoTooltip
              [title]="dataItem.author"
              >{{ dataItem.author }}</span
            >
            <span class="t-text-xs">{{
              dataItem.timestamp | date : 'short'
            }}</span>
          </div>
          <div class="t-flex">
            <kendo-buttongroup kendoTooltip>
              @for (icon of tagGroupSvgIcons; track icon.actionType) {
              <button
                kendoButton
                #actionGrid
                [title]="icon.actionType | titlecase"
                class="!t-p-[0.2rem]"
                (click)="onTagGroupAction(icon.actionType)"
                fillMode="clear"
                size="none">
                <span
                  [parentElement]="actionGrid.element"
                  venioSvgLoader
                  [hoverColor]="
                    icon.actionType === commonActionTypes.DELETE
                      ? '#ED7425'
                      : '#FFBB12'
                  "
                  color="#979797"
                  [svgUrl]="icon.iconPath"
                  [height]="
                    icon.actionType === commonActionTypes.DELETE
                      ? '0.9rem'
                      : icon.actionType === commonActionTypes.EDIT
                      ? '1rem'
                      : '1.2rem'
                  "
                  [width]="
                    icon.actionType === commonActionTypes.DELETE
                      ? '0.9rem'
                      : icon.actionType === commonActionTypes.EDIT
                      ? '1rem'
                      : '1.2rem'
                  ">
                  <!-- loader if required -->
                  <!-- <kendo-loader size="small"></kendo-loader> -->
                </span>
              </button>
              }
            </kendo-buttongroup>
          </div>
        </div>
        <div
          class="t-max-w-[100%] t-truncate"
          kendoTooltip
          [title]="dataItem.text">
          {{ dataItem.text }}
        </div>
        <!-- Insert expand/collapse icons or custom logic if needed -->
      </div>
      <!-- Nested replies will automatically be taken care of by the TreeView -->
    </ng-template>
  </kendo-treeview>

  <div
    *ngIf="!chatMessages.length"
    class="t-grid t-text-center t-place-content-center t-text-[#979797] t-min-h-[125px]">
    No records found.
    <div class="t-flex t-text-sm t-mt-1">
      (Click on the + icon to add a new note.)
    </div>
  </div>

  <div
    class="t-flex t-absolute t-z-[2] t-bg-white t-flex-col t-p-4 t-gap-3 t-w-full t-bottom-0 t-shadow-[0_0px_32px_-2px_rgba(0,0,0,0.4)]"
    *ngIf="showNewNotePanel">
    <div class="t-flex t-flex-col t-gap-2">
      <!-- Change the placeholder logic for the textarea in the development as Edit note won't be required however we need to show a Add new note for the new note-->
      <kendo-textarea
        [rows]="3"
        resizable="vertical"
        [placeholder]="
          !showEditNotePanel
            ? 'Add a new note...'
            : 'Message text for note will be shown here and you can add more'
        "></kendo-textarea>

      <kendo-dropdownlist
        *ngIf="!showEditNotePanel"
        defaultItem="Select item..."
        [data]="listItems"
        [valuePrimitive]="true">
      </kendo-dropdownlist>
    </div>

    <div class="t-flex t-justify-end t-gap-3">
      <kendo-buttongroup class="t-flex t-gap-3">
        <button
          kendoButton
          fillMode="clear"
          [toggleable]="true"
          [svgIcon]="checkIcon"
          class="t-bg-[#BAE36E3D] t-text-[#88B13F]"></button>
        <button
          kendoButton
          fillMode="clear"
          [toggleable]="true"
          [svgIcon]="xIcon"
          class="t-bg-[#FF5F521A] t-text-[#EC3737]"
          (click)="showNewNotePanel = false"></button>
      </kendo-buttongroup>
    </div>
  </div>
</div>
