import { ComponentFixture, TestBed } from '@angular/core/testing'
import { NotesPanelUiComponent } from './notes-panel-ui.component'
import { CommonModule } from '@angular/common'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('NotesPanelUiComponent', () => {
  let component: NotesPanelUiComponent
  let fixture: ComponentFixture<NotesPanelUiComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [NotesPanelUiComponent, CommonModule, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(NotesPanelUiComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
