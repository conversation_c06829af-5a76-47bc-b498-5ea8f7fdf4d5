import { ComponentFixture, TestBed } from '@angular/core/testing'
import { TranscriptUiComponent } from './transcript-ui.component'
import { NoopAnimationsModule } from '@angular/platform-browser/animations'
import { provideHttpClient } from '@angular/common/http'
import { provideHttpClientTesting } from '@angular/common/http/testing'

describe('TranscriptUiComponent', () => {
  let component: TranscriptUiComponent
  let fixture: ComponentFixture<TranscriptUiComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TranscriptUiComponent, NoopAnimationsModule],
      providers: [provideHttpClient(), provideHttpClientTesting()],
    }).compileComponents()

    fixture = TestBed.createComponent(TranscriptUiComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
