<!-- template for #transcriptViewer, could be moved to separate component -->
<div class="t-flex t-w-full t-h-full">
  <div
    class="t-flex t-flex-col t-w-16 t-transition-all t-border t-border-[#ccc] t-border-l-0 t-border-b-0 t-border-t-0 t-border-r-1 t-h-max t-min-h-full"
    [ngClass]="{ '!t-w-[230px]': menuState }">
    <div
      class="t-flex t-p-4 t-border t-border-[#cccccc] t-border-r-0 t-border-l-0 t-border-t-0 t-border-b-1">
      <button
        kendoButton
        fillMode="clear"
        class="t-p-0 t-bg-white"
        (click)="this.toggleMenuState()">
        <img src="assets/svg/icon-slide-menu-left.svg" alt="Open Menu" />
      </button>
    </div>

    <div class="t-flex t-flex-col" *ngIf="menuState">
      <kendo-expansionpanel
        *ngFor="let item of items; trackBy: trackById; index as i"
        [title]="item.text"
        [expanded]="item.expanded"
        class="v-custom-expansionpanel-trans t-flex t-w-full t-items-center t-mt-0"
        (action)="onAction(i)">
        <ng-template kendoExpansionPanelTitleDirective>
          <div
            class="t-flex t-w-full t-justify-between t-px-4 t-py-2 t-border t-border-[#cccccc] t-border-t-0 t-border-l-0 t-border-r-0 t-border-b-1 t-items-center t-text-[#1EBADC] t-font-medium">
            <span>{{ item.text }}</span>
            <kendo-svgicon
              [icon]="icons.chevronRightIcon"
              class="t-text-[#000000]"
              [ngClass]="{ 't-rotate-90': item.expanded }"></kendo-svgicon>
          </div>
        </ng-template>
        <div class="content t-flex">
          <ng-container
            *ngTemplateOutlet="getTemplate(item.templateName)"></ng-container>
        </div>
      </kendo-expansionpanel>
    </div>
  </div>

  <div class="t-flex t-flex-1 t-w-full">
    <div
      class="t-flex t-w-full t-justify-between t-p-2 t-px-4 t-h-[53px] t-border-b t-border-b-[#cccccc]">
      <div class="t-flex t-gap-2 t-h-[34px]">
        <kendo-dropdownlist
          defaultItem="Roboto"
          [data]="listItems"
          [valuePrimitive]="true"
          class="t-w-52">
        </kendo-dropdownlist>

        <kendo-dropdownlist
          defaultItem="14"
          [data]="sizeItems"
          [valuePrimitive]="true"
          class="t-w-20">
        </kendo-dropdownlist>
        <kendo-dropdownlist
          defaultItem="Normal"
          [data]="weightItems"
          [valuePrimitive]="true"
          class="t-w-28">
        </kendo-dropdownlist>
      </div>

      <div class="t-block t-relative">
        <kendo-dropdownbutton
          [data]="btnDropdown"
          fillMode="clear"
          imageUrl="assets/svg/icon-material-graph-box.svg">
        </kendo-dropdownbutton>
      </div>
    </div>
    <!-- FOR DEMO: context menu for add note, highlight & linked documents-->
    <div class="t-flex t-p-4" *ngIf="items[1].expanded">
      <div #target>Sample text right click to show context menu</div>

      <kendo-contextmenu
        [target]="target"
        [items]="contextMenuItems"
        (select)="onContextMenuSelect($event)">
        <kendo-menu>
          <kendo-menu-item
            *ngFor="let item of contextMenuItems"
            [text]="item.text"
            [svgIcon]="item.svgIcon">
          </kendo-menu-item>
        </kendo-menu>
      </kendo-contextmenu>
    </div>
  </div>
</div>

<ng-template #transcriptContent>
  <div class="t-flex t-flex-col t-flex-1 t-w-full">
    <kendo-listview
      [data]="items"
      class="t-border-0 v-custom-listview-transcript">
      <ng-template kendoListViewItemTemplate let-dataItem="dataItem">
        <div
          class="t-flex t-px-8 t-py-2 hover:t-bg-[#F1EEEE] hover:t-cursor-pointer">
          {{ dataItem.text }}
        </div>
      </ng-template>
    </kendo-listview>
  </div>
</ng-template>

<ng-template #addNote>
  <div class="t-flex t-flex-col t-flex-1 t-w-full">
    <ng-container *ngIf="noteItems.length > 0; else noDataTemplate">
      <kendo-listview
        [data]="noteItems"
        class="t-border-0 v-custom-listview-transcript">
        <ng-template kendoListViewItemTemplate let-dataItem="dataItem">
          <div
            class="t-flex t-px-8 t-py-2 hover:t-bg-[#F1EEEE] hover:t-cursor-pointer"
            (click)="onItemNoteClick(dataItem)">
            {{ dataItem.text }}
          </div>
        </ng-template>
      </kendo-listview>
    </ng-container>
  </div>
</ng-template>

<ng-template #linkedDocuments>
  <div class="t-flex t-flex-col t-flex-1 t-w-full">
    <ng-container *ngIf="items.length > 0; else noDataTemplate">
      <kendo-listview
        [data]="items"
        class="t-border-0 v-custom-listview-transcript">
        <ng-template
          kendoListViewItemTemplate
          let-dataItem="dataItem"
          let-i="index">
          <div
            class="t-flex t-px-8 t-py-2 hover:t-bg-[#F1EEEE] hover:t-cursor-pointer t-group t-relative">
            <span class="t-flex-1 t-mr-2 t-max-w-[90%]">
              {{ i }}:{{ i + 23 }} {{ dataItem.text }}
            </span>

            <button
              kendoButton
              fillMode="clear"
              class="t-p-0 t-hidden t-absolute t-right-2 t-top-0 t-w-10 t-h-[2.2rem] t-place-content-center group-hover:!t-grid">
              <span
                venioSvgLoader
                svgUrl="assets/svg/Icon-material-delete.svg"
                height="0.9rem"
                width="0.8rem">
              </span>
            </button>
          </div>
        </ng-template>
      </kendo-listview>
    </ng-container>
  </div>
</ng-template>

<ng-template #wordWheel>
  <div class="t-flex t-flex-col t-flex-1 t-w-full">
    <div class="t-flex t-pt-2">
      <!-- kendo textbox with placeholder -->
      <kendo-textbox
        placeholder="Search"
        class="t-w-full t-mx-4 t-my-2"></kendo-textbox>
    </div>

    <div class="t-flex t-flex-col t-py-2 t-px-4 t-font-medium">
      <ng-container *ngIf="wheelItems.length > 0; else noDataTemplate">
        <kendo-chiplist size="small">
          <kendo-chip
            *ngFor="let item of wheelItems; trackBy: trackById"
            [label]="item.label">
          </kendo-chip>
        </kendo-chiplist>
      </ng-container>
    </div>
  </div>
</ng-template>

<ng-template #highlightDocs>
  <div class="t-flex t-flex-col t-flex-1 t-w-full">
    <ng-container *ngIf="items.length > 0; else noDataTemplate">
      <kendo-listview
        [data]="items"
        class="t-border-0 v-custom-listview-transcript">
        <ng-template kendoListViewItemTemplate let-dataItem="dataItem">
          <div
            class="t-flex t-px-8 t-py-2 hover:t-bg-[#F1EEEE] hover:t-cursor-pointer">
            {{ dataItem.text }}
          </div>
        </ng-template>
      </kendo-listview>
    </ng-container>
  </div>
</ng-template>

<!-- No data template -->
<ng-template #noDataTemplate>
  <div class="t-grid t-p-4 t-place-content-center t-text-[#cccccc]">
    No data available
  </div>
</ng-template>

<!-- Add new Note dialog -->
<kendo-dialog
  *ngIf="noteDialogOpenStatus"
  (close)="close('cancel')"
  [maxHeight]="550"
  [height]="'90vh'"
  [minWidth]="250"
  [width]="'55%'">
  <kendo-dialog-titlebar>
    <div>
      {{ dialogTitle }}
    </div>
  </kendo-dialog-titlebar>

  <div class="t-w-full t-block t-h-full">
    <ng-container
      *ngComponentOutlet="notesViewerComponent | async"></ng-container>
  </div>
</kendo-dialog>
