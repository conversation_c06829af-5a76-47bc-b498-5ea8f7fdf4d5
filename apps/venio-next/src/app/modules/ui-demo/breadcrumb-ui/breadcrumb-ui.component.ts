import { ChangeDetectionStrategy, Component } from '@angular/core'
import { CommonModule } from '@angular/common'
import { LayoutModule } from '@progress/kendo-angular-layout'
import { ButtonModule } from '@progress/kendo-angular-buttons'
import { InputsModule, SwitchModule } from '@progress/kendo-angular-inputs'
import { DropDownsModule } from '@progress/kendo-angular-dropdowns'
import {
  SVGIcon,
  plusIcon,
  searchIcon,
  xCircleIcon,
} from '@progress/kendo-svg-icons'
import { FormsModule } from '@angular/forms'
import { IconsModule } from '@progress/kendo-angular-icons'
import { LabelModule } from '@progress/kendo-angular-label'

@Component({
  selector: 'venio-breadcrumb-ui',
  standalone: true,
  imports: [
    CommonModule,
    LayoutModule,
    InputsModule,
    DropDownsModule,
    SwitchModule,
    FormsModule,
    IconsModule,
    LabelModule,
    ButtonModule,
  ],
  templateUrl: './breadcrumb-ui.component.html',
  styleUrl: './breadcrumb-ui.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BreadcrumbUiComponent {
  public listItems: Array<string> = ['Condition']

  public selectedValues = 'Condition'

  public selectedCondition = 'AND'

  public plusSvgIcon: SVGIcon = plusIcon

  public searchIconSvg: SVGIcon = searchIcon

  public xCircleIcon: SVGIcon = xCircleIcon

  // On condition click
  public addConditions(): void {
    console.log('open')
  }

  // On search click
  public onSearch(): void {
    console.log('search')
  }
}
