<div class="t-flex t-max-w-[350px]">
  <!-- Copy the breadcrumb UI from here -->

  <div class="t-flex t-w-full t-flex-col t-gap-2 t-p-2 t-items-center">
    <!-- we may remove the  t-py-3 t-border-y t-border-[#dbdbdb] if its already there in the golden layout -->
    <div
      class="t-flex t-w-full t-items-center t-gap-2 t-py-3 t-border-y t-border-[#dbdbdb]">
      <!-- place holder for grab icon/button-->
      <!-- put it above and remove t-ml-[10%] from combobox -->
      <kendo-combobox
        [data]="listItems"
        [clearButton]="false"
        class="t-cursor-pointer t-flex-grow t-w-[54%] t-ml-[10%]"
        [(ngModel)]="selectedValues"
        [readonly]="true"
        (click)="addConditions()">
        <ng-template kendoPrefixTemplate>
          <button kendoButton fillMode="flat" [svgIcon]="plusSvgIcon"></button>
        </ng-template>
      </kendo-combobox>

      <button
        kendoButton
        [svgIcon]="searchIconSvg"
        (click)="onSearch()"></button>
      <kendo-label
        class="t-w-[37%] t-justify-between t-flex t-items-center t-text-xs"
        text="Auto Run"
        ><kendo-switch size="large"></kendo-switch
      ></kendo-label>
    </div>

    <!-- Cards and Conditions-->
    <div class="t-flex t-flex-col t-gap-2 t-items-center">
      <kendo-card
        class="t-w-full t-shadow-md t-rounded-md t-overflow-hidden"
        width="100%">
        <kendo-card-header class="t-pb-2 t-flex t-items-center">
          <div class="t-flex t-justify-between t-items-center t-w-full">
            <h4 class="t-font-bold t-text-sm t-uppercase t-tracking-widest">
              Advanced Search
            </h4>
            <div class="t-flex t-items-center t-gap-1.5">
              <input type="checkbox" #notification kendoCheckBox />
              <button
                kendoButton
                [svgIcon]="xCircleIcon"
                [size]="'large'"
                fillMode="clear"
                class="t-p-0 t-text-[#ED7425]"></button>
            </div>
          </div>
        </kendo-card-header>

        <kendo-card-body class="t-pt-2">
          <div class="t-flex t-text-[#979797] t-tracking-wider">
            (Lorem Ipsum = Ipsum) AND (Lorem Ipsum = Ipsum) AND (Lorem Ipsum =
            Ipsum) AND (Lorem Ipsum = Ipsum)
          </div>
        </kendo-card-body>
      </kendo-card>

      <kendo-dropdownlist
        [data]="['AND', 'OR']"
        [(ngModel)]="selectedCondition"
        class="t-w-24 t-mt-2"></kendo-dropdownlist>
      <kendo-card
        class="t-w-full t-shadow-md t-rounded-md t-overflow-hidden"
        width="100%">
        <kendo-card-header class="t-pb-2 t-flex t-items-center">
          <div class="t-flex t-justify-between t-items-center t-w-full">
            <h4 class="t-font-bold t-text-sm t-uppercase t-tracking-widest">
              Advanced Search
            </h4>
            <div class="t-flex t-items-center t-gap-1.5">
              <input type="checkbox" #notification kendoCheckBox />
              <button
                kendoButton
                [svgIcon]="xCircleIcon"
                [size]="'large'"
                fillMode="clear"
                class="t-p-0 t-text-[#ED7425]"></button>
            </div>
          </div>
        </kendo-card-header>

        <kendo-card-body class="t-pt-2">
          <div class="t-flex t-text-[#979797] t-tracking-wider">
            (Lorem Ipsum = Ipsum) AND (Lorem Ipsum = Ipsum) AND (Lorem Ipsum =
            Ipsum) AND (Lorem Ipsum = Ipsum)
          </div>
        </kendo-card-body>
      </kendo-card>
    </div>

    <!-- clear all buttons -->
    <button kendoButton fillMode="outline" class="t-mt-2">
      Clear All Conditions
    </button>
  </div>
  <!-- end -->
</div>
