import { Injectable } from '@angular/core'
import { Actions, createEffect, ofType } from '@ngrx/effects'
import { fetch } from '@ngrx/router-store/data-persistence'

import * as RootActions from './root.actions'

@Injectable()
export class RootEffects {
  public init$ = createEffect(() =>
    this.actions$.pipe(
      ofType(RootActions.initRoot),
      fetch({
        run: (action) => {
          // Your custom service 'load' logic goes here. For now just return a success action...
          return RootActions.loadRootSuccess({ root: [] })
        },
        onError: (action, error) => {
          console.error('Error', error)
          return RootActions.loadRootFailure({ error })
        },
      })
    )
  )

  constructor(private readonly actions$: Actions) {}
}
