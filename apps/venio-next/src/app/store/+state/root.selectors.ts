import { createFeatureSelector, createSelector } from '@ngrx/store'
import { rootAdapter, RootState, ROOT_FEATURE_KEY } from './root.reducer'

// Lookup the 'Root' feature state managed by NgRx
export const getRootState = createFeatureSelector<RootState>(ROOT_FEATURE_KEY)

const { selectAll, selectEntities } = rootAdapter.getSelectors()

export const getRootLoaded = createSelector(
  getRootState,
  (state: RootState) => state.loaded
)

export const getRootError = createSelector(
  getRootState,
  (state: RootState) => state.error
)

export const getAllRoot = createSelector(getRootState, (state: RootState) =>
  selectAll(state)
)

export const getRootEntities = createSelector(
  getRootState,
  (state: RootState) => selectEntities(state)
)

export const getSelectedId = createSelector(
  getRootState,
  (state: RootState) => state.selectedId
)

export const getSelected = createSelector(
  getRootEntities,
  getSelectedId,
  (entities, selectedId) => (selectedId ? entities[selectedId] : undefined)
)
