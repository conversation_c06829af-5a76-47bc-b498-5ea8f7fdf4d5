import { createEntityAdapter, Entity<PERSON>dapter, EntityState } from '@ngrx/entity'
import { Action, createReducer, on } from '@ngrx/store'

import * as RootActions from './root.actions'
import { RootEntity } from './root.models'

export const ROOT_FEATURE_KEY = 'root'

export interface RootState extends EntityState<RootEntity> {
  selectedId?: string | number // which Root record has been selected
  loaded: boolean // has the Root list been loaded
  error?: string | null // last known error (if any)
}

export interface RootPartialState {
  readonly [ROOT_FEATURE_KEY]: RootState
}

export const rootAdapter: EntityAdapter<RootEntity> =
  createEntityAdapter<RootEntity>()

export const initialRootState: RootState = rootAdapter.getInitialState({
  // set initial required properties
  loaded: false,
})

const reducer = createReducer(
  initialRootState,
  on(RootActions.initRoot, (state) => ({
    ...state,
    loaded: false,
    error: null,
  })),
  on(RootActions.loadRootSuccess, (state, { root }) =>
    rootAdapter.setAll(root, { ...state, loaded: true })
  ),
  on(RootActions.loadRootFailure, (state, { error }) => ({ ...state, error }))
)

export function rootReducer(
  state: RootState | undefined,
  action: Action
): RootState {
  return reducer(state, action)
}
