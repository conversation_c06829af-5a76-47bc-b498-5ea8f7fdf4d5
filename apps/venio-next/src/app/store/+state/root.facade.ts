import { Injectable } from '@angular/core'
import { select, Store } from '@ngrx/store'

import * as RootActions from './root.actions'
import * as RootSelectors from './root.selectors'

@Injectable()
export class RootFacade {
  /**
   * Combine pieces of state using createSelector,
   * and expose them as observables through the facade.
   */
  public loaded$ = this.store.pipe(select(RootSelectors.getRootLoaded))

  public allRoot$ = this.store.pipe(select(RootSelectors.getAllRoot))

  public selectedRoot$ = this.store.pipe(select(RootSelectors.getSelected))

  constructor(private readonly store: Store) {}

  /*
   * Use the initialization action to perform one
   * or more tasks in your Effects.
   */
  public init(): void {
    this.store.dispatch(RootActions.initRoot())
  }
}
