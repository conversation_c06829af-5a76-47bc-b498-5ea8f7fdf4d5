import { NgModule } from '@angular/core'
import { EffectsModule } from '@ngrx/effects'
import { StoreModule } from '@ngrx/store'
import { StoreDevtoolsModule } from '@ngrx/store-devtools'
import { environment } from '@venio/shared/environments'
import { RootEffects } from './+state/root.effects'
import { RootFacade } from './+state/root.facade'
import * as fromRoot from './+state/root.reducer'
import {
  DataAccessCommonModule,
  UserEffects,
  USERS_VIEW_FEATURE_KEY,
  usersReducer,
} from '@venio/data-access/common'
import {
  STARTUPS_FEATURE_KEY,
  StartupsEffects,
  startupsReducer,
} from '@venio/data-access/review'
import { ReportsModule } from '@venio/data-access/reports'
import { DataAccessAiModule } from '@venio/data-access/ai'
import {
  IFRAME_MESSENGER_FEATURE_KEY,
  IframeMessengerEffects,
  iframeMessengerReducer,
} from '@venio/data-access/iframe-messenger'
@NgModule({
  imports: [
    StoreModule.forRoot(
      {},
      {
        metaReducers: !environment.production ? [] : [],
        runtimeChecks: {
          strictActionImmutability: true,
          strictStateImmutability: true,
        },
      }
    ),
    EffectsModule.forRoot([
      RootEffects,
      UserEffects,
      StartupsEffects,
      IframeMessengerEffects,
    ]),
    !environment.production
      ? StoreDevtoolsModule.instrument({
          connectInZone: true,
        })
      : [],
    StoreModule.forFeature(fromRoot.ROOT_FEATURE_KEY, fromRoot.rootReducer),
    StoreModule.forFeature(USERS_VIEW_FEATURE_KEY, usersReducer),
    StoreModule.forFeature(STARTUPS_FEATURE_KEY, startupsReducer),
    StoreModule.forFeature(
      IFRAME_MESSENGER_FEATURE_KEY,
      iframeMessengerReducer
    ),
    ReportsModule,
    DataAccessCommonModule,
    DataAccessAiModule,
  ],
  exports: [StoreModule, EffectsModule],
  providers: [RootFacade],
})
export class RootStoreModule {}
